package com.ruifox.collect.system;

import org.springframework.core.env.Environment;

/**
 * 提供静态方法来获取Spring框架环境中配置信息(如application.yml)的工具类
 */
public class ConfigUtil {

    private static Environment environment = ApplicationContextProvider.getApplicationContext().getEnvironment();


    /**
     * 暴露环境对象
     * @param key application.yml中定义的key
     * @return 返回application.yml中的key对应的值
     */
    public static String getProperties(String key){
        return environment.getProperty(key);
    }

}