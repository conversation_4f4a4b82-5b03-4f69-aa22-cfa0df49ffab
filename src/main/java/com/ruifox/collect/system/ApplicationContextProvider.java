package com.ruifox.collect.system;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Description: 该类用于获取IOC容器以及IOC容器中的bean
 **/
@Component
public class ApplicationContextProvider implements ApplicationContextAware {

    private static ApplicationContext context;

    /**
     * 设置IOC容器
     *
     * @param applicationContext 初始化好的上下文
     * @throws BeansException bean相关异常
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    /**
     * 暴露IOC容器供外部使用
     *
     * @return 返回ApplicationContext上下文
     */
    public static ApplicationContext getApplicationContext() {
        return context;
    }

    /**
     * 根据bean类型从IOC容器中获取bean
     *
     * @param clazz bean的字节码对象
     * @param <T>   由于待获取的bean的类型是不确定的，所有这里使用泛型
     * @return 返回IOC容器中的bean对象
     */
    public static <T> T getBeanByType(Class<T> clazz) {
        return context.getBean(clazz);
    }

    /**
     * 通过bean名称和类型进行获取，主要用于多态
     *
     * @param beanName bean名称
     * @param clazz    bean 类型
     * @param <T>      由于待获取的bean的类型是不确定的，所有这里使用泛型
     * @return 返回bean
     */
    public static <T> T getBean(String beanName, Class<T> clazz) {
        return context.getBean(beanName, clazz);
    }

    /**
     * 根据指定类型从IOC容器中获取所有该类型的bean
     *
     * @param clazz bean的字节码对象
     * @param <T>   由于待获取的bean类型是不确定的，所有这里使用泛型
     * @return 返回一个存储bean名称和对应实例的Map
     */
    public static <T> Map<String, T> getBeansOfType(Class<T> clazz) {
        return context.getBeansOfType(clazz);
    }
}