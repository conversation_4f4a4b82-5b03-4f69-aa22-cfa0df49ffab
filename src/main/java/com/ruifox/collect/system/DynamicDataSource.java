package com.ruifox.collect.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Description: 动态切换数据源
 **/
@Component
public class DynamicDataSource extends AbstractRoutingDataSource {
    private static final ThreadLocal<String> dataSourceFlag = ThreadLocal.withInitial(() -> "default"); // 线程安全容器: 用于存放数据源标记
    private static final ConcurrentHashMap<Object, Object> flag2DataSourceMap = new ConcurrentHashMap<>();
    private static DriverManagerDataSource defaultDataSource; // 默认数据源
    private static DriverManagerDataSource resourceDynamicDataSource; // 动态数据源
    private static DriverManagerDataSource buildDynamicDataSource; // 动态数据源2
    private static DriverManagerDataSource haxyyDynamicDataSource; // 动态数据源haxyy:
    private static DriverManagerDataSource newPhpDynamicDataSource; // 动态数据源NewPhp:
    private static DriverManagerDataSource oldPhpDynamicDataSource; // 动态数据源NewPhp:

    private static DriverManagerDataSource handleDynamicDataSource; // 动态数据源-更新医生简介:

    private static DriverManagerDataSource reloadDynamicDataSource; // 动态数据源-重新导入图片

    private static DriverManagerDataSource oldJavaDynamicDataSource; // 动态数据源OldJava:
    private static volatile DynamicDataSource INSTANCE;

    public static DynamicDataSource getInstance() {
        if (INSTANCE == null) {
            synchronized (DynamicDataSource.class) {
                if (INSTANCE == null) {
                    INSTANCE = ApplicationContextProvider.getBeanByType(DynamicDataSource.class);
                }
            }
        }
        return INSTANCE;
    }

    @Autowired
    private Environment env; // 用来获取yml中的配置信息
    // NOTE 这里为什么不用ApplicationContextProvider获取?
    // NOTE 因为会启动时报错，原因不明，但只要把ApplicationContextProvider的类名改为以C开头...就没问题...离谱吧？
    // NOTE 测试了好几个SpringBoot的版本都是这样，不知道是不是SpringBoot的bug
    // NOTE 我们还有一个工具类叫做ConfigUtil，它里面也是用ApplicationContextProvider获取的，同样的问题。
    // NOTE 所以干脆就用@Autowired注入了 Environment env;
    // NOTE 我怀疑是类启动加载顺序的问题
    // NOTE 使用过@PostConstruct来代替afterPropertiesSet，也不行，得，就这样吧。

    @Override
    public void afterPropertiesSet() {

        defaultDataSource = createDataSource("default");
        resourceDynamicDataSource = createDataSource("resourceDynamic");
        buildDynamicDataSource = createDataSource("buildDynamic");
        haxyyDynamicDataSource = createDataSource("haxyyDynamic");
        newPhpDynamicDataSource = createDataSource("NewPHPdynamic");
        oldPhpDynamicDataSource = createDataSource("OldPHPdynamic");
        handleDynamicDataSource = createDataSource("handleDynamic");
        reloadDynamicDataSource = createDataSource("reloadDynamic");
        oldJavaDynamicDataSource = createDataSource("oldJavaDynamic");

        // 添加进集合
        flag2DataSourceMap.put("default", defaultDataSource);
        flag2DataSourceMap.put("resourceDynamic", resourceDynamicDataSource);
        flag2DataSourceMap.put("buildDynamic", buildDynamicDataSource);
        flag2DataSourceMap.put("haxyyDynamic", haxyyDynamicDataSource);
        flag2DataSourceMap.put("NewPHPdynamic", newPhpDynamicDataSource);
        flag2DataSourceMap.put("OldPHPdynamic",oldPhpDynamicDataSource);
        flag2DataSourceMap.put("handleDynamic",handleDynamicDataSource);
        flag2DataSourceMap.put("reloadDynamic",reloadDynamicDataSource);
        flag2DataSourceMap.put("oldJavaDynamic",oldJavaDynamicDataSource);
        // 设置启用默认数据源
        dataSourceFlag.set("default");

        // 添加进AbstractRoutingDataSource抽象类
        super.setTargetDataSources(flag2DataSourceMap);
        super.setDefaultTargetDataSource(defaultDataSource);
        super.afterPropertiesSet();
    }

    /**
     * 创建数据源
     */
    private DriverManagerDataSource createDataSource(String prefix) {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUrl("jdbc:mysql://" + env.getProperty("mysql." + prefix + ".host") + ":" + env.getProperty("mysql." + prefix + ".port") + "/" + env.getProperty("mysql." + prefix + ".database") + "?useUnicode=true&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC&nullCatalogMeansCurrent=true&zeroDateTimeBehavior=convertToNull");
        dataSource.setUsername(env.getProperty("mysql." + prefix + ".username"));
        dataSource.setPassword(env.getProperty("mysql." + prefix + ".password"));
        return dataSource;
    }

    /**
     * 切换至默认数据源
     */
    public static void changeDefaultDataSource() {
        System.out.println("切换至默认数据源...");
        dataSourceFlag.set("default");
    }


    /**
     * 切换至默认动态数据源
     */
    public static void changeDynamicDataSource() {
        System.out.println("切换至动态数据源...");
        dataSourceFlag.set("resourceDynamic");
    }


    /**
     * 切换至默认动态数据源，使用自定义数据源信息
     */
    public static void changeDynamicDataSource(String host, String port, String database, String username, String password) {
        System.out.println("切换至动态数据源...");

        resourceDynamicDataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        resourceDynamicDataSource.setUrl("jdbc:mysql://" + host + ":" + port + "/" + database + "?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&allowPublicKeyRetrieval=true&characterSetResults=utf8mb4");
        resourceDynamicDataSource.setUsername(username);
        resourceDynamicDataSource.setPassword(password);

        dataSourceFlag.set("resourceDynamic");
    }


    /**
     * 切换至Resource动态数据源
     */
    public static void changeResourceDynamicDataSource() {
        System.out.println("切换至动态数据源...");
        dataSourceFlag.set("resourceDynamic");
    }


    /**
     * 切换至Resource动态数据源，使用自定义数据源信息
     */
    public static void changeResourceDynamicDataSource(String host, String port, String database, String username, String password) {
        System.out.println("切换至动态数据源...");

        resourceDynamicDataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        resourceDynamicDataSource.setUrl("jdbc:mysql://" + host + ":" + port + "/" + database + "?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&allowPublicKeyRetrieval=true&characterSetResults=utf8mb4");
        resourceDynamicDataSource.setUsername(username);
        resourceDynamicDataSource.setPassword(password);

        dataSourceFlag.set("resourceDynamic");
    }

    /**
     * 切换至Build动态数据源
     */
    public static void changeBuildDynamicDataSource() {
        System.out.println("切换至动态数据源2...");
        dataSourceFlag.set("buildDynamic");
    }

    /**
     * 切换至Build动态数据源，使用自定义数据源信息
     */
    public static void changeBuildDynamicDataSource(String host, String port, String database, String username, String password) {
        System.out.println("切换至动态数据源2...");
        buildDynamicDataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        buildDynamicDataSource.setUrl("jdbc:mysql://" + host + ":" + port + "/" + database + "?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&allowPublicKeyRetrieval=true&characterSetResults=utf8mb4");
        buildDynamicDataSource.setUsername(username);
        buildDynamicDataSource.setPassword(password);
        dataSourceFlag.set("buildDynamic");
    }

    /**
     * 切换至Haxyy动态数据源
     */
    public static void changeHaxyyDynamicDataSource() {
        System.out.println("切换至动态数据源Haxyy...");
        dataSourceFlag.set("haxyyDynamic");
    }

    /**
     * 切换至Build动态数据源，使用自定义数据源信息
     */
    public static void changeHaxyyDynamicDataSource(String host, String port, String database, String username, String password) {
        System.out.println("切换至动态数据源Haxyy...");
        buildDynamicDataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        buildDynamicDataSource.setUrl("jdbc:mysql://" + host + ":" + port + "/" + database + "?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&allowPublicKeyRetrieval=true&characterSetResults=utf8mb4");
        buildDynamicDataSource.setUsername(username);
        buildDynamicDataSource.setPassword(password);
        dataSourceFlag.set("haxyyDynamic");
    }

    /**
     * 切换至NewPhp动态数据源
     */
    public static void changeNewPhpDynamicDataSource() {
        System.out.println("切换至动态数据源NewPhp...");
        dataSourceFlag.set("NewPHPdynamic");
    }

    /**
     * 切换至OldPhp动态数据源
     */
    public static void changeOldPhpDynamicDataSource() {
        System.out.println("切换至动态数据源OldPhp...");
        dataSourceFlag.set("OldPHPdynamic");
    }

    /**
     * 切换至handle动态数据源
     */
    public static void changeHandleDynamicDataSource() {
        System.out.println("切换至动态数据源handleDynamic...");
        dataSourceFlag.set("handleDynamic");
    }


    /**
     * 切换至reload动态数据源
     */
    public static void changeReloadDynamicDataSource() {
        System.out.println("切换至动态数据源reloadDynamic...");
        dataSourceFlag.set("reloadDynamic");
    }



    /**
     * 切换至oldJavaDynamic动态数据源
     */
    public static void changeOldJavaDynamicDataSource() {
        System.out.println("切换至动态数据源oldJavaDynamic...");
        dataSourceFlag.set("oldJavaDynamic");
    }

    @Override
    protected Object determineCurrentLookupKey() {
        return dataSourceFlag.get();
    }

}
