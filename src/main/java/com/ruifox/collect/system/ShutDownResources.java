package com.ruifox.collect.system;

import com.ruifox.collect.designmode.factory.WebDriverFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;

@Component
@Slf4j
public class ShutDownResources {
    
    @PreDestroy
    public void end() {
        // TODO 关闭所有浏览器
        log.info("关闭所有浏览器对象!");
        WebDriverFactory.driverPool.getPool().forEach(managedFirefoxDriver -> managedFirefoxDriver.getDriver().quit());
    }
}
