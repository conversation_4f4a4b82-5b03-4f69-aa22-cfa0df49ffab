package com.ruifox.collect.extractors;

import fr.opensagres.poi.xwpf.converter.core.IImageExtractor;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @auther wave
 * @date 2024/8/29
 * @Description 用于替换word文件中的图片内容
 */
public class Base64ImageExtractor implements IImageExtractor {

    private final Map<String, String> imageMap = new HashMap<>();

    @Override
    public void extract(String imageName, byte[] imageBytes) throws IOException {
        String imageType = getImageType(imageName);
        String base64 = "data:image/" + imageType + ";base64," + Base64.getEncoder().encodeToString(imageBytes);
        imageMap.put(imageName,base64);
    }

    public Map<String,String> getImageMap(){
        return imageMap;
    }

    private String getImageType(String imageName){
        if (imageName.endsWith(".png")) return "png";
        if (imageName.endsWith(".jpg") || imageName.endsWith(".jpeg")) return "jpeg";
        return null;
    }

}
