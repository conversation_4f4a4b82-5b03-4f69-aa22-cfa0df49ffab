package com.ruifox.collect.aop.aspect;


import com.ruifox.collect.aop.annotation.TestBefore;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description:切面类
 * @author: RXH
 * @date: 2025/8/8 10:44
 * @param:
 * @return:
 **/
@Aspect
@Component
public class TestBeforeAspect {


    //打印字体颜色
    public static final String BLUE = "\033[34m";
    public static final String RESET = "\033[0m";

    @Value("${token.describe}")
    private String describe;

    public void init(){
        for (int i = 0; i < 10; i++) {
            System.out.println(BLUE+"=====================================当前使用的是: <"+describe+"> 的账号密码=============================="+RESET);
        }
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }


    @Pointcut("@within(com.ruifox.collect.aop.annotation.TestBefore)")
    public void testPointcut(){}


    @Before("testPointcut()")
    public void beforeTestMethod(JoinPoint joinPoint){
        Class<?> aClass = joinPoint.getTarget().getClass();
        TestBefore annotation = aClass.getAnnotation(TestBefore.class);

        System.out.println("[AOP 前置] 类：" + aClass.getName()
                + "，注解参数：" + annotation.value()
                + "，即将执行方法：" + joinPoint.getSignature().getName());

        for (int i = 0; i < 10; i++) {
            System.out.println(BLUE+"=====================================当前使用的是: <"+describe+"> 的账号密码=============================="+RESET);
        }
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
