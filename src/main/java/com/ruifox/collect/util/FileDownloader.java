package com.ruifox.collect.util;

// 导入必要的类和包

import com.ruifox.collect.common.constants.BusinessConstant; // 引入业务常量
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.manager.RedisGetManager; // 引入Redis管理类
import com.ruifox.collect.module.entity.TaskCookie; // 引入任务Cookie实体类
import lombok.extern.slf4j.Slf4j; // 引入日志处理库
import com.ruifox.collect.module.entity.CollectTask; // 引入收集任务实体类

import javax.imageio.ImageIO; // 用于图像处理
import javax.net.ssl.HttpsURLConnection; // 处理HTTPS连接
import javax.net.ssl.SSLContext; // 管理SSL上下文
import javax.net.ssl.TrustManager; // 信任管理
import javax.net.ssl.X509TrustManager; // 管理X.509证书
import java.awt.image.BufferedImage; // 存储图像数据
import java.io.*;
import java.net.HttpURLConnection; // 处理HTTP连接
import java.net.URL; // URL处理
import java.nio.file.Files; // 文件操作
import java.nio.file.Path; // 文件路径处理
import java.nio.file.Paths; // 处理文件路径
import java.nio.file.StandardCopyOption;
import java.security.cert.CertificateException; // 证书异常
import java.security.cert.X509Certificate; // X.509证书
import java.util.Base64; // Base64编码/解码
import java.util.List; // 列表处理
import java.util.Map; // 映射处理
import java.util.concurrent.Callable; // 可调用接口

@Slf4j // 使用Slf4j注解启用日志记录
public class FileDownloader {

    // 定义下载任务类，实现Callable接口
    public static class DownloadTask implements Callable<String> {

        // 定义类的属性
        private final String url; // 下载的URL
        private final String referer; // Referer请求头
        private final String fileNameWithoutExtension; // 不带扩展名的文件名
        private final String extension; // 文件扩展名
        private final CollectTask collectTask; // 收集任务对象

        // 构造函数，初始化属性
        public DownloadTask(String url, String referer, String fileNameWithoutExtension, String extension, CollectTask collectTask) {
            this.url = url;
            this.referer = referer;
            this.fileNameWithoutExtension = fileNameWithoutExtension;
            this.extension = extension;
            this.collectTask = collectTask;
        }

        @Override
        public String call() throws IOException {
            int maxRetries = 3; // 设置最大重试次数
            int retryCount = 0; // 当前重试次数

            while (retryCount < maxRetries) {
                try {
                    // TODO 针对报错javax.net.ssl.SSLHandshakeException: SSL握手异常 的补丁
                    // NOTE 这段代码用于创建自定义的SSL上下文，忽略服务器证书验证。在生产环境中这不安全，可能导致中间人攻击。
                    try {
                        SSLContext sc = SSLContext.getInstance("SSL");
                        sc.init(null, new TrustManager[]{new X509TrustManager() {
                            public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                            }

                            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                            }

                            public X509Certificate[] getAcceptedIssuers() {
                                return new X509Certificate[0];
                            }
                        }}, new java.security.SecureRandom());
                        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    // 增加连接超时时间或调整缓冲大小，来优化网络连接。
                    System.setProperty("sun.net.client.defaultConnectTimeout", "60000");
                    System.setProperty("sun.net.client.defaultReadTimeout", "60000");

                    // 检查是否为base64格式的图片，base64格式图片单独下载
                    if (url.contains("data:image")) {
                        ByteArrayInputStream bais = null;
                        try {
                            String urlTmp = url.split(",")[1];
                            String extra = url.split(",")[0];
                            String style = extra.substring(extra.lastIndexOf("/") + 1, extra.indexOf(";"));
                            String extension = "." + style;
                            byte[] bytes = Base64.getDecoder().decode(urlTmp);
                            bais = new ByteArrayInputStream(bytes);
                            BufferedImage bufferedImage = ImageIO.read(bais);
                            String fileName = fileNameWithoutExtension + extension;
                            File imageFile = new File(fileName);
                            ImageIO.write(bufferedImage, style, imageFile);

                            return fileName.replace("\\", "/");
                        } catch (Exception e) {
                            throw new IOException(e.getMessage());
                        } finally {
                            if (bais!= null) {
                                bais.close();
                            }
                        }
                    }

                    // 处理HTTP下载
                    URL urlObj = new URL(url);
                    HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
                    // 再获取一次连接
                    HttpURLConnection connection2 = (HttpURLConnection) urlObj.openConnection();
                    connection.setRequestMethod("GET");
                    connection.setDoInput(true);
                    connection.setDoOutput(true);
                    connection.setRequestProperty("Referer", referer);
                    connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:134.0) Gecko/20100101 Firefox/134.0");
                    // 设置cookie
                    TaskCookie taskCookie = RedisGetManager.getTaskCookie(collectTask);
                    if (taskCookie!= null && taskCookie.getIsDownload() == 1) {
                        StringBuilder stringBuilder = new StringBuilder();
                        List<Map<String, String>> list = JsonUtil.Json2Obj(taskCookie.getCookie(), List.class);
                        for (Map<String, String> map : list) {
                            stringBuilder.append(map.get("key")).append("=").append(map.get("value")).append(";");
                        }
                        connection.setRequestProperty("Cookie", stringBuilder.toString());
                    }
                    connection.setInstanceFollowRedirects(true);

                    int responseCode = connection.getResponseCode();
                    // TODO 处理http重定向（这里只处理了一次重定向的情况，后续有需要的话进行多次判断）
                    while (responseCode >= 300 && responseCode <= 399) {
                        String redirectUrl = connection.getHeaderField("Location");
                        if (redirectUrl!= null) {
                            urlObj = new URL(redirectUrl);
                            connection = (HttpURLConnection) urlObj.openConnection();
                            connection.setInstanceFollowRedirects(true);
                            connection.setRequestMethod("GET");
                            connection.setDoInput(true);
                            connection.setDoOutput(true);
                            connection.setRequestProperty("Referer", "https://mp.weixin.qq.com/");
                            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:134.0) Gecko/20100101 Firefox/134.0");
                            responseCode = connection.getResponseCode();
                        }
                    }
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        InputStream in = connection.getInputStream();
                        InputStream inputStream = connection2.getInputStream();

                        Path filePath = null;
                        if (BusinessConstant.FileErrorExtension.equals(extension)) {
                            String extension = getExtensionFromContentType(connection.getContentType());
                            if (extension.contains("text") || extension.contains("bin")) {
                                extension = ".unknown";
                            }
                            filePath = Paths.get(fileNameWithoutExtension + extension);
                        } else {
                            filePath = Paths.get(fileNameWithoutExtension + extension);
                        }

                        while (Files.exists(filePath)) {
                            log.info("目标文件已存在，你可以选择手动删除或者重新命名目标文件。");

                            String dirPathFileName = FileDataManager.getDirPathFileName(collectTask);
                            filePath = Paths.get(dirPathFileName + extension);
                        }

                        Files.copy(in, filePath, StandardCopyOption.REPLACE_EXISTING);

                        // 通过contentType获取不到时，使用魔数获取，获取后若获取成功就将其文件后缀替换为这个新的后缀
                        String a = getFileExtensionFromStream(inputStream);
                        File file = filePath.toFile();
                        if (file.getName().substring(file.getName().lastIndexOf(".")).contains("unknown")) {
                            if (!a.contains("unknown")) {
                                file.renameTo(new File(fileNameWithoutExtension + a));
                                filePath = new File(fileNameWithoutExtension + a).toPath();
                            }
                        }

                        in.close();
                        return filePath.toString().replace("\\", "/");
                    } else {
                        throw new IOException("Failed to download file, server returned HTTP code: " + responseCode);
                    }
                } catch (IOException e) {
                    String errorMessage = e.getMessage();
                    if (errorMessage.contains("Connection reset") ||
                            // 这里可以根据实际可能出现的其他需要重试的异常情况添加更多判断条件，比如SSL握手异常等
                            errorMessage.contains("SSLHandshakeException")) {
                        retryCount++;
                        log.warn("Download failed due to {}, retrying (attempt {}/{})...", errorMessage, retryCount, maxRetries);
                        continue;
                    }
                    throw e;
                }
            }
            throw new IOException("Failed to download file after " + maxRetries + " retries.");
        }


        // 根据内容类型获取文件扩展名
        private String getExtensionFromContentType(String contentType) {
            if ("image/jpg".equals(contentType) || "image/jpeg".equals(contentType) || "image/pjpeg".equals(contentType))
                return ".jpg"; // JPEG格式
            else if ("image/webp".equals(contentType))
                return ".webp"; // WebP格式
            else if ("image/tiff".equals(contentType))
                return ".tiff"; // TIFF格式
            else if ("image/png".equals(contentType) || "image/x-png".equals(contentType))
                return ".png"; // PNG格式
            else if ("image/gif".equals(contentType))
                return ".gif"; // GIF格式
            else if ("image/svg+xml".equals(contentType))
                return ".svg"; // SVG格式
            else if ("image/bmp".equals(contentType) || "image/x-bitmap".equals(contentType) || "image/x-pixmap".equals(contentType))
                return ".bmp"; // BMP格式
            else if ("audio/mpeg".equals(contentType))
                return ".mp3"; // MP3格式
            else if ("audio/x-wav".equals(contentType) || "audio/wav".equals(contentType))
                return ".wav"; // WAV格式
            else if ("audio/aac".equals(contentType))
                return ".aac"; // AAC格式
            else if ("video/mp4".equals(contentType) || "video/x-m4v".equals(contentType))
                return ".mp4"; // MP4格式
            else if ("video/x-msvideo".equals(contentType) || "video/avi".equals(contentType))
                return ".avi"; // AVI格式
            else if ("video/quicktime".equals(contentType))
                return ".mov"; // MOV格式
            else if ("video/x-flv".equals(contentType))
                return ".flv"; // FLV格式
            else if ("video/webm".equals(contentType))
                return ".webm"; // WEBM格式
            else if ("application/pdf".equals(contentType))
                return ".pdf"; // PDF格式
            else if ("application/msword".equals(contentType) || "application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(contentType))
                return ".docx"; // DOCX格式
            else if ("application/vnd.ms-excel".equals(contentType) || "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType))
                return ".xlsx"; // XLSX格式
            else if ("application/vnd.ms-powerpoint".equals(contentType) || "application/vnd.openxmlformats-officedocument.presentationml.presentation".equals(contentType))
                return ".pptx"; // PPTX格式
            else if ("application/zip".equals(contentType))
                return ".zip"; // ZIP格式
            else if ("application/x-rar-compressed".equals(contentType) || "application/x-rar-compressed; version=5".equals(contentType))
                return ".rar"; // RAR格式

                // 二进制流，通常需要其他信息来确定
            else if ("application/octet-stream".equals(contentType))
                return ".bin"; // 二进制流

                // 文本类型可能不需要特定后缀，或根据需要处理
            else if (contentType.startsWith("text/"))
                return ".text"; // 文本格式

            else if ("application/octet-stream".equals(contentType))
                return ".unknown"; // 未知格式

                // 如果无法识别类型，可以考虑默认一个或者抛出异常
            else return ".unknown"; // 默认返回未知格式
        }

        // 从输入流获取文件扩展名
        private String getFileExtensionFromStream(InputStream in) throws IOException {
            byte[] magicNumber = new byte[4]; // 定义魔数数组
            in.read(magicNumber); // 读取魔数

            // MP4 文件
            if (magicNumber[0] == (byte) 0x00 && magicNumber[1] == (byte) 0x00 &&
                    magicNumber[2] == (byte) 0x00 && magicNumber[3] == (byte) 0x20) {
                return ".mp4"; // 返回MP4扩展名
            }
            // JPEG 文件
            else if (magicNumber[0] == (byte) 0xFF && magicNumber[1] == (byte) 0xD8) {
                return ".jpg"; // 返回JPEG扩展名
            }
            // PNG 文件
            else if (magicNumber[0] == (byte) 0x89 && magicNumber[1] == (byte) 0x50 &&
                    magicNumber[2] == (byte) 0x4E && magicNumber[3] == (byte) 0x47) {
                return ".png"; // 返回PNG扩展名
            }
            // GIF 文件
            else if (magicNumber[0] == (byte) 0x47 && magicNumber[1] == (byte) 0x49 &&
                    magicNumber[2] == (byte) 0x46) {
                return ".gif"; // 返回GIF扩展名
            }
            // TIFF 文件
            else if ((magicNumber[0] == (byte) 0x49 && magicNumber[1] == (byte) 0x49) ||
                    (magicNumber[0] == (byte) 0x4D && magicNumber[1] == (byte) 0x4D)) {
                return ".tiff"; // 返回TIFF扩展名
            }
            // BMP 文件
            else if (magicNumber[0] == (byte) 0x42 && magicNumber[1] == (byte) 0x4D) {
                return ".bmp"; // 返回BMP扩展名
            }
            // PDF 文件
            else if (magicNumber[0] == (byte) 0x25 && magicNumber[1] == (byte) 0x50 &&
                    magicNumber[2] == (byte) 0x44 && magicNumber[3] == (byte) 0x46) {
                return ".pdf"; // 返回PDF扩展名
            }
            // MP3 文件
            else if (magicNumber[0] == (byte) 0xFF && (magicNumber[1] & (byte) 0xE0) == (byte) 0xE0) {
                return ".mp3"; // 返回MP3扩展名
            }
            // ZIP 文件
            else if (magicNumber[0] == (byte) 0x50 && magicNumber[1] == (byte) 0x4B) {
                return ".zip"; // 返回ZIP扩展名
            }
            // RAR 文件（部分）
            else if (magicNumber[0] == (byte) 0x52 && magicNumber[1] == (byte) 0x61 &&
                    magicNumber[2] == (byte) 0x72 && magicNumber[3] == (byte) 0x21) {
                return ".rar"; // 返回RAR扩展名
            } else if (magicNumber[0] == (byte) 0x4D && magicNumber[1] == (byte) 0x5A) {
                return ".exe"; // 返回EXE扩展名
            } else if (magicNumber[0] == (byte) 0x7F && magicNumber[1] == (byte) 0x45 &&
                    magicNumber[2] == (byte) 0x4C && magicNumber[3] == (byte) 0x46) {
                return ".elf"; // 返回ELF扩展名
            } else if (magicNumber[0] == (byte) 0x52 && magicNumber[1] == (byte) 0x61 &&
                    magicNumber[2] == (byte) 0x72 && magicNumber[3] == (byte) 0x21) {
                return ".rar"; // 返回RAR扩展名
            } else if (magicNumber[0] == (byte) 0x42 && magicNumber[1] == (byte) 0x5A) {
                return ".bz2"; // 返回BZ2扩展名
            } else if (magicNumber[0] == (byte) 0x1F && magicNumber[1] == (byte) 0x8B) {
                return ".gz"; // 返回GZ扩展名
            } else if (magicNumber[0] == (byte) 0xD0 && magicNumber[1] == (byte) 0xCF &&
                    magicNumber[2] == (byte) 0x11 && magicNumber[3] == (byte) 0xE0) {
                return ".doc"; // 返回旧版DOC扩展名
            } else if (magicNumber[0] == (byte) 0x50 && magicNumber[1] == (byte) 0x4B &&
                    magicNumber[2] == (byte) 0x03 && magicNumber[3] == (byte) 0x04) {
                return ".zip"; // 返回ZIP扩展名
            } else if (magicNumber[0] == (byte) 0x50 && magicNumber[1] == (byte) 0x4B &&
                    magicNumber[2] == (byte) 0x07 && magicNumber[3] == (byte) 0x08) {
                return ".docx"; // 返回DOCX扩展名
            } else if (magicNumber[0] == (byte) 0x50 && magicNumber[1] == (byte) 0x4B &&
                    magicNumber[2] == (byte) 0x5A && magicNumber[3] == (byte) 0x28) {
                return ".xlsx"; // 返回XLSX扩展名
            } else if (magicNumber[0] == (byte) 0x50 && magicNumber[1] == (byte) 0x4B &&
                    magicNumber[2] == (byte) 0x5A && magicNumber[3] == (byte) 0x22) {
                return ".pptx"; // 返回PPTX扩展名
            }

            return ".unknown"; // 默认返回未知扩展名
        }

    }
}