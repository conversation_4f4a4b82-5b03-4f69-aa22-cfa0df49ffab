package com.ruifox.collect.util;

import cn.hutool.http.HttpDownloader;
import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
public class WechatUtil {


    /**
     * 读取文件数据，目前程序中用来从本地html文件中获取源码
     *
     * @param path 本地html文件路径
     * @return pageSource 源码
     */
    public static String getPageSourceFromFile(String path) {
        try {
            return new String(Files.readAllBytes(Paths.get(path)));
            //使用FileUtils 类获取文件内容
            //return FileUtils.readFileToString(new File(path), "UTF-8");
        } catch (Exception e) {
            return "";
        }
    }


    /**
     * 微信外链基本信息获取（标题、来源、时间，摘要，封面）
     *
     * @param document    微信详情页面
     * @param collectTask 采集任务
     * @return Map集合，包含基本信息
     */
    public static Map<String, String> getWechatBaseInfo(Document document, CollectTask collectTask, String pageId) {
        Map<String, String> map = new HashMap<>();


        try {
            // 标题
            String title = "";
            Element titleElement = document.select("h1#activity-name").first();
            if (titleElement != null) {
                title = titleElement.text();
            }
            // 来源
            String come_from = "";
            Element comefromElement = document.select("a#js_name").first();
            if (comefromElement != null) {
                come_from = comefromElement.text();
            }

            // 正文元素
            Element content = document.select("div#js_content").first();

            // 摘要
            String description = content.text();
            if (StringUtils.isBlank(description)) {
                description = "";
            } else {
                description = StringUtils.trim(description.substring(0, Math.min(description.length(), 150))) + "...";
            }

            //发布时间
            String publish_time = "";
            Element timeElement = document.select("em#publish_time").first();
            if (timeElement != null) {
                publish_time = timeElement.text();
                publish_time = publish_time.trim();
                publish_time = publish_time.substring(0, publish_time.indexOf("日") + "日".length());
                Date date = DateUtils.parseDate(publish_time, "yyyy年MM月dd日");
                publish_time = String.valueOf(date.getTime() / 1000);
            }

            //作者
            /*String author = "";
            Element authorElement = document.select("section[data-fail=\"0\"] > p:nth-of-type(2) > span").first();
            if(authorElement!=null){
                String text = authorElement.text();
                if(StringUtils.isNotBlank(text)) {
                    author = text.substring(text.indexOf("辑：") + "辑：".length());
                }
            }else{
                authorElement = document.select("section[data-role=\"outer\"]> section >section >section:first-child > p:nth-of-type(2) > span").first();
                if(authorElement!=null){
                    String text = authorElement.text();
                    if(StringUtils.isNotBlank(text)) {
                        if(text.contains("来   源："))
                            author = text.substring(text.indexOf("辑：") + "辑：".length(),text.indexOf("来"));
                        else if(text.endsWith("| 文"))
                            author = text.substring(text.indexOf("辑：") + "辑：".length(),text.indexOf("| 文"));
                        else
                            author = text.substring(text.indexOf("辑：") + "辑：".length());
                    }
                }
            }*/
            String targetUrl = collectTask.getTargetUrl();
            //TODO 图片的下载
            Elements imgList = content.select("img");
            if (imgList != null) {
                for (Element img : imgList) {
                    String src = img.attr("data-src");
                    if (src.isEmpty())
                        continue;
                    String localPath = FileDataManager.downloadFromHttpUrl(src, targetUrl, pageId, collectTask);
                    localPath = localPath.replace("C:/Collect_Data", "https://gzsrmyy.my120.org");
                    img.attr("data-src", localPath);
                    img.attr("src", localPath);
                    //有这个属性的话会显示不出来
                    img.removeAttr("crossorigin");
                }
            }

            Elements sectionList = content.select("section[style*='background-image: url']");
            if (sectionList != null) {
                for (Element section : sectionList) {
                    String src = section.attr("data-lazy-bgimg");
                    if (src.isEmpty())
                        continue;
                    String style = section.attr("style");
                    String substring = style.substring(style.indexOf("\"") + "\"".length());
                    String url = substring.substring(0, substring.indexOf("\""));

                    String localPath = FileDataManager.downloadFromHttpUrl(src, targetUrl, pageId, collectTask);
                    localPath = localPath.replace("C:/Collect_Data", "https://gzsrmyy.my120.org");
                    section.attr("data-lazy-bgimg", localPath);
                    style = section.attr("style");
                    style = style.replace(url, localPath);
                    section.attr("style", style);
                    //有这个属性的话会显示不出来
                    section.removeAttr("crossorigin");
                }
            }

            //视频下载
            Elements videoOutList = content.select("div[id*='js_mpvedio_wrapper_wxv']");
            if (videoOutList != null) {
                for (Element videoOut : videoOutList) {
                    Elements video = videoOut.select("video");
                    String src = video.attr("src").replace("&amp;", "&").replace("https", "http");
                    if (src.isEmpty())
                        continue;
                    String poster = video.attr("poster");
                    String localPathVideo = "";
                    localPathVideo = FileDataManager.downloadFromHttpUrl(src, targetUrl, pageId, collectTask);
                    //转为上传url
                    localPathVideo = localPathVideo.replace("C:/Collect_Data", "https://gzsrmyy.my120.org");

                    String localPathPoster = FileDataManager.downloadFromHttpUrl(poster, targetUrl, pageId, collectTask);
                    //转为上传url
                    localPathPoster = localPathPoster.replace("C:/Collect_Data", "https://gzsrmyy.my120.org");

                    String myVideo = "<video src=\"" + localPathVideo + "\" poster=\"" + localPathPoster + "\" controls=\"controls\" width=\"640\" height=\"360\" style=\"display: block; margin-left:auto; margin-right:auto\">\n" +
                            "        111\n" +
                            "    </video>\n";
                    Element newVideoElement = Jsoup.parse(myVideo).body().child(0);
                    videoOut.replaceWith(newVideoElement);
                }
            }

            //轮播图删除--测试
            //content.select("foreignobject").remove();

            map.put(NewsDataConstant.CONTENT, content.toString());
            map.put(NewsDataConstant.TITLE, title);
            map.put(NewsDataConstant.COME_FROM, come_from);
            map.put(NewsDataConstant.DESCRIPTION, description);
            map.put(NewsDataConstant.PUBLISH_TIME, publish_time);
            //map.put(NewsDataConstant.AUTHOR, author);
        } catch (Exception e) {
            throw new RuntimeException("解析微信文章失败，原因：" + e.getMessage());
        }
        return map;
    }

    public static String getWechatBaseInfo(Document document, String targetUrl, String pageId, CollectTask collectTask) {
        String result;
        try {
            // 正文元素
            Element content = document.select("div#js_content").first();
            if(content == null){
                return "公众号内容出错";
            }

            //TODO 图片的下载
            Elements imgList = content.select("img");
            if (imgList != null) {
                for (Element img : imgList) {
                    String src = img.attr("data-src");
                    if (src.isEmpty())
                        continue;
                    String localPath = FileDataManager.downloadFromHttpUrl(src, targetUrl, pageId, collectTask);
                    localPath = localPath.replace("C:/Collect_Data/gzsrmyy", "https://gzsrmyy.my120.org/oss");
                    img.attr("data-src", localPath);
                    img.attr("src", localPath);
                    //有这个属性的话会显示不出来
                    img.removeAttr("crossorigin");
                }
            }

            Elements sectionList = content.select("section[style*='background-image: url']");
            if (sectionList != null) {
                for (Element section : sectionList) {
                    String src = section.attr("data-lazy-bgimg");
                    if (src.isEmpty())
                        continue;
                    String style = section.attr("style");
                    String substring = style.substring(style.indexOf("\"") + "\"".length());
                    String url = substring.substring(0, substring.indexOf("\""));

                    String localPath = FileDataManager.downloadFromHttpUrl(src, targetUrl, pageId, collectTask);
                    localPath = localPath.replace("C:/Collect_Data/gzsrmyy", "https://gzsrmyy.my120.org/oss");
                    section.attr("data-lazy-bgimg", localPath);
                    style = section.attr("style");
                    style = style.replace(url, localPath);
                    section.attr("style", style);
                    //有这个属性的话会显示不出来
                    section.removeAttr("crossorigin");
                }
            }

            //视频下载
            Elements videoOutList = content.select("div[id*='js_mpvedio_']");
            if (videoOutList != null) {
                for (Element videoOut : videoOutList) {
                    Elements video = videoOut.select("video");
                    String src = video.attr("src").replace("&amp;", "&").replace("https", "http");
                    if (src.isEmpty())
                        continue;
                    String poster = video.attr("poster");
                    String localPathVideo = "";
                    localPathVideo = FileDataManager.downloadFromHttpUrl(src, targetUrl, pageId, collectTask);
                    //转为上传url
                    localPathVideo = localPathVideo.replace("C:/Collect_Data/gzsrmyy", "https://gzsrmyy.my120.org/oss");

                    String localPathPoster = FileDataManager.downloadFromHttpUrl(poster, targetUrl, pageId, collectTask);
                    //转为上传url
                    localPathPoster = localPathPoster.replace("C:/Collect_Data/gzsrmyy", "https://gzsrmyy.my120.org/oss");

                    String myVideo = "<video src=\"" + localPathVideo + "\" poster=\"" + localPathPoster + "\" controls=\"controls\" width=\"640\" height=\"360\" style=\"display: block; margin-left:auto; margin-right:auto\">\n" +
                            "        111\n" +
                            "    </video>\n";
                    Element newVideoElement = Jsoup.parse(myVideo).body().child(0);
                    videoOut.replaceWith(newVideoElement);
                }
            }

            result = content.toString();
        } catch (Exception e) {
            throw new RuntimeException("解析微信文章失败" + targetUrl + "，原因：", e);
        }
        return result;
    }

    // TODO 微信外链正文内容解析，替换图片、视频路径，返回封面路径、摘要、正文
    public static String parseContent(Element element, CollectTask collectTask) {

        // 正文
        String content = element.toString();

        // 1、获取正文中所有图片的URL


        // 将正文中的图片路径去除所有参数
        String baseUrl = FileDataManager.getDirPath(collectTask);
        content = content.replaceAll("(" + baseUrl + "[^\"&]+)(&.*)?", "$1");

        return content;
    }


    // TODO 完整解析正文
    public static Map<String, String> getWechatAllInfo(Document document, CollectTask collectTask) {
        Map<String, String> map = new HashMap<>();

        try {

            Map<String, String> baseInfo = WechatUtil.getWechatBaseInfo(document, collectTask, "111");
            String content = WechatUtil.parseContent(document.select("div#js_content").first(), collectTask);


        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
        return map;
    }


    public static String removeParameters(String input) {
        String patternStr = "C:/www.cs4hospital.com/20241207/[^&]+";
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(input);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String withoutParams = matcher.group().split("&")[0];
            matcher.appendReplacement(result, withoutParams);
        }
        matcher.appendTail(result);
        return result.toString();
    }

}
