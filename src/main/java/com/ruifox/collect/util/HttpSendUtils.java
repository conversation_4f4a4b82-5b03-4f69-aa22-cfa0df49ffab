package com.ruifox.collect.util;

import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

public class HttpSendUtils {
    // TODO 一个发送HTTP GET 和 POST 请求的工具类，使用案例见main方法

    public static SendUtils setUrl(String url) {
        return new SendUtils(url);
    }
    
    public static class SendUtils {
        private String body;
        private HttpHeaders headers;
        private UriComponentsBuilder uriComponentsBuilder;
        
        public SendUtils(String url) {
            this.body = null;
            this.headers = null;
            this.uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(url);
        }
        
        /**
         * 添加请求参数
         *
         * @param name 参数名
         * @return SendPost对象
         */
        public SendUtils addQueryParam(String name, Object... values) {
            this.uriComponentsBuilder.queryParam(name, values);
            return this;
        }
        
        /**
         * 批量添加请求参数
         *
         * @param queryParams 请求参数MultiValueMap
         * @return SendPost对象
         */
        public SendUtils addQueryParams(Map<String, Object> queryParams) {
            queryParams.forEach((k, v) -> {
                this.uriComponentsBuilder.queryParam(k, v);
            });
            return this;
        }
        
        /**
         * 添加请求头
         *
         * @param name  请求头名
         * @param value 请求头值
         * @return SendPost对象
         */
        public SendUtils addHead(String name, Object value) {
            if (this.headers == null) {
                this.headers = new HttpHeaders();
            }
            headers.set(name, value.toString());
            return this;
        }
        
        /**
         * 批量添加请求头
         *
         * @param heads 请求体Map
         * @return SendPost对象
         */
        public SendUtils addHeads(Map<String, String> heads) {
            if (this.headers == null) {
                this.headers = new HttpHeaders();
            }
            headers.forEach((k, v) -> {
                this.headers.set(k, v.toString());
            });
            return this;
        }
        
        /**
         * 设置内容类型
         *
         * @param value 内容类型
         * @return SendPost对象
         */
        public SendUtils setContentType(MediaType value) {
            if (this.headers == null) {
                this.headers = new HttpHeaders();
            }
            headers.setContentType(value);
            return this;
        }
        
        /**
         * 设置请求体
         *
         * @param body 请求体
         * @return SendPost对象
         */
        public SendUtils setBody(String body) {
            this.body = body;
            return this;
        }
        
        /**
         * 发送Get请求
         *
         * @return SendPost对象
         */
        public <T> ResponseEntity<T> sendGet(Class<T> clazz) {
            try {
                String url = this.uriComponentsBuilder.toUriString();
                RestTemplate restTemplate = new RestTemplate();
                return restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), clazz);
            } catch (Exception e) {
                return null;
            }
        }
        
        /**
         * 发送Post请求
         *
         * @return SendPost对象
         */
        public <T> ResponseEntity<T> sendPost(Class<T> clazz) {
            String url = this.uriComponentsBuilder.toUriString();
            RestTemplate restTemplate = new RestTemplate();
            return restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(body, headers), clazz);
        }
    }

    public static void main(String[] args) {
        // 发送 GET 请求
        ResponseEntity<String> getResponse = HttpSendUtils.setUrl("https://example.com/api/get-endpoint")
                .addQueryParam("param1", "value1")
                .addQueryParam("param2", "value2")
                .addHead("Authorization", "Bearer your_token")
                .setContentType(MediaType.APPLICATION_JSON)
                .sendGet(String.class);

        if (getResponse!= null) {
            System.out.println("GET Response Status Code: " + getResponse.getStatusCode());
            System.out.println("GET Response Body: " + getResponse.getBody());
        }
    }
}