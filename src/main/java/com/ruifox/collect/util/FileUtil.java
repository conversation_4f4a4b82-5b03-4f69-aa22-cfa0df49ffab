package com.ruifox.collect.util;

import com.ruifox.collect.system.ConfigUtil;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;

public class FileUtil {


    /**
     * 创建文件夹
     */
    public static boolean createFolder(String str) {
        File fileFolder = new File(str);
        if (!fileFolder.exists()) {
            return fileFolder.mkdirs();
        }
        return true;
    }

    /**
     * 检查文件路径在本地是否存在
     */
    public static boolean localUrlChick(String path) {
        // NOTE 对于非下载文件，目前处理逻辑是返回false，可能会使得该元素被移除；
        if(path.contains(".gif")) {
            return false;
        }
        if(path.contains("/oss/")) {
            return true;
        }
        if (!path.startsWith(ConfigUtil.getProperties("common.save-path"))) {
            return false;
        }
        try {
           File file = new File(path);
//            return file.exists();

            // 判断文件大小，小于1kb返回false
            long fileSize = Files.size(file.toPath());
            return fileSize > 1024;
        } catch (Exception e) {
            return false;
        }
    }

    // TODO 获取本地文件扩展后缀

    // TODO 下载文件到指定位置
    public static boolean downloadFile(String url, String path) {
        try {
            URL urlT = new URL(url);
            URLConnection connection = urlT.openConnection();
            InputStream inputStream = connection.getInputStream();
            FileOutputStream outputStream = new FileOutputStream(path);
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
