package com.ruifox.collect.util;

import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.SerializationConfig;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;
import org.codehaus.jackson.type.JavaType;
import org.codehaus.jackson.type.TypeReference;

import java.text.SimpleDateFormat;

public class JsonUtil {
 
	private static ObjectMapper objectMapper = new ObjectMapper();
	static {
		// TODO 对象的所有字段全部列入
		objectMapper.setSerializationInclusion(Inclusion.ALWAYS);
 
		// TODO 取消默认转换timestamps形式
		objectMapper.configure(SerializationConfig.Feature.WRITE_DATES_AS_TIMESTAMPS, false);

		// TODO 忽略空Bean转json的错误
		objectMapper.configure(SerializationConfig.Feature.FAIL_ON_EMPTY_BEANS, false);

		// TODO 所有的日期格式都统一为以下的样式，即yyyy-MM-dd HH:mm:ss
		objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
 
		// TODO 忽略 在json字符串中存在，但是在java对象中不存在对应属性的情况。防止错误
		objectMapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}


	/**
	 * 对象转json
	 * @param obj 目标对象
	 * @return json字符串
	 * @param <T> 泛型
	 */
	public static <T> String obj2String(T obj) {
		if (obj == null) {
			return "";
		}
		try {
			return obj instanceof String ? (String) obj : objectMapper.writeValueAsString(obj);
		} catch (Exception e) {
			System.err.println("Parse Object to String error");
			return null;
		}
	}
 
	public static <T> String obj2StringPretty(T obj) {
		if (obj == null) {
			return null;
		}
		try {
			return obj instanceof String ? (String) obj
					: objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
		} catch (Exception e) {
			System.err.println("Parse Object to String error");
			return null;
		}
	}


	/**
	 * json字符串转目标对象
	 * @param str json字符串
	 * @param clazz 目标对象字节码
	 * @return 目标对象
	 * @param <T> 泛型
	 */
	public static <T> T Json2Obj(String str, Class<T> clazz) {
		if (StringUtils.isEmpty(str) || clazz == null) {
			return null;
		}
 
		try {
			return clazz.equals(String.class) ? (T) str : objectMapper.readValue(str, clazz);
		} catch (Exception e) {
			System.err.println("Parse String to Object error");
			return null;
		}
	}
 
	public static <T> T Json2Obj(String str, TypeReference<T> typeReference) {
		if (StringUtils.isEmpty(str) || typeReference == null) {
			return null;
		}
		try {
			return (T) (typeReference.getType().equals(String.class) ? str
					: objectMapper.readValue(str, typeReference));
		} catch (Exception e) {
			System.err.println("Parse String to Object error");
			return null;
		}
	}
 
	public static <T> T Json2Obj(String str, Class<?> collectionClass, Class<?>... elementClasses) {
		JavaType javaType = objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
		try {
			return objectMapper.readValue(str, javaType);
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println("Parse String to Object error");
			return null;
		}
	}

}