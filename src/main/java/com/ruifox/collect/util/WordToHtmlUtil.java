package com.ruifox.collect.util;

import com.ruifox.collect.extractors.Base64ImageExtractor;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.converter.WordToHtmlConverter;
import org.apache.poi.hwpf.usermodel.Picture;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.util.*;

public class WordToHtmlUtil {

    public static String parseDocxToHtml(File file) {
        ByteArrayOutputStream htmlStream = new ByteArrayOutputStream();
        String content = null;
        try(InputStream fis = new FileInputStream(file)) {
            XWPFDocument document = new XWPFDocument(fis);
            XHTMLOptions options = XHTMLOptions.create();
            Base64ImageExtractor base64ImageExtractor = new Base64ImageExtractor();
            options.setExtractor(base64ImageExtractor);
            //TODO 转换html
            document.createNumbering();
            XHTMLConverter.getInstance().convert(document, htmlStream, options);
            String html = htmlStream.toString();
            //TODO 替换图片链接
            for (Map.Entry<String, String> entry : base64ImageExtractor.getImageMap().entrySet()) {
                String imageName = entry.getKey();
                String base64Image = entry.getValue();
                html = html.replace("src=\"" + imageName + "\"", "src=\"" + base64Image + "\"");
            }
            Document doc = Jsoup.parse(html);
            doc.getElementsByTag("head").get(0).appendChild(new Element("meta").attr("charset", "utf-8"));
            // TODO 修改body下的第一个div大小为90%
            Element firstDiv = doc.body().select("div").first();
            if (firstDiv != null) {
                // 移除直接width属性（如width="500px"）
                firstDiv.removeAttr("width");
//                // 获取当前style属性（如果存在）
//                String style = firstDiv.attr("style");
//                // 移除style中已有的width声明（避免冲突）
//                if (style != null && !style.isEmpty()) {
//                    style = style.replaceAll("width\\s*:\\s*[^;]+;", ""); // 删除width:xxx;
//                }
//                // 添加新的width:100%
//                style = style.trim() + "width:90%;"; // 追加到现有样式末尾
                // 更新style属性
//                firstDiv.attr("style", style);
                // FIXME 这里直接移除原有style，取消A4纸结构，后续修改！！！
                firstDiv.removeAttr("style");
                firstDiv.attr("style", "width:90%;margin: 0 auto;");
            }
            // TODO 修改table标签width值为100%
            Elements tables = doc.getElementsByTag("table");
            for (Element table : tables) {
                // 移除原有的width属性（无论是style还是直接width）
                table.removeAttr("width"); // 移除直接width属性（如width="300"）
//                // 处理style中的width（如style="width:500px;"）
//                String style = table.attr("style");
//                if (style != null && !style.isEmpty()) {
//                    // 使用正则替换style中的width值
//                    style = style.replaceAll("width\\s*:\\s*[^;]+;", "width:100%;");
//                    table.attr("style", style);
//                } else {
//                    // 如果style不存在，直接添加width:100%
//                    table.attr("style", "width:100%;");
//                }
                // FIXME 这里直接删除了原table的格式，默认重新添加了格式，后续修改！！！
                table.removeAttr("style");
                table.attr("style", "width:100%;border: 1.5px solid #000000;border-collapse:collapse;");
            }
            // TODO 修改td标签，添加属性border:1.5px solid #000000;
            Elements tds = doc.getElementsByTag("td");
            for (Element td : tds) {
                // 获取当前style属性（如果存在）
                String existingStyle = td.attr("style");
                // 移除已有的border相关声明（避免冲突）
                if (existingStyle != null && !existingStyle.isEmpty()) {
                    existingStyle = existingStyle.replaceAll("border\\s*:\\s*[^;]+;", "");
                    existingStyle = existingStyle.replaceAll("border-(top|bottom|left|right)\\s*:\\s*[^;]+;", "");
                }
                // 添加新的border样式
                String newStyle = existingStyle + " border: 1.5px solid #000000;";
                // 更新style属性
                td.attr("style", newStyle.trim());
            }
            content = doc.getElementsByTag("body").get(0).html();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return content;
    }

    public static String parseDocToHtml(File file) {
        String content = null;
        try(FileInputStream fis = new FileInputStream(file);) {

            // 创建 DocumentBuilderFactory 并禁用外部实体和 DTD
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
//            dbf.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
//            dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
//            dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
//            dbf.setFeature("http://apache.org/xml/features/nonvalidating/load-dtd-grammar", false);
            DocumentBuilder db = dbf.newDocumentBuilder();
            org.w3c.dom.Document doc = db.newDocument();

            HWPFDocument document = new HWPFDocument(fis);
            List<Picture> pictureList = document.getPicturesTable().getAllPictures();
            List<String> base64PicList = new ArrayList<>();
            for (Picture picture : pictureList) {
                byte[] bytes = picture.getContent();
                String base64Pic = Base64.getEncoder().encodeToString(bytes);
                base64PicList.add(base64Pic);
            }

            //TODO 转换为html文本
            WordToHtmlConverter converter = new WordToHtmlConverter(doc);
            converter.processDocument(document);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            //TODO 添加转换配置
            Properties properties = new Properties();
            properties.put(OutputKeys.ENCODING,"UTF-8");
            properties.put(OutputKeys.INDENT,"yes");
            properties.put(OutputKeys.METHOD,"html");
            transformer.setOutputProperties(properties);
            transformer.transform(new DOMSource(doc),new StreamResult(outputStream));
            content = outputStream.toString("UTF-8");
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
        return content;
    }

}