package com.ruifox.collect.util;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public class Sleeper {

    // NOTE 每次sleep都要try-catch。。。太烦了。。。抽了一个类来做这个
    public static void sleep(long time, TimeUnit timeUnit) {
        long sleepTime = timeUnit.toMillis(time);
        try {
            Thread.sleep(sleepTime);
        } catch (InterruptedException e) {
            log.error("不是吧？我没写打断啊？，怎么被打断了？？？woc！");
        }
    }
}
