package com.ruifox.collect.util;

import com.ruifox.collect.module.entity.CollectTask;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

@Slf4j
public class ElementUtil {

    // TODO 删除script、style等元素
    public static void deleteNoContent(Document document) {
        try {
            document.select("script").remove();
            document.select("style").remove();
            document.select("input").remove();
            document.select("head").remove();
        } catch (Exception e) {
            log.error("删除正文中无用标签失败，原因："+e.getMessage());
        }
    }

    //删除正文中a标签，且该标签所对应的是原网站的html
    public static void deleteHtmlA(Document document, CollectTask collectTask){
        Elements select = document.select("a");
        for(Element e : select) {
            String attr = e.attr("href");
            if(attr.contains(collectTask.getHost()) && attr.contains(".htm")) {
                e.removeAttr("href");
            }
        }
    }

    // TODO 替换html、head、body标签，并返回字符串
    public static String getContentRemoveHtml(Document document) {
        String body = document.body().toString();
        if (body != null && !body.isEmpty()) {
            body = body.replace("<body>", "");
            body = body.replace("</body>", "");
            body = body.replace("<head>", "");
            body = body.replace("</head>", "");
            body = body.replace("<html>", "");
            body = body.replace("</html>", "");
            body = body.replace("<form","<div");
            body = body.replace("</form>","</div>");
            body = body.replace("<section","<div");
            body = body.replace("</section>","</div>");
            body = body.trim();
        }
        return body;
    }

    public static void modifyFormat(Document contextDom){
        Elements divElements = contextDom.select("div");
        for(Element divElement : divElements){
            String style = "font-size: 16px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2;";
            if(divElement.attr("style").toLowerCase().contains("text-align: center;"))
            {
                style = "text-align: center; " + style;
            }
            divElement.attr("style",style);
        }

        Elements spanElements = contextDom.select("span");
        for(Element spanElement : spanElements){
            String style = "font-size: 16px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2;";
            if(spanElement.attr("style").toLowerCase().contains("text-align: center;"))
            {
                style = "text-align: center; " + style;
            }
            spanElement.attr("style",style);
        }

        Elements pElements = contextDom.select("p");
        for(Element pElem : pElements) {
            String style = "font-size: 16px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2;";
            if(pElem.attr("style").toLowerCase().contains("text-align: center;"))
            {
                style = "text-align: center; " + style;
            }
            pElem.attr("style",style);
        }

        Elements imgElements = contextDom.select("img");
        for(Element imgElement : imgElements){
            String style = imgElement.attr("style");
            style = style + " display: block; margin-left: auto; margin-right: auto;";
            imgElement.attr("style",style);
        }
    }


    /**
     * 从给定的Element中移除所有匹配指定选择器的子元素。
     * @param element 要处理的Element对象
     * @param selector CSS选择器，用于匹配需要移除的子元素
     * @return 处理后的Element，移除了所有匹配选择器的子元素
     */
    public static Element removeElementsBySelector(Element element, String selector) {
        try {
            Elements elementsToRemove = element.select(selector);
            for (Element e : elementsToRemove) {
                e.remove();
            }
            return element;
        } catch (Exception e) {
            log.info("移除元素失败,返回初始元素!");
            return element;
        }
    }

    /**
     * 查找Element的最近祖先元素，该祖先元素需匹配给定的选择器。
     * @param startElement 起始Element，从该Element开始向上查找
     * @param selector 选择器字符串，可以是类名（".className"）、ID（"#idName"）或标签名（"tagName"）
     * @return 匹配选择器的第一个祖先Element，如果未找到则返回null
     */
    public static Element findClosestAncestor(Element startElement, String selector) {
        if (startElement == null || selector == null || selector.isEmpty()) {
            return null;
        }

        Element currentElement = startElement;
        while ((currentElement = currentElement.parent()) != null) {
            if (currentElement.is(selector)) {
                return currentElement;
            }
        }

        return null; // 未找到匹配的祖先元素
    }

    // TODO 删除空元素 同时保留附件、图片、视频等。。。(有其他的后续加)
    public static void deleteEmptyElement(Element element,String css) {
        try {
            for (Element el : element.select(css)) {
                Elements elementImg;
                Elements elementA;
                Elements elementVideo;
                try {
                    elementImg = el.select("img");
                } catch (Exception e) {
                    elementImg = null;
                }

                try{
                    elementA = el.select("a");
                }catch (Exception e) {
                    log.info("解析a失败");
                    elementA = null;
                }

                try{
                    elementVideo = el.select("video");
                }catch (Exception e){
                    log.info("解析video失败");
                    elementVideo = null;
                }
                if ((elementImg != null && !elementImg.isEmpty())||(elementA != null && !elementA.isEmpty())||(elementVideo != null && !elementVideo.isEmpty())) {
                    continue;
                }

                if (el.text().replace(" ","").replace("\u00A0","").isEmpty()) {
                    el.remove();
                }
            }
        } catch (Exception e) {
            log.info("解析失败");
        }
    }

    public static void deleteEmptyElement(Document document,String css) {
        try {
            for (Element el : document.select(css)) {
                Elements elementImg;
                Elements elementA;
                Elements elementVideo;
                try {
                    elementImg = el.select("img");
                } catch (Exception e) {
                    elementImg = null;
                }

                try{
                    elementA = el.select("a");
                }catch (Exception e) {
                    log.info("解析a失败");
                    elementA = null;
                }

                try{
                    elementVideo = el.select("video");
                }catch (Exception e){
                    log.info("解析video失败");
                    elementVideo = null;
                }

                if ((elementImg != null && !elementImg.isEmpty())||(elementA != null && !elementA.isEmpty())||(elementVideo != null && !elementVideo.isEmpty())) {
                    continue;
                }

                if (el.text().replace(" ","").replace("\u00A0","").isEmpty()) {
                    el.remove();
                }
            }
        } catch (Exception e) {
            log.info("解析失败");
        }
    }



    // TODO 修改元素的指定属性值
    public static void updateElementAttribute(Element element, String attr, String replaceStr) {
        try {
            element.attr(attr, replaceStr);
        } catch (Exception e) {
            log.info("修改 " + attr + " 属性为 "+ replaceStr +" 失败");
        }
    }

    public static void deleteElementsNotWithImg(Document document,String css) {
        try {
            for (Element el : document.select(css)) {
                Elements elementImg;
                Elements elementA;
                Elements elementVideo;
                try {
                    elementImg = el.select("img");
                } catch (Exception e) {
                    elementImg = null;
                }

                try{
                    elementA = el.select("a");
                }catch (Exception e) {
                    log.info("解析a失败");
                    elementA = null;
                }

                try{
                    elementVideo = el.select("video");
                }catch (Exception e){
                    log.info("解析video失败");
                    elementVideo = null;
                }

                if ((elementImg != null && !elementImg.isEmpty())||(elementA != null && !elementA.isEmpty())||(elementVideo != null && !elementVideo.isEmpty())) {
                    continue;
                }
                el.remove();
            }
        } catch (Exception e) {
            log.info("解析失败");
        }
    }
}