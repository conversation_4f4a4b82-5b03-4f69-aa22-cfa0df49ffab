package com.ruifox.collect.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import javax.net.ssl.SSLContext;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.*;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 基于 httpclient 4.5+版本的 HttpClient工具类
 */
@Slf4j
public class HttpClientUtils {

    // TODO 将请求url中参数部分重新编码
    public static String ReEncodeHttpUrl(String url) {
        String urlString = url;
        if(StringUtils.isNotBlank(url) && url.contains("?")){
            try {
                URL urlT = new URL(urlString);
                String query = urlT.getQuery();
                if (query != null) {
                    // 对参数部分进行编码
                    String encodedQuery = "";
                    String[] params = query.split("&");
                    for (String param : params) {
                        String[] keyValue = param.split("=");
                        if (keyValue.length == 2) {
                            String encodedKey = URLEncoder.encode(keyValue[0], "UTF-8");
                            String value = keyValue[1];
                            String encodedValue = "";
                            boolean needsEncoding = false;
                            for (char c : value.toCharArray()) {
                                if (Character.isISOControl(c) || Character.isWhitespace(c) || !Character.isDefined(c)) {
                                    needsEncoding = true;
                                    break;
                                }
                            }
                            if (needsEncoding) {
                                encodedValue = URLEncoder.encode(value, "UTF-8");
                            } else {
                                encodedValue = value;
                            }
                            encodedQuery += encodedKey + "=" + encodedValue + "&";
                        }
                    }
                    if (!encodedQuery.isEmpty()) {
                        encodedQuery = encodedQuery.substring(0, encodedQuery.length() - 1);
                    }
                    // 重新构建 URL
                    urlString = urlT.getProtocol() + "://" + urlT.getHost() + urlT.getPath() + "?" + encodedQuery;
                }
            } catch(Exception e){
                log.info("请求url中参数部分重新编码失败，"+e.getMessage());
            }
        }
        return urlString;
    }

    // TODO 通过HttpClient获取页面源码
    public static String getPageSource(String url){
        String pageSource = "";
        try {
            // 从 Java 11 开始，可以使用新的 java.net.http 包中的 HttpClient
            //保证跟随重定向
            HttpClient client = HttpClient.newBuilder().followRedirects(HttpClient.Redirect.ALWAYS).build();
            // 创建 HttpRequest 对象
            HttpRequest httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();

            // 发送请求并获取响应
            HttpResponse<String> httpResponse = client.send(httpRequest, HttpResponse.BodyHandlers.ofString(StandardCharsets.UTF_8));

            // 获取响应状态码
            int code = httpResponse.statusCode();
            if (code == 200) {
                // 获取响应体
                pageSource = httpResponse.body();
            } else {
                throw new RuntimeException("响应状态码：" + code);
            }
        } catch (Exception e) {
            log.info("通过url获取页面源码失败，"+e.getMessage());
        }
        return pageSource;
    }


    private static final CloseableHttpClient httpClient;
    public static final String CHARSET = "UTF-8";

    // TODO 采用静态代码块，初始化超时时间配置，再根据配置生成默认httpClient对象
    static {
        RequestConfig config = RequestConfig.custom().setConnectTimeout(30000).setSocketTimeout(15000).build();
        httpClient = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
    }

    public static String doGet(String url, Map<String, String> params) {
        return doGet(url, params, CHARSET);
    }

    public static String doGetSSL(String url, Map<String, String> params) {
        return doGetSSL(url, params, CHARSET);
    }

    public static String doPost(String url, Map<String, String> params) throws IOException {
        return doPost(url, params, CHARSET);
    }

    /**
     * HTTP Get 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 页面内容
     */
    public static String doGet(String url, Map<String, String> params, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        try {
            if (params != null && !params.isEmpty()) {
                List<NameValuePair> pairs = new ArrayList<>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
                // 将请求参数和url进行拼接
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
            }
            HttpGet httpGet = new HttpGet(url);
            // NOTE HttpClient默认情况下会自动跟随重定向（最多自动重定向 20 次，可调整）。
            CloseableHttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpGet.abort();
                throw new RuntimeException("HttpClient error status code: " + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            response.close();
            return result;
        } catch (Exception e) {
            log.info("HttpClient发送get请求失败，"+e.getMessage());
        }
        return null;
    }

    /**
     * HTTPS Get 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 页面内容
     */
    public static String doGetSSL(String url, Map<String, String> params, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        try {
            if (params != null && !params.isEmpty()) {
                List<NameValuePair> pairs = new ArrayList<>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
            }
            HttpGet httpGet = new HttpGet(url);

            // TODO 注意这里获取https内容，使用了忽略证书的方式，当然还有其他的方式来获取https内容
            CloseableHttpClient httpsClient = HttpClientUtils.createSSLClientDefaultAllowRedirects();

            CloseableHttpResponse response = httpsClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpGet.abort();
                throw new RuntimeException("HttpClient error status code: " + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            response.close();
            return result;
        } catch (Exception e) {
            log.info("HttpClient发送get请求失败，"+e.getMessage());
        }
        return null;
    }

    /**
     * HTTP Post 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 页面内容
     * @throws IOException
     */
    public static String doPost(String url, Map<String, String> params, String charset) throws IOException {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        List<NameValuePair> pairs = null;
        if (params != null && !params.isEmpty()) {
            pairs = new ArrayList<NameValuePair>(params.size());
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String value = entry.getValue();
                if (value != null) {
                    pairs.add(new BasicNameValuePair(entry.getKey(), value));
                }
            }
        }
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000) // 设置连接超时时间为5秒钟
                .setSocketTimeout(120000) // 设置读取数据超时时间为2分钟
                .build();
        httpPost.setConfig(requestConfig);
        if (pairs != null && pairs.size() > 0) {
            httpPost.setEntity(new UrlEncodedFormEntity(pairs, CHARSET));
        }
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpPost.abort();
                throw new RuntimeException("HttpClient error status code: " + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            return result;
        } catch (ParseException e) {
            log.info("HttpClient发送post请求失败，"+e.getMessage());
        } finally {
            if (response != null)
                response.close();
        }
        return null;
    }

    /**
     * 创建忽略整数验证的 CloseableHttpClient 对象，设置允许重定向
     *
     * @return
     */
    public static CloseableHttpClient createSSLClientDefaultAllowRedirects() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                // 信任所有
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            // 设置重定向策略
            LaxRedirectStrategy redirectStrategy = new LaxRedirectStrategy();
            RequestConfig.Builder requestConfigBuilder = RequestConfig.custom().setRedirectsEnabled(true).setCircularRedirectsAllowed(true);
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(sslsf)
                    .setRedirectStrategy(redirectStrategy)
                    .setDefaultRequestConfig(requestConfigBuilder.build())
                    .build();

            return httpClient;
        } catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e) {
            log.info("创建CloseableHttpClient忽略SSL失败，"+e.getMessage());
        }
        return HttpClients.createDefault();
    }

    /**
     * HTTPS Get 获取 HttpEntity
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return HttpEntity 对象
     */
    public static HttpEntity doGetSSLAsEntity(String url, Map<String, String> params, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        try {
            if (params != null && !params.isEmpty()) {
                List<NameValuePair> pairs = new ArrayList<>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
            }
            HttpGet httpGet = null;
            try {
                httpGet = new HttpGet(url);
            } catch (IllegalArgumentException e) {
                throw new RuntimeException(e);
            }

            CloseableHttpClient httpsClient = HttpClientUtils.createSSLClientDefaultAllowRedirects();
            CloseableHttpResponse response = httpsClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpGet.abort();
                throw new RuntimeException("HttpClient error status code: " + statusCode);
            }

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                throw new RuntimeException("No content in the HTTP response.");
            }

            return entity; // 直接返回 HttpEntity，不再将其转换为字符串
        } catch (Exception e) {
            log.info("HttpClient发送get请求获取Entity失败，"+e.getMessage());
        }
        return null;
    }

    /**
     * Http、Https多种情况分发
     *
     * @param originalUrl 源网络地址
     * @param params      请求的参数
     * @param charset     编码格式
     * @return HttpEntity 对象
     * @throws IOException
     */
    public static HttpEntity getEntityWithAutoRedirectProtocol(String originalUrl, Map<String, String> params, String charset) throws IOException {
        URL url = new URL(originalUrl);
        String protocol = url.getProtocol();
        String alternativeProtocol = protocol.equals("http") ? "https" : "http";
        String alternativeUrl = alternativeProtocol + ":" + url.getHost() + url.getFile();

        HttpEntity entity = doGetSSLAsEntity(originalUrl, params, charset);
        if (entity == null) {
            // 如果原协议下载失败，则尝试使用另一种协议
            entity = doGetSSLAsEntity(alternativeUrl, params, charset);
        }

        return entity;
    }

    // NOTE 这之后的方法本来是处理从URL下载图片、文件、视频的，但程序逻辑修改了，下载文件全都在FileDownloader里，目前这几个方法都没用到

    // 下载图片
    public static void downloadImage(String imageUrl,String savePath) throws IOException {
        HttpEntity entity = getEntityWithAutoRedirectProtocol(imageUrl, null, "utf-8");
        File outputFile = new File(savePath);
        try (FileOutputStream out = new FileOutputStream(outputFile)) {
            entity.writeTo(out);
        } finally {
            EntityUtils.consume(entity);
        }
    }

    // 下载图片并将png改jpg
    public static void downloadAndConvertPngToJpg(String imageUrl, String savePath) throws IOException {
        HttpEntity entity = getEntityWithAutoRedirectProtocol(imageUrl, null, "utf-8");

        // 创建临时文件存储原始下载的 PNG 图片
        File tempPngFile = File.createTempFile("temp_image_", ".png");
        try (FileOutputStream out = new FileOutputStream(tempPngFile)) {
            entity.writeTo(out);
        } finally {
            EntityUtils.consume(entity);
        }

        // 计算 PNG 备份文件名，与 JPG 文件名保持一致但扩展名改为 .png
        String backupPngPath = FilenameUtils.removeExtension(savePath) + ".png";

        // 将临时 PNG 文件复制到备份路径
        Files.copy(tempPngFile.toPath(), Paths.get(backupPngPath), StandardCopyOption.REPLACE_EXISTING);

        // 使用参考方法将 PNG 转换为 JPG 并保存到指定路径
        png2jpg(tempPngFile.getAbsolutePath(), savePath);

        // 删除临时文件
        tempPngFile.delete();
    }

    // 修改参考方法名称，明确其功能为 PNG 转 JPG
    public static void png2jpg(String pngPath, String jpgPath) {
        try {
            BufferedImage png = ImageIO.read(new File(pngPath));
            BufferedImage jpg = new BufferedImage(png.getWidth(), png.getHeight(), BufferedImage.TYPE_INT_RGB);
            jpg.createGraphics().drawImage(png, 0, 0, Color.white, null);
            ImageIO.write(jpg, "jpg", new File(jpgPath));
        } catch (Exception e) {
            log.info("png转jpg失败，"+e.getMessage());
        }
    }

    // 从url获取图片后缀
    public static String getImageTypeFromUrl(String url) {
        // 更新正则表达式以匹配 PNG, JPEG, 以及 GIF 扩展名
        Pattern pattern = Pattern.compile("\\.(png|jpe?g|gif)$", Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            return matcher.group(1).toLowerCase();
        } else {
            return null; // 扩展名无法确定或不是 PNG/JPG/GIF
        }
    }

    // 假设你有一个类似downloadAndConvertPngToJpg的函数，修改为处理GIF
    public static void downloadAndConvertGifToJpg(String imageUrl, String savePath) throws IOException {
        HttpEntity entity = getEntityWithAutoRedirectProtocol(imageUrl, null, "utf-8");
        // 创建临时文件存储原始下载的 GIF 图片
        File tempGifFile = File.createTempFile("temp_image_", ".gif");
        try (FileOutputStream out = new FileOutputStream(tempGifFile)) {
            entity.writeTo(out);
        } finally {
            EntityUtils.consume(entity);
        }

        // 计算 GIF 备份文件名，与 JPG 文件名保持一致但扩展名改为 .gif
        String backupGifPath = FilenameUtils.removeExtension(savePath) + ".gif";

        // 将临时 GIF 文件复制到备份路径
        Files.copy(tempGifFile.toPath(), Paths.get(backupGifPath), StandardCopyOption.REPLACE_EXISTING);

        // 使用新定义的gif转jpg方法进行转换
        gif2jpg(tempGifFile.getAbsolutePath(), savePath);

        // 删除临时文件
        tempGifFile.delete();
    }

    // gif 转 jpg
    public static void gif2jpg(String gifPath, String jpgPath) {
        try {
            // 读取 GIF 图像
            ImageInputStream input = ImageIO.createImageInputStream(new File(gifPath));
            Iterator<ImageReader> readers = ImageIO.getImageReaders(input);
            if (!readers.hasNext()) {
                // FIXME 这个异常要处理掉
                throw new RuntimeException("Unsupported image format or not a GIF file.");
            }
            ImageReader reader = readers.next();
            reader.setInput(input);

            int numFrames = reader.getNumImages(true);
            BufferedImage firstFrame;
            if (numFrames > 1) {
                // GIF有多个帧，仅转换第一帧
                firstFrame = reader.read(0);
                System.out.println("警告：GIF文件包含多帧，仅转换了第一帧为JPG。");
            } else {
                firstFrame = reader.read(0);
            }

            BufferedImage jpgImage = new BufferedImage(firstFrame.getWidth(), firstFrame.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D g = jpgImage.createGraphics();
            g.drawImage(firstFrame, 0, 0, null);
            g.dispose();

            // 写入 JPG 文件
            ImageIO.write(jpgImage, "jpg", new File(jpgPath));

            // 关闭资源
            reader.dispose();
            input.close();
        } catch (IOException e) {
            log.info("gif转jpg失败，"+e.getMessage());
        }
    }

    // 视频下载
    public static boolean downVideo(String videoUrl, String downloadPath) {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        RandomAccessFile randomAccessFile = null;
        boolean re;
        try {

            URL url = new URL(videoUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestProperty("Range", "bytes=0-");
            connection.connect();
            if (connection.getResponseCode() / 100 != 2) {
                log.info("连接失败...");
                return false;
            }
            inputStream = connection.getInputStream();
            int downloaded = 0;
            int fileSize = connection.getContentLength();
            randomAccessFile = new RandomAccessFile(downloadPath, "rw");
            while (downloaded < fileSize) {
                byte[] buffer = null;
                if (fileSize - downloaded >= 1000000) {
                    buffer = new byte[1000000];
                } else {
                    buffer = new byte[fileSize - downloaded];
                }
                int read = -1;
                int currentDownload = 0;
                long startTime = System.currentTimeMillis();
                while (currentDownload < buffer.length) {
                    read = inputStream.read();
                    buffer[currentDownload++] = (byte) read;
                }
                long endTime = System.currentTimeMillis();
                double speed = 0.0;
                if (endTime - startTime > 0) {
                    speed = currentDownload / 1024.0 / ((double) (endTime - startTime) / 1000);
                }
                randomAccessFile.write(buffer);
                downloaded += currentDownload;
                randomAccessFile.seek(downloaded);
                System.out.printf(downloadPath+"下载了进度:%.2f%%,下载速度：%.1fkb/s(%.1fM/s)%n", downloaded * 1.0 / fileSize * 10000 / 100,
                        speed, speed / 1000);
            }
            re = true;
            return re;
        } catch (IOException e) {
            log.info("下载视频失败，"+e.getMessage());
            re = false;
            return re;
        } finally {
            try {
                connection.disconnect();
                inputStream.close();
                randomAccessFile.close();
            } catch (Exception e) {
                log.info("关闭连接失败，"+e.getMessage());
            }
        }
    }
}