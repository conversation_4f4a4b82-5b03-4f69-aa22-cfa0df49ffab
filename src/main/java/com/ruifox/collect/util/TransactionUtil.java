package com.ruifox.collect.util;

import com.ruifox.collect.system.ApplicationContextProvider;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

/**
 * Description: 编程式事务工具类
 **/
public class TransactionUtil {
    
//    @Autowired
//    private PlatformTransactionManager transactionManager;
    
    /**
     * 编程式事务处理
     * @param action 需要在事务中执行的操作
     * @param <T> 返回值类型
     * @return 返回值
     */
    public static <T> T executeInTransaction(TransactionalAction<T> action) {
        // FIXME 这里每次都要获取一下,开销较大,可以优化为静态变量...
        PlatformTransactionManager transactionManager = ApplicationContextProvider.getBeanByType(PlatformTransactionManager.class);
        
        TransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            System.out.println("事务开始.....");
            T result = action.execute();
            transactionManager.commit(status);
            System.out.println("执行完成,事务提交.....");
            return result;
        } catch (RuntimeException e) {
            transactionManager.rollback(status);
            System.err.println("出现异常, 事务回滚.....");
            throw new RuntimeException(e);
        }
    }

    /**
     * 事务操作接口 配合lambda表达式使用
     * 示例：
     * <br>
     * TransactionUtil.executeInTransaction(() -> { <br>
     *    // ...略  这里进行数据库的操作 <br>
     *    return null; <br>
     * }); <br>
     *
     * @param <T> 返回值类型
     */
    @FunctionalInterface
    public interface TransactionalAction<T> {
        T execute();
    }
}
