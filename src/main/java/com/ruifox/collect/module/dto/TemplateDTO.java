package com.ruifox.collect.module.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "采集模版DTO")
public class TemplateDTO {
    @ApiModelProperty(value = "解析模板名称",required = true)
    private String name;
    @ApiModelProperty(value = "下一页按钮位置",required = true)
    private String nextButtonLoc;
    @ApiModelProperty(value = "下一页按钮名称",required = true)
    private String nextButtonName;
    @ApiModelProperty(value = "列表项标签",required = true)
    private String listItemTag;
    @ApiModelProperty(value = "列表项中可点击元素的位置",required = true)
    private String listItemClickLoc;
    @ApiModelProperty(value = "列表项/外链字段解析模版",required = true)
    private String listItemFieldLoc;
    @ApiModelProperty(value = "详情项标签",required = true)
    private String independentDetailTag;
    @ApiModelProperty(value = "独立详情页字段解析模版",required = true)
    private String independentDetailFieldLoc;
    @ApiModelProperty(value = "正文附件位置",required = true)
    private String contentALoc;
    @ApiModelProperty(value = "正文图片位置",required = true)
    private String contentImgLoc;
    @ApiModelProperty(value = "正文视频位置",required = true)
    private String contentVideoLoc;
    @ApiModelProperty(value = "通用json字符串",required = false)
    private String common;
}
