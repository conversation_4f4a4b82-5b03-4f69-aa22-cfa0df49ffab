package com.ruifox.collect.module.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "采集任务DTO")
public class CollectTaskDTO {
    @ApiModelProperty(value = "目标URL", example = "xxx", required = true)
    private String targetUrl;
    @ApiModelProperty(value = "采集总页数", example = "0", required = false)
    private int pageSum;
    @ApiModelProperty(value = "任务顺序", example = "10", required = false)
    private String sort;
    @ApiModelProperty(value = "特殊处理类型", example = "0", required = false)
    private String specialPage;
    @ApiModelProperty(value = "类型ID", example = "1", required = true)
    private int typeId;
    @ApiModelProperty(value = "对象ID", example = "1", required = true)
    private int objectId;
    @ApiModelProperty(value = "模版ID", example = "999", required = true)
    private int templateId;
    @ApiModelProperty(value = "cookieID", example = "0", required = false)
    private Integer cookieId;
    @ApiModelProperty(value = "页面加载等待时间", example = "6", required = false)
    private int timeOut;
    @ApiModelProperty(value = "待导入新栏目名称", example = "xxx", required = false)
    private String importCategoryName;
    @ApiModelProperty(value = "待导入新栏目ID", example = "999", required = false)
    private int importCategoryId;
}