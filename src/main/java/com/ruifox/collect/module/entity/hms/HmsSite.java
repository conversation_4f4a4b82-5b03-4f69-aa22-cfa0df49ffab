package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("hms_site")
public class HmsSite {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("status")
    private int status;

    @TableField("name")
    private String name;

    @TableField("dirname")
    private String dirname;

    @TableField("domain")
    private String domain;

    @TableField("static_domain")
    private String staticDomain;

    @TableField("title")
    private String title;

    @TableField("keywords")
    private String keywords;

    @TableField("description")
    private String description;

    @TableField("default_style")
    private String defaultStyle;

    @TableField("template")
    private String template;

    @TableField("setting")
    private String setting;

    @TableField("copyright")
    private String copyright;

    @TableField("logo")
    private String logo;

    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @TableField("seo")
    private String seo;

    @TableField("created_at")
    private LocalDateTime createdAt;
}