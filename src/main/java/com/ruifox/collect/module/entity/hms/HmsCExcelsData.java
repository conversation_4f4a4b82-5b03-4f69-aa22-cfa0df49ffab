package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_c_excels_data")
@Builder
public class HmsCExcelsData {
    @TableId(type = IdType.AUTO)
    private Long did;

    @ApiModelProperty("申请号")
    private String applicantCode;
    @ApiModelProperty("名称")
    private String title;
    @ApiModelProperty("申请人")
    private String applicant;
    @ApiModelProperty("专利类型")
    private String patentType;
    @ApiModelProperty("发明人")
    private String inventor;
    @ApiModelProperty("申请日期")
    private int applicantDate;
    @ApiModelProperty("申请人所在国家")
    private String inventorArea;
    @ApiModelProperty("代理人")
    private String agent;
    @ApiModelProperty("代理机构")
    private String agency;
    @ApiModelProperty("公开号")
    private String publicAccount;
    @ApiModelProperty(value = "公开日期", example = "0")
    private int publicDate;
    @ApiModelProperty("IPC分类")
    private String publicIpcType;
    @ApiModelProperty("摘要")
    @TableField("abstract")
    private String theAbstract;
    @ApiModelProperty("??")
    private String zView;
    @ApiModelProperty("文章/正文")
    private String aArticle;
    @ApiModelProperty(value = "摘要附图", example = "<p><img src=\"http://hospital-cqmu-foxhcs.foxtest.net/oss/********/*********.jpg\" alt=\"\" /></p>")
    private String zyPic;
    @ApiModelProperty("主权利要求")
    private String zQlyq;
    @ApiModelProperty("外观设计洛迦诺分类号(外观设计专利独有)")
    private String wClass;
    @ApiModelProperty("著录项目")
    private String zDirectory;
    @ApiModelProperty("CPC分类")
    private String cpcType;
    @ApiModelProperty("公开号(授权)")
    private String sqPublicNum;
    @ApiModelProperty("公开日期（授权）")
    private String sqPublicDate;
    @ApiModelProperty("申请人（授权）")
    private String sqApplicant;
    @ApiModelProperty("发明人（授权）")
    private String sqInventor;
    @ApiModelProperty("IPC分类（授权）")
    private String sqIpcType;
    @ApiModelProperty("发明名称（授权）")
    private String sqInventionName;
    @ApiModelProperty("摘要（授权）")
    private String sqAbstract;
    @ApiModelProperty("？？")
    private String briefDescription;
    @ApiModelProperty("封面")
    private String thumb;
    @ApiModelProperty("拓展(pdf)-手加目前")
    private String zlPdf;

}
