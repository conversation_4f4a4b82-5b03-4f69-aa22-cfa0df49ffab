package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("hms_c_mans_data")
public class HmsCMansData {

    // FIXME 可能这里的ID不是自增
    @TableId(value = "did", type = IdType.AUTO)
    private Long did;

    @TableField("title")
    private String title;
    @TableField("thumb")
    private String thumb;
    @TableField("depart")
    private String depart;
    @TableField("doc_position")
    private String docPosition;
    @TableField("edu_position")
    private String eduPosition;
    @TableField("edu_post")
    private String eduPost;
    @TableField("hos_position")
    private String hosPosition;
    @TableField("level")
    private String level;
    @TableField("political_visage")
    private String politicalVisage;
    @TableField("honor")
    private String honor;
    @TableField("goodat")
    private String goodat;
    @TableField("content")
    private String content;
    @TableField("content_image")
    private String contentImage;
    @TableField("content_link")
    private String contentLink;
    @TableField("information")
    private String information;
    @TableField("views")
    private Integer views;
    @TableField("comefrom")
    private String comefrom;
}