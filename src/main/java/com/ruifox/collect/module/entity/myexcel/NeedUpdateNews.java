package com.ruifox.collect.module.entity.myexcel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class NeedUpdateNews {

    @ExcelProperty("导入栏目")
    private String originAndTarget;

    @ExcelProperty("数据Id")
    private String dataIds;
}
