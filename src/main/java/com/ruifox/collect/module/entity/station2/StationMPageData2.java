package com.ruifox.collect.module.entity.station2;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
* 
* @TableName resource_data_page
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("resource_data_page")
public class StationMPageData2 implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer DataId;

    private String uuid;

    private Integer createUserId;

    private Integer updateUserId;

    private Double createTime;

    private Double updateTime;

    private Integer state;

    private String title;

    private String comefrom;

    private String description;

    private String content;


    private Integer isLink;

    private String tagList;


}
