package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_admin")
public class HmsAdmin {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String account;
    private String pwd;
    private String encrypt;
    private String realName;
    private String phone;
    private String email;
    private String openid;
    private String avatar;
    private String introduction;
    private String roles;
    private String lastIp;
    private Integer isAuth;
    private Integer status;
    private Integer lastUpPass;
    private Timestamp deletedAt;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    private Integer isLogin;
    private String info;

}
