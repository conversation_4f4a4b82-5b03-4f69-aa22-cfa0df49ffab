package com.ruifox.collect.module.entity.resource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 
* @TableName folder_resource
*/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("folder_resource")
public class FolderResource implements Serializable {

    /**
    * 主键id
    */
    @ApiModelProperty("主键id")
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
    * 用户id
    */
    @ApiModelProperty("用户id")
    private Integer userId;
    /**
    * 稿件夹id
    */
    @ApiModelProperty("稿件夹id")
    private Integer folderId;
    /**
    * 模型id
    */
    @ApiModelProperty("模型id")
    private Integer modelId;
    /**
    * 资源id
    */
    @ApiModelProperty("资源id")
    private Long resourceId;
    /**
    * 当前版本
    */
    @ApiModelProperty("当前版本")
    private Integer version;
    /**
    * 历史版本
    */

    @ApiModelProperty("历史版本")
    private String versionsInfo;
    /**
    * 发布信息
    */
    @ApiModelProperty("发布信息")
    private String publishInfo;
    /**
    * 排序值
    */
    @ApiModelProperty("排序值")
    private Long sort;
    /**
    * 排序值
    */
    @ApiModelProperty("排序值")
    private Integer listOrder;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Double createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private Double updateTime;
    /**
    * 状态
    */
    @ApiModelProperty("状态")
    private Integer state;
    /**
    * 是否删除
    */
    @ApiModelProperty("是否删除")
    private Integer isDeleted;



}
