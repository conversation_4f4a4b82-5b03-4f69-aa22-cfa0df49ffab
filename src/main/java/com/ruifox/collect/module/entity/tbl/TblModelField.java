package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_model_field")
public class TblModelField {

    @TableId(value = "fieldid", type = IdType.AUTO)
    private Integer fieldid;

    @TableField("modelid")
    private Integer modelid;

    @TableField("siteid")
    private Integer siteid;

    @TableField("field")
    private String field;

    @TableField("name")
    private String name;

    @TableField("tips")
    private String tips;

    @TableField("css")
    private String css;

    @TableField("minlength")
    private Integer minlength;

    @TableField("maxlength")
    private Integer maxlength;

    @TableField("pattern")
    private String pattern;

    @TableField("errortips")
    private String errortips;

    @TableField("formtype")
    private String formtype;

    @TableField("setting")
    private String setting;

    @TableField("formattribute")
    private String formattribute;

    @TableField("unsetgroupids")
    private String unsetgroupids;

    @TableField("unsetroleids")
    private String unsetroleids;

    @TableField("iscore")
    private Integer iscore;

    @TableField("issystem")
    private Integer issystem;

    @TableField("isunique")
    private Integer isunique;

    @TableField("isbase")
    private String isbase;

    @TableField("issearch")
    private Integer issearch;

    @TableField("isshare")
    private Integer isshare;

    @TableField("isadd")
    private Integer isadd;

    @TableField("isfulltext")
    private Integer isfulltext;

    @TableField("isposition")
    private Integer isposition;

    @TableField("isfilter")
    private Integer isfilter;

    @TableField("listorder")
    private Integer listorder;

    @TableField("disabled")
    private Integer disabled;

    @TableField("isomnipotent")
    private Integer isomnipotent;
}