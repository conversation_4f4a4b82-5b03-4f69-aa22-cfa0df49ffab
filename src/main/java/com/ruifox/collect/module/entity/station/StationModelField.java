package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_model_field")
public class StationModelField {
    @TableId(value = "id", type = IdType.AUTO)
    private int id;

    private int modelId;
    private int relyFieldId;
    private String field;
    private String name;
    private String title;
    private String tips;
    private int isSystem;
    private int enable;
    private int isRequired;
    private int isScan;
    private int isForm;
    private int isShow;
    private int isShowControl;
    private int isSearch;
    private int isAutoComplete;
    private int isOrder;
    private int sort;
    private String component;
    private String setting;
    private int minLength;
    private int maxLength;
    private String defaultValue;
    private String tableType;
    private Double createTime;
    private Double updateTime;
}
