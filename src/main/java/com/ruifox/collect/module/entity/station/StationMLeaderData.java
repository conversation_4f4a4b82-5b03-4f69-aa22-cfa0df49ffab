package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_leader_data")
public class StationMLeaderData {
    @TableId(value = "data_id", type = IdType.AUTO)
    private Long dataId;

    private String uuid;
    private String title;
    private String thumb;
    private Integer partyPosition;
    private Integer leaderPosition;
    private Integer eduPost;
    private Integer eduPosition;
    private Integer docPosition;
    private String tenure;
    private String relatedPosition;
    private String content;
    private String ignoreReason;
    private Double publishTime;
}
