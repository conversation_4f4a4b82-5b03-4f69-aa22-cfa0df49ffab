package com.ruifox.collect.module.entity.news;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("xxzrmyy_news_483")
public class NewsTable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private int collectTaskId;
    private String classification;
    private String targetUrl;
    private String pageId;
    private String sort;
    private String title;
    private String thumb;
    private String publishTime;
    private String comeFrom;
    private String author;
    private String views;
    private String description;
    private String content;
    private int flag;
    private String originCode;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
