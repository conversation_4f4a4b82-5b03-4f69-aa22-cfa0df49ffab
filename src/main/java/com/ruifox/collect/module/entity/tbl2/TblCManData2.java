package com.ruifox.collect.module.entity.tbl2;




import java.io.Serializable;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* 
* @TableName tbl_c_man_data
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_man_data")
public class TblCManData2 implements Serializable {


    private Integer id;

    private String content;



    private Integer readpoint;

    private String groupidsView;

    private Integer paginationtype;

    private Integer maxcharperpage;

    private String template;

    private Integer paytype;

    private Integer allowComment;

    private String relation;

    private String paper;

    private String wordcard;

    private String hasgqorjs;

    private String qrimg;

    private String weibo;

    private String sex;

    private Date birth;

    private String phone;

    private String tel;

    private String email;

    private String degree;

    private String openid;

    private String position;

    // TODO 特定加的
    private String title;

    private String depart;

}
