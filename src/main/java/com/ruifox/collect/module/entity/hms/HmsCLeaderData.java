package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_c_leader_data")
public class HmsCLeaderData {
    @TableId(value = "did", type = IdType.AUTO)
    private Long did;

    private String title;
    private String thumb;
    private String partyPosition;
    private String leaderPosition;
    private String eduPost;
    private String eduPosition;
    private String docPosition;
    private String content;
    private String tenure;
    private String relatedPosition;
    private Integer views;
    private String comefrom;
}
