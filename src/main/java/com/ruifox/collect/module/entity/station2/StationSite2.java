package com.ruifox.collect.module.entity.station2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_site")
public class StationSite2 {
    @TableId(value = "id", type = IdType.AUTO)
    private int id;

    private String name;
    private String domain;
    private String uri;
    private String title;
    private String description;
    private String logo;
    private String themeColor;
    private String keywords;
    private String setting;
    private Integer isAsh;
    private Double createTime;
    private Double updateTime;
    private Integer state;
}
