package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_c_donor_volunteers_data")
@Builder
public class HmsCDonorVolunteersData {
    @TableId(type = IdType.AUTO)
    private Long did;

    private String title;
    private String sex;
    private String registered_birthplace;
    private String ethnic_group;
    private String career;
    private String address;
    private int birthday;
    private int taboo_day;
    private int registration_day;
    private int realization_day;
    private String biographic_sketch;
    private int flowers;
    private int song;
    private int candle;
    private int wine;
    private String thumb;
}
