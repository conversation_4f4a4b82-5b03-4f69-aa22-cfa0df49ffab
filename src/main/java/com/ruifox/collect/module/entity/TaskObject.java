package com.ruifox.collect.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("task_object")
public class TaskObject {
    @TableId(value = "id" ,type = IdType.AUTO)
    private Integer id;

    private String name;
    private String dataTableSuffix;
    private String fileTableSuffix;
    private String dataTableFields;
    private String fileTableFields;
}
