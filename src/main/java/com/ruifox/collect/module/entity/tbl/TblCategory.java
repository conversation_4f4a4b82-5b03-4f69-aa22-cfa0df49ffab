package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_category")
public class TblCategory {

    /**
     * 分类ID
     */
    @TableId(value = "catid", type = IdType.AUTO)
    private Integer catid;

    /**
     * 站点ID
     */
    private Integer siteid;

    /**
     * 调用其他栏目数据
     */
    private Integer relacat;

    /**
     * 模块
     */
    private String module;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 模型ID
     */
    private Integer modelid;

    /**
     * 父级ID
     */
    private Integer parentid;

    /**
     * 所有父级ID
     */
    private String arrparentid;

    /**
     * 是否有子栏目
     */
    private Integer child;

    /**
     * 所有子栏目ID
     */
    private String arrchildid;

    /**
     * 栏目名称
     */
    private String catname;

    /**
     * 栏目英文名称
     */
    private String catnameen;

    /**
     * 样式
     */
    private String style;

    /**
     * 图片
     */
    private String image;

    /**
     * 描述
     */
    private String description;

    /**
     * 父目录
     */
    private String parentdir;

    /**
     * 栏目目录
     */
    private String catdir;

    /**
     * URL
     */
    private String url;

    /**
     * 项目数
     */
    private Integer items;

    /**
     * 点击数
     */
    private Integer hits;

    /**
     * 设置
     */
    private String setting;

    /**
     * 子栏目类名定义
     */
    private String classdef;

    /**
     * 类型
     */
    private String classes;

    /**
     * 字母
     */
    private String letter;

    /**
     * 排序
     */
    private Integer listorder;

    /**
     * 启用
     */
    private Integer enable;

    /**
     * 是否在菜单中显示
     */
    private Integer ismenu;

    /**
     * 是否生成HTML
     */
    private Integer sethtml;

    /**
     * 可用类型
     */
    @TableField("usable_type")
    private String usableType;

    /**
     * 附加内容
     */
    private String additional;

    /**
     * 评论类型ID
     */
    private Integer commenttypeid;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 是否是频道
     */
    private Integer ischnnel;

    /**
     * 栏目属性
     */
    private Integer isonly;
}