package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_video_data")
public class TblCVideoData {
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    private String content;
    private Integer readpoint;
    private String groupidsView;
    private Integer paginationtype;
    private Integer maxcharperpage;
    private String template;
    private Integer paytype;
    private Integer allowComment;
    private String relation;
    private String linkdepart;
    private String linkexpert;

/*    private String content;
    private String relation;
    private String linkdepart;
    private String linkexpert;
    private String title;
    private String thumb;
    private String keywords;
    private String description;
    private Integer endtime;
    private String comefrom;
    private String video;*/
}