package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_c_pages")
@Builder
public class HmsCPages {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long dataid;
    private Integer catid;
    private Integer username;
    private Integer status;
    private Integer state;
    private Integer publishTime;
    private Integer expireTime;
    private Integer inputTime;
    private Integer updateTime;
    private String url;
    private String islink;
    private Integer top;
    private Long listorder;
    private Integer sort;
    private Integer endOperator;
    private Integer isLocked;
}
