package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tbl_c_picture_data")
public class TblCPictureData {
//    @TableId(value = "id", type = IdType.AUTO)
//    private Integer id;
//
//    private String content;
//    private Integer paginationtype;
//    private Integer maxcharperpage;
//    private Integer paytype;
//    private String images;
//    private String title;
//    private String thumb;
//    private String keywords;
//    private String description;
//    private String comefrom;
@TableId(value = "id", type = IdType.INPUT)
private Integer id;

    private String content;
    private Integer readpoint;
    private String groupidsView;
    private Integer paginationtype;
    private Integer maxcharperpage;
    private String template;
    private Integer paytype;
    private Integer allowComment;
    private String relation;
    private String images;

}
