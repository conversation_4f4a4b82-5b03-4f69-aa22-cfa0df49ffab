package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_man_data")
public class TblCManDataHxey {

    /**
     * ID，对应主表 'tbl_c_man' 的ID。
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String content;

    private Integer readpoint;

    private String groupids_view;

    private Integer paginationtype;

    private Integer maxcharperpage;

    private String template;

    private Integer paytype;

    private Integer allow_comment;

    private String relation;

    private String paper;

    private String wordcard;

    private String hasgqorjs;

    private String qrimg;

    private String weibo;

    private String sex;

    private LocalDate birth;

    private String phone;

    private String tel;

    private String email;

    private String degree;

    private String openid;

    private String position;

    private String introduce;

    private String employment_time;
}