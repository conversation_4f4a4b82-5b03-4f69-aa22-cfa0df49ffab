package com.ruifox.collect.module.entity.tbl_special;



import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 
* @TableName tbl_special_content
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_special_content")
public class TblSpecialContent implements Serializable {

    private Integer id;

    private Integer specialid;

    private String title;

    private String style;

    private Integer typeid;

    private String thumb;

    private String keywords;

    private String comefrom;

    private String description;

    private String url;

    private String curl;

    private Integer listorder;

    private Integer sort;

    private Integer userid;

    private String username;

    private Integer inputtime;

    private Integer updatetime;

    private Integer searchid;

    private Integer state;

    private Integer islink;

    private Integer isdata;

    private Integer istop;

    private Integer isDepartment;


}
