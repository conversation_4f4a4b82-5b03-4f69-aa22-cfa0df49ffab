package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("hms_c_news_data")
public class HmsCNewsData {

    @TableId(value = "did",type = IdType.AUTO)
    @ApiModelProperty(value = "数据ID", example = "1")
    private Long did;

    @TableField("title")
    @ApiModelProperty(value = "标题", example = "新闻标题")
    private String title;
    @TableField("top_title")
    @ApiModelProperty(value = "上标题", example = "上一级标题")
    private String topTitle;
    @TableField("sub_title")
    @ApiModelProperty(value = "副标题", example = "副标题内容")
    private String subTitle;
    @TableField("original_title")
    @ApiModelProperty(value = "原标题", example = "原始新闻标题")
    private String originalTitle;
    @TableField("thumb")
    @ApiModelProperty(value = "缩略图路径", example = "http://example.com/thumb.jpg")
    private String thumb;
    @TableField("comefrom")
    @ApiModelProperty(value = "来源部门", example = "新闻部")
    private String comefrom;
    @TableField("author")
    @ApiModelProperty(value = "作者", example = "张三")
    private String author;
    @TableField("photographer")
    @ApiModelProperty(value = "摄影师", example = "李四")
    private String photographer;
    @TableField("related_expert")
    @ApiModelProperty(value = "关联专家", example = "王五")
    private String relatedExpert;
    @TableField("related_depart")
    @ApiModelProperty(value = "关联科室", example = "心血管科")
    private String relatedDepart;
    @TableField("content")
    @ApiModelProperty(value = "医生简介", example = "医生简介...")
    private String content;
    @TableField("description")
    @ApiModelProperty(value = "摘要", example = "摘要内容")
    private String description;
    @TableField("content_image")
    @ApiModelProperty(value = "内容图片JSON数据", notes = "JSON数组")
    private String contentImage;
    @TableField("content_link")
    @ApiModelProperty(value = "内容链接JSON数据", notes = "JSON数组")
    private String contentLink;
    @TableField("relevant_files")
    @ApiModelProperty(value = "相关附件JSON数据",notes = "JSON数组")
    private String relevantFiles;
    @TableField("views")
    @ApiModelProperty(value = "访问量", example = "1000")
    private Integer views;
    @TableField("editors")
    @ApiModelProperty(value = "编辑人员", example = "[赵六,钱七]")
    private String editors;
    @TableField("executive_editor")
    @ApiModelProperty(value = "责任编辑", example = "孙八")
    private String executiveEditor;
    @TableField("examine_article")
    @ApiModelProperty(value = "审稿信息", example = "已通过")
    private String examineArticle;
}