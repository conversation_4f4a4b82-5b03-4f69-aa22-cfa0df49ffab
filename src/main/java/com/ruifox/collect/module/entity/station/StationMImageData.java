package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_image_data")
public class StationMImageData {
    @TableId(value = "data_id", type = IdType.AUTO)
    private Integer dataId;

    private String uuid;
    private String author;
    private String photographer;
    private String images;
    private String comefrom;
    private String description;
    private String title;
    private Double publishTime;
}
