package com.ruifox.collect.module.entity.tbl_special;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 
* @TableName tbl_type
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_type")
public class TblType implements Serializable {

    private Integer typeid;

    private Integer siteid;

    private String module;

    private Integer modelid;

    private String name;

    private Integer parentid;

    private String typedir;

    private String url;

    private String template;

    private Integer listorder;

    private String description;

    private String setting;

}
