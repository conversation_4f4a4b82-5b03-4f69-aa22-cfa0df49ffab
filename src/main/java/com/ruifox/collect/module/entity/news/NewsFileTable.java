package com.ruifox.collect.module.entity.news;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("xxzrmyy_news_file_471")
public class NewsFileTable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String targetUrl;
    private String pageId;
    private String localUrl;
    private String originUrl;
    private int resCode;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
