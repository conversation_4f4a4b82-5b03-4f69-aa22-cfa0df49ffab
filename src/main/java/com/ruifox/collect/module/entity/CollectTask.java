package com.ruifox.collect.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("collect_task")
public class CollectTask {
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    private String targetUrl;
    private int pageSum;
    private String sort;
    private String specialPage;
    private int typeId;
    private int objectId;
    private int templateId;
    private Integer cookieId;
    private int timeOut;
    private String importCategoryName;
    private int importCategoryId;
    private String host;
    private int taskStatus;
    private String dataTableName;
    private String fileTableName;
    private String fileFolderName;
}
