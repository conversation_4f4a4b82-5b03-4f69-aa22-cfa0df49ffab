package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_category")
public class HmsCategory {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer siteId;
    private Integer pid;
    private Integer level;
    private String pString;
    private String url;
    private String name;
    private Integer isChannel;
    private Integer isHome;
    private Integer catHeaderId;
    private Integer type;
    private Integer modelId;
    private Integer isShow;
    private Integer isEnable;
    private Integer target;
    private String headertemplate;
    private String pagetemplate;
    private String channeltemplate;
    private String bodyclass;
    private String listtemplate;
    private String detailtemplate;
    private String formtemplate;
    private Integer sort;
    private String info;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    private String reading;
    private String subtitle;
    private String thumb;
    private String copyStr;
    private Integer status;
    private String seo;
}