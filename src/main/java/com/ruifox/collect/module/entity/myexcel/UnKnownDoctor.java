package com.ruifox.collect.module.entity.myexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnKnownDoctor {
    @ExcelProperty("姓名")
    private String doctorName;

    @ExcelProperty("部门")
    private String depart;

    @ExcelProperty("所在Excel表")
    private String excelName;
}
