package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("hms_model_field")
public class HmsModelField {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("model_id")
    private Integer modelId;

    @TableField("site_id")
    private Integer siteId;

    @TableField("field")
    private String field;

    @TableField("rely_field")
    private Integer relyField;

    @TableField("name")
    private String name;

    @TableField("tips")
    private String tips;

    @TableField("css")
    private String css;

    @TableField("form_attribute")
    private String formAttribute;

    @TableField("setting")
    private String setting;

    @TableField("is_scan")
    private Integer isScan;

    @TableField("is_system")
    private Integer isSystem;

    @TableField("sys_add")
    private Integer sysAdd;

    @TableField("is_search")
    private Integer isSearch;

    @TableField("is_order")
    private Integer isOrder;

    @TableField("is_share")
    private Integer isShare;

    @TableField("is_show")
    private Integer isShow;

    @TableField("is_form")
    private Integer isForm;

    @TableField("is_fulltext")
    private Integer isFulltext;

    @TableField("enable")
    private Integer enable;

    @TableField("sort")
    private Integer sort;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updatedAt;

//    @TableLogic
    private LocalDateTime deletedAt;

    @TableField("minlength")
    private Integer minLength;

    @TableField("maxlength")
    private Integer maxLength;

    @TableField("form_type")
    private String formType;

    @TableField("is_show_control")
    private Integer isShowControl;
}