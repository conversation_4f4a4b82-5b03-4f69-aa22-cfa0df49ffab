package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_teacher_data")
public class StationMTeacherData {
    @TableId(value = "data_id", type = IdType.AUTO)
    private Long dataId;
    private String uuid;
    private Double publishTime;
    private String title;
    private String school;
    private String currentLevel;
    private String researchDirection;
    private String email;
    private String thumb;
}
