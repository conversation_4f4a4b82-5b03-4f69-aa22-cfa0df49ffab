package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tbl_c_man_data")
public class TblCManDataXbq {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String content;
    private String paper;
    private String wordcard;
    private String isparty;
    private String qrimg;
    private String weibo;
    private String sex;
    private Integer birth;
    private String phone;
    private String tel;
    private String email;
    private String degree;
    private String openid;
    private String position;
    private String thumb;
    private String level;
    private String protit;
    private String depart;
    private String visittb;
    private String goodat;
    private String hisid;
    private String title;
    private String keywords;
    private Integer hasLink;
    private String eduPosition;
    private String docPosition;
    private String eduPost;
    private String hosPosition;
    private Integer words;
}
