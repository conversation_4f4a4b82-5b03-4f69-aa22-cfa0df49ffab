package com.ruifox.collect.module.entity.tbl2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 新闻主表
 *
 * @TableName tbl_c_news
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_news")
public class TblCNews2 implements Serializable {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 栏目id
     */
    private Integer catid;

    /**
     * 是否为链接
     */
    private Integer islink;

    /**
     * 是否由后台添加
     */
    private Integer sysadd;

    /**
     * 用户名 (关联用户表的ID)
     */
    private Integer username;

    /**
     * 添加时间
     */
    private Integer inputtime;

    /**
     * 更新时间
     */
    private Integer updatetime;

    /**
     * 状态 (1:正常)
     */
    private Integer status;

    /**
     * 状态位
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer listorder;

    /**
     * 排序方式
     */
    private Integer sort;

    /**
     * 是否置顶
     */
    private Integer istop;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 访问量
     */
    private Integer views;

    /**
     * 附表数据id
     */
    private Integer dataid;

}