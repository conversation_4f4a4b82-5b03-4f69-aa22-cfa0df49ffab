package com.ruifox.collect.module.entity.hos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hos_category")
public class HosCategory {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String categoryName;
    private String type;
    private String url;
    private String pic;
    private String contents;
    private String urls;
    private Integer parentId;
    private Integer level;
    private Integer addTime;
    private Integer updTime;
    private Integer isShow;
    private Integer sort;
}
