package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_man")
public class TblCMan2 {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer catid;
    private Integer sysadd;
    private String username;
    private Integer inputtime;
    private Integer updatetime;
    private Integer status;
    private Integer state;
    private Integer listorder;
    private Integer sort;
    private Integer istop;
    private String url;
    private Integer views;
    private String title;
    private String thumb;
    private String depart;
    private String positive;
    private String description;
    private String protit;
    @TableField("protit_tech")
    private String protitTech;

}
