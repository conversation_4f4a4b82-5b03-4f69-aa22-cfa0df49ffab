package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_news_data")
public class TblCNewsData {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String title;
    private String keywords;
    private String description;
    private String thumb;
    private String content;
    private String author;
    //    private String picture;
//    private String pictuer;
    private String comefrom;
    private String linkdepart;
    private String linkexpert;
    private String relation;
    private Integer paginationtype;
    private Integer maxcharperpage;
    private Integer paytype;
//    private Integer importMethod;
//    private String pdfContent;
//    private String subtitle;
}
