package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_newspaper_data")
public class StationMNewspaperData {
    @TableId(value = "data_id", type = IdType.AUTO)
    private Long dataId;

    private String uuid;
    private String comefrom;
    private String title;
    private Integer publishNums;
    private Integer publishYear;
    private Integer publishIndex;
    private String newspaper;
    private String articleList;
    private Double publishTime;
    private String thumb;
    private String content;
}
