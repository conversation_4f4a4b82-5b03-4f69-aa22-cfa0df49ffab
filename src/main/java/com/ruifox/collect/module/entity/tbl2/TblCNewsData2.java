package com.ruifox.collect.module.entity.tbl2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 新闻内容附表
 *
 * @TableName tbl_c_news_data
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_news_data")
public class TblCNewsData2 implements Serializable {

    /**
     * 对应主表的ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 描述
     */
    private String description;

    /**
     * 缩略图
     */
    private String thumb;

    /**
     * 内容
     */
    private String content;

    /**
     * 作者
     */
    private String author;

    /**
     * 图片 (?) - 根据字段名推测，可能与thumb有区别
     */
    private String pictuer;

    /**
     * 来源
     */
    private String comefrom;

    /**
     * 关联科室
     */
    private String linkdepart;

    /**
     * 关联专家
     */
    private String linkexpert;

    /**
     * 相关文章
     */
    private String relation;

    /**
     * 内容分页方式
     */
    private Integer paginationtype;

    /**
     * 每页最大字符数
     */
    private Integer maxcharperpage;

    /**
     * 付费类型
     */
    private Integer paytype;
}