package com.ruifox.collect.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("task_template")
public class TaskTemplate {
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    private String name;
    private String nextButtonLoc;
    private String nextButtonName;
    private String listItemTag;
    private String listItemClickLoc;
    private String listItemFieldLoc;
    private String independentDetailTag;
    private String independentDetailFieldLoc;
    private String contentALoc;
    private String contentImgLoc;
    private String contentVideoLoc;
    private String common;
}
