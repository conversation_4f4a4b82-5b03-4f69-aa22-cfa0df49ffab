package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_video_data")
public class StationMVideoData {
    @TableId(value = "data_id", type = IdType.AUTO)
    private Long dataId;

    private String uuid;
//    private Integer sortLevel;
    private String title;
    private String video;
    private String thumb;
    private String comefrom;
    private String description;
    private String content;
    private Double publishTime;

//    private String uuid;
//    private Integer createUserId;
//    private Integer updateUserId;
//    private Double createTime;
//    private Double updateTime;
//    private Boolean state;
//    private String title;
//    private String comefrom;
//    private String thumb;
//    private String description;
//    private String video;
//    private String content;
//    private String tagList;
}
