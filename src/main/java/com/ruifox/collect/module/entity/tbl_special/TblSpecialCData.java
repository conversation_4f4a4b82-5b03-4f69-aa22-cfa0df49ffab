package com.ruifox.collect.module.entity.tbl_special;



import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;



/**
* 
* @TableName tbl_special_c_data
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_special_c_data")
public class TblSpecialCData implements Serializable {


    private Integer id;

    private String author;

    private String content;

    private Integer paginationtype;

    private Integer maxcharperpage;

    private String style;

    private String showTemplate;


}
