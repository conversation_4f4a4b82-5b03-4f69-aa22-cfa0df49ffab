package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("hms_c_mans")
public class HmsCMans {

    // FIXME 可能不是自增
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("dataid")
    private Long dataid;
    @TableField("catid")
    private Integer catid;
    @TableField("username")
    private Integer username;
    @TableField("status")
    private Byte status;
    @TableField("state")
    private Integer state;
    @TableField("publish_time")
    private Integer publishTime;
    @TableField("expire_time")
    private Integer expireTime;
    @TableField("input_time")
    private Integer inputTime;
    @TableField("update_time")
    private Integer updateTime;
    @TableField("url")
    private String url;
    @TableField("content_num")
    private Integer contentNum;
    @TableField("content_image_num")
    private Integer contentImageNum;
    @TableField("content_link_num")
    private Integer contentLinkNum;
    @TableField("top")
    private Long top;
    @TableField("listorder")
    private Long listorder;
    @TableField("sort")
    private Integer sort;
    @TableField("old_catid")
    private String oldCatid;
    @TableField("end_operator")
    private Integer endOperator;
    @TableField("is_locked")
    private Boolean isLocked;
}
