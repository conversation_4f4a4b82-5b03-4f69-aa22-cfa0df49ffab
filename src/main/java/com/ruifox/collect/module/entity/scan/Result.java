package com.ruifox.collect.module.entity.scan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("website_scan_result_274")
public class Result {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String purl;
    private String targetUrl;
    private Integer responseCode;
    private String word;
    private String backLink;
    private Integer taskId;
    private Integer type;
}
