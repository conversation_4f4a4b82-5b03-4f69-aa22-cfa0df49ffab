package com.ruifox.collect.module.entity.station2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("resource_data_leader")
public class StationMLeaderData2 {
    @TableId(value = "id", type = IdType.AUTO)
    private Long dataId;
    private String uuid;
    private Integer createUserId;
    private Integer updateUserId;
    private Double createTime;
    private Double updateTime;
    private Integer state;
    private String title;
    private String thumb;
    private Integer eduPost;
    private Integer eduPosition;
    private Integer docPosition;
    private Integer partyPosition;
    private Integer leaderPosition;
    private String relatedPosition;
    private String tenure;
    private String content;
    private String ignoreReason;
    private String tagList;
}
