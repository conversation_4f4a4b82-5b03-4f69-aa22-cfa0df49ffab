package com.ruifox.collect.module.entity.station2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_category")
public class StationCategory2 {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer originId;
    private Integer siteId;
    private Integer pid;
    private String procdefId;
    private Integer level;
    private Integer sortLevel;
    private String parentString;
    private String uri;
    private String name;
    private String subTitle;
    private String reading;
    private String thumb;
    private String icon;
    private String navIcon;
    private String backImage;
    private String info;
    private String link;
    private Integer isChannel;
    private Integer isShow;
    private Integer modelId;
    private Integer headTemplate;
    private Integer footTemplate;
    private Integer pageTemplate;
    private Integer detailTemplate;
    private Integer type;
    private Double createTime;
    private Double updateTime;
    private Integer state;
}
