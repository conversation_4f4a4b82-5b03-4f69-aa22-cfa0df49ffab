package com.ruifox.collect.module.entity.tbl;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_man_data")
public class TblCManDataCdd5 {

    private Integer id;

    private String content;

    private String paper;

    private String wordcard;

    private String hasgqorjs;

    private String qrimg;

    private String weibo;

    private String sex;

    private LocalDate birth;

    private String phone;

    private String tel;

    private String email;

    private String degree;

    private String openid;

    private String position;

    private String thumb;

    private String level;

    private String protit;

    private String depart;

    private String visittb;

    private String goodat;

    private String hisid;

    private String title;

    private String keywords;
}