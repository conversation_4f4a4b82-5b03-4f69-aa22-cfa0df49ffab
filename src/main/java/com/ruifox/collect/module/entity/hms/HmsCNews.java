package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("hms_c_news")
public class HmsCNews {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "新闻元信息ID", example = "1")
    private Long id;

    @TableField("dataid")
    @ApiModelProperty(value = "新闻数据ID", example = "1")
    private Long dataid;
    @TableField("catid")
    @ApiModelProperty(value = "新闻所属栏目ID", example = "1")
    private Integer catid;
    @TableField("username")
    @ApiModelProperty(value = "创建人", example = "1")
    private Integer username;
    @TableField("status")
    @ApiModelProperty(value = "审核状态", example = "1",notes = "用于审核")
    private Byte status;
    @TableField("state")
    @ApiModelProperty(value = "数据状态", example = "1", notes = "回收，草稿，正常状态标识")
    private Integer state;
    @TableField("publish_time")
    @ApiModelProperty(value = "发布时间", example = "1628563200")
    private Integer publishTime;
    @TableField("expire_time")
    @ApiModelProperty(value = "定时撤稿", example = "1628563200")
    private Integer expireTime;
    @TableField("input_time")
    @ApiModelProperty(value = "添加时间", example = "1628563200")
    private Integer inputTime;
    @TableField("update_time")
    @ApiModelProperty(value = "修改时间", example = "1628563200")
    private Integer updateTime;
    @TableField("url")
    @ApiModelProperty(value = "数据访问地址",notes = "新网站url")
    private String url;
    @TableField("islink")
    @ApiModelProperty(value = "转向链接",notes = "非转向链接:不填 ; 转向链接: 填目标URL")
    private String islink;
    @TableField("content_num")
    @ApiModelProperty(value = "内容字数", example = "1000",notes = "提交数据后记录内容数据的字数（不包含标点符号）")
    private Integer contentNum;
    @TableField("content_image_num")
    @ApiModelProperty(value = "图片张数", example = "2",notes = "统计内容图片个数")
    private Integer contentImageNum;
    @TableField("content_link_num")
    @ApiModelProperty(value = "内容链接数量", example = "1",notes = "统计内容链接数目")
    private Integer contentLinkNum;
    @TableField("top")
    @ApiModelProperty(value = "置顶标识", example = "1")
    private Long top;
    @TableField("listorder")
    @ApiModelProperty(value = "列表排序", example = "1")
    private Long listorder;
    @TableField("sort")
    @ApiModelProperty(value = "晋级排序", example = "1")
    private Integer sort;
    @TableField("old_catid")
    @ApiModelProperty(value = "旧栏目ID", example = "1")
    private String OldCatid;
    @TableField("end_operator")
    @ApiModelProperty(value = "最终操作者ID", example = "1")
    private Integer endOperator;
    @TableField("is_locked")
    @ApiModelProperty(value = "是否锁定", example = "false")
    private Boolean isLocked;
}