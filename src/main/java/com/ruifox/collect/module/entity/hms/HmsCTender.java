package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_c_tender")
@Builder
public class HmsCTender {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long dataid;
    private Long oldCatid;
    private Integer catid;
    private Integer username;
    private Integer endOperator;
    private String url;
    private String islink;
    private Integer top;
    private Long listorder;
    private Integer sort;
    private Integer state;
    private Integer status;
    private Integer publishTime;
    private Integer expireTime;
    private Integer inputTime;
    private Integer updateTime;
    private Integer views;
    private Integer isLocked;
}
