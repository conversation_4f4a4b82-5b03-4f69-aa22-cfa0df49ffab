package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_article_data")
public class StationMArticleData {
    @TableId(value = "data_id", type = IdType.AUTO)
    private Long dataId;
    private String uuid;
    private String title;
    private String subTitle;
    private String author;
    private String content;
    private String thumb;
    private String description;
    private String comefrom;
    private String photographer;
    private Integer isLink;
    private String ignoreReason;
    private Double publishTime;
}
