package com.ruifox.collect.module.entity.station2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("model")
public class StationModel2 {
    @TableId(value = "id", type = IdType.AUTO)
    private int id;

    private String name;
    private String description;
    private String icon;
    private String tableName;
    private int publishType;
    private String positions;
    private Double createTime;
    private Double updateTime;
    private int state;
}
