package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_c_tender_data")
@Builder
public class HmsCTenderData {
    @TableId(type = IdType.AUTO)
    private Long did;

    private String title;
    private String description;
    private String content;
    private String comefrom;
    private String subject;
    private String applyform;
    private Integer openTime;
    private Integer signTime;
    private Integer endTime;
}
