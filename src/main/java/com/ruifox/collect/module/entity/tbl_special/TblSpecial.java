package com.ruifox.collect.module.entity.tbl_special;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_special")
public class TblSpecial implements Serializable {

    private Integer id;

    private Integer siteid;

    private Integer catid;

    private String title;

    private String typeids;

    private String thumb;

    private String banner;

    private String description;

    private String url;

    private Integer ishtml;

    private Integer ispage;

    private String filename;

    private String pics;

    private String voteid;

    private String style;

    private String indexTemplate;

    private String listTemplate;

    private String showTemplate;

    private String css;

    private String username;

    private Integer userid;

    private Integer createtime;

    private Integer sort;

    private Integer listorder;

    private Integer elite;

    private Integer disabled;



}
