package com.ruifox.collect.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("task_parse_record")
public class TaskParseRecord {
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    private int collectTaskId;
    private String targetUrl;
    private String cause;
    private String record;

    public TaskParseRecord(int collectTaskId , String targetUrl, String cause, String record){
        this.collectTaskId = collectTaskId;
        this.targetUrl = targetUrl;
        this.cause = cause;
        this.record = record;
    }
}
