package com.ruifox.collect.module.entity.myexcel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class PersonArticle {



    @ExcelProperty("栏目名称")
    private String categoryName;

    @ExcelProperty("文章名称")
    private String name;


    @ExcelProperty("文章地址")
    private String address;

}
