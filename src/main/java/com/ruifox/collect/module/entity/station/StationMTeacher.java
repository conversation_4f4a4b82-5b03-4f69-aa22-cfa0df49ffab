package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_teacher")
public class StationMTeacher {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "导师元信息ID", example = "1")
    private Integer id;

    private Long dataId;
    private Integer catId;
    private Integer publishUserId;
    private Double createTime;
    private Double updateTime;
    private Integer state;
    private String uri;
    private Integer views;
    private String processInstanceId;
    private Integer sortLevel;
    private Integer isLock;
    private Integer isTop;
}
