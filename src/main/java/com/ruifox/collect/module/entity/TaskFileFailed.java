package com.ruifox.collect.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("task_file_failed")
public class TaskFileFailed {
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    private Integer collectTaskId;
    private String pageId;
    private String url;
    private String filePath;
}
