package com.ruifox.collect.module.entity.tbl_oldest;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* 
* @TableName tbl_c_news
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_news")
public class TblCNewsOldest implements Serializable {

    private Integer id;


    private Integer catid;


    private Integer typeid;


    private String title;


    private String style;


    private String thumb;

    private String keywords;


    private String description;


    private Integer posids;


    private String url;

    private Integer listorder;

    private Integer status;

    private Integer state;

    private Integer sysadd;

    private Integer islink;

    private String username;

    private Integer inputtime;

    private Integer updatetime;

    private Integer endtime;

    private Integer views;

    private Integer sort;

    private Integer istop;

    private String comefrom;

    private String push;

    private String relationid;
}
