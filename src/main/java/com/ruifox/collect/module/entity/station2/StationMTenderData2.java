package com.ruifox.collect.module.entity.station2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("resource_data_tender")
public class StationMTenderData2 {
    @TableId(value = "id", type = IdType.AUTO)
    private Long dataId;
    private String uuid;
    private Integer createUserId;
    private Integer updateUserId;
    private Double createTime;
    private Double updateTime;
    private Integer state;
    private String title;
    private String comefrom;
    private String subject;
    private Double openTime;
    private Double signTime;
    private Double endTime;
    private String description;
    private Integer applyform;
    private String content;
    private String tagList;
}
