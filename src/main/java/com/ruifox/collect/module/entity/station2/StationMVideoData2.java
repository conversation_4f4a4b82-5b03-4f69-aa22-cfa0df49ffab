package com.ruifox.collect.module.entity.station2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("resource_data_video")
public class StationMVideoData2 {
    @TableId(value = "id", type = IdType.AUTO)
    private Long dataId;
    private String uuid;
    private Integer createUserId;
    private Integer updateUserId;
    private Double createTime;
    private Double updateTime;
    private Integer state;
    private String title;
    private String comefrom;
    private String downloads;
    private String thumb;
    private String description;
    private String video;
    private String content;
    private String tagList;
}
