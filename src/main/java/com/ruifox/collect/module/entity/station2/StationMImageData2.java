package com.ruifox.collect.module.entity.station2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("resource_data_image")
public class StationMImageData2 {
    @TableId(value = "id", type = IdType.AUTO)
    private Long dataId;
    private String uuid;
    private Integer createUserId;
    private Integer updateUserId;
    private Double createTime;
    private Double updateTime;
    private Integer state;
    private String title;
    private String author;
    private String photographer;
    private String comefrom;
    private String description;
    private String images;
    private String tagList;
}
