package com.ruifox.collect.module.entity.station2;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_reference")
public class StationMReference {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Long dataId;
    private Integer catId;
    private Integer state;
    private String uri;
    private Integer views;
    private String processInstanceId;
    private Integer sortLevel;
    private Integer isTop;
    private Double publishTime;
    private Double withdrawTime;
    private Double topStartTime;
    private Double topEndTime;

}
