package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_c_newspaper_data")
public class HmsCNewspaperData {

    @TableId(type = IdType.AUTO)
    private Long did;

    private String newspaper;
    private String title;
    private String publishNums;
    private String publishIndex;
    private String publishYear;
    private String articleList;
    private Integer views;
    private String comefrom;
}

