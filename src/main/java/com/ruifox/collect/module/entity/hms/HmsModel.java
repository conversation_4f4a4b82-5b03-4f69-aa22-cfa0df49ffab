package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("hms_model")
public class HmsModel {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点ID
     */
    @TableField("site_id")
    private Integer siteId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 数据表名
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 设置（假设为JSON格式）
     */
    @TableField("setting")
    private String setting; // 或使用特定类型及TypeHandler处理JSON

    /**
     * 页面模板
     */
    @TableField("pageTemplate")
    private String pageTemplate; // 修正为正确的字段名

    /**
     * 列表模板
     */
    @TableField("listTemplate")
    private String listTemplate;

    /**
     * 详情模板
     */
    @TableField("detailTemplate")
    private String detailTemplate;

    /**
     * 表单模板
     */
    @TableField("formTemplate")
    private String formTemplate;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Integer enable;

    /**
     * 是否可搜索
     */
    @TableField("is_search")
    private Integer isSearch;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 删除时间
     */
    @TableField("deleted_at")
    private LocalDateTime deletedAt;

    // Getter和Setter省略，可根据需要添加
}