package com.ruifox.collect.module.entity.station;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("station_m_doctor_data")
public class StationMDoctorData {
    @TableId(value = "data_id", type = IdType.AUTO)
    private Long dataId;

    private String uuid;
    private String thumb;
    private String title;
    private Integer eduPosition;
    private Integer eduPost;
    private String content;
    private String comefrom;
    private Integer docPosition;
    private String goodat;
    private String depart;
    private Integer sex;
    private String ignoreReason;
    private Double publishTime;
}