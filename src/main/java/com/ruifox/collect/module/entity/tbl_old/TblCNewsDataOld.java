package com.ruifox.collect.module.entity.tbl_old;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *
 * @TableName tbl_c_news
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("tbl_c_news_data")
public class TblCNewsDataOld implements Serializable {

    private Integer id;

    private String content;

    private Integer readpoint;

    private String groupidsView;

    private Integer paginationtype;

    private Integer maxcharperpage;

    private String template;

    private Integer paytype;

    private Integer allowComment;

    private String relation;

    private String linkdepart;

    private String linkexpert;


}
