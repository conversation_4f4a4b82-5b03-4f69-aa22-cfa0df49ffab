package com.ruifox.collect.module.entity.hos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hos_doctor")
public class HosDoctor {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer dotAge;
    private Integer dotPhone;
    private Integer addTime;
    private Integer updTime;
    private Integer paixu;
    private Integer hit;
    private Integer tuijian;

    private String dotName;
    private String dotDepId;
    private String dotSex;
    private String dotTime;
    private String dotPic;
    private String dotThumbPic;
    private String dotDesc;
}
