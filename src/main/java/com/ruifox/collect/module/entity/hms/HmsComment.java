package com.ruifox.collect.module.entity.hms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("hms_comment")
public class HmsComment {
    @TableId(type = IdType.AUTO)
    private Long did;

    private Integer catId;
    private Integer modelId;
    private Integer contentId;
    private Integer memberId;
    private Integer adminId;
    private Integer pid;
    private Integer replyId;
    private Integer isReply;
    private Long replyTime;
    private Long inputTime;
    private Integer status;
    private Integer isShow;
    private String ip;
    private String content;
    private Integer isTop;
    private Long state;
}
