package com.ruifox.collect.designmode.singeton;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.locks.ReentrantLock;

/**
 * CollectTaskQueue：启动采集任务的请求队列
 **/
@Getter
@Setter
public class CollectTaskQueue {
    
    private static class CollectTaskQueueSingleton{
        private static final CollectTaskQueue collectTaskQueue = new CollectTaskQueue();
    }
    
    public static CollectTaskQueue getInstance(){
        return CollectTaskQueueSingleton.collectTaskQueue;
    }

    private CollectTaskQueue(){
        collectTaskQueue = new ConcurrentLinkedQueue<>();
        startLock = new ReentrantLock();
    }
    
    private final ConcurrentLinkedQueue<Integer> collectTaskQueue;
    private final ReentrantLock startLock;
    // NOTE volatile关键字保证可见性（确保所有线程都能看到变量最新值）、禁止命令重排序（确保多线程环境下程序的正确执行，防止与该变量相关的指令被重排序到对该变量的写操作之前或读操作之后）
    // NOTE volatile不能保证原子性，即对复合操作（如i++），仍需通过同步锁机制来确保线程安全
    private volatile int taskId = -1;
    private volatile String taskState = "没有任务需要执行";
    
    // TODO 队列操作
    public synchronized void addTask(int taskId){
        // 防止重复添加 + 上锁 ...
        boolean contains = collectTaskQueue.contains(taskId);
        if (contains) {
            return;
        }
        collectTaskQueue.add(taskId);
    }
    
    public Integer pollTask(){
        Integer poll = collectTaskQueue.poll();
        if (poll != null) {
            taskId = poll;
        }
        return poll;
    }
    
    public List<Integer> getAll(){
        return new ArrayList<>(collectTaskQueue);
    }
    
    public void clear(){
        collectTaskQueue.clear();
    }
    
    public void remove(String taskId){
        collectTaskQueue.remove(taskId);
    }
    
    // TODO 锁操作
    public void lock(){
        startLock.lock();
    }
    
    public void unlock(){
        startLock.unlock();
    }
    
    public boolean tryLock(){
        return startLock.tryLock();
    }
    
    public boolean isLocked(){
        return startLock.isLocked();
    }
}
