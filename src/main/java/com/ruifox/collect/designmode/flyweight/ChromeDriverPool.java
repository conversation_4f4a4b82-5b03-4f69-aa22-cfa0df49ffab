package com.ruifox.collect.designmode.flyweight;

import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.Sleeper;
import lombok.Getter;
import org.openqa.selenium.chrome.ChromeOptions;

import java.util.ArrayList;
import java.util.List;
import java.util.Vector;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

@Getter
public class ChromeDriverPool {
    private final Vector<ManagedChromeDriver> pool;
    private final ReentrantLock lock = new ReentrantLock();
    private final ExecutorService executorService;
    // driver启动页面和常驻标签页-搜狗，
    private String residentPageUrl = "https://www.sogou.com/";


    /**
     * 构造方法，建立driver池
     *
     * @param initialSize 构造driver池中初始设置driver数量
     * @param options driver设置参数
     * @param collectTask 采集任务
     */
    public ChromeDriverPool(String residentPageUrl, int initialSize, ChromeOptions options, CollectTask collectTask) {

        // NOTE 这里把劳模给去掉了，改为通过 collectTask来获取常驻的标签页 | 添加url临时参数
        // NOTE 现在修改了逻辑，driver启动页和常驻标签页都用搜狗来替代
        // NOTE 采用搜狗启动的话，如果遇到需要校验cookie的医院怎么办？？？目前想到的还是进行特殊处理，对需要校验cookie的医院启动页用首页
        this.residentPageUrl = residentPageUrl;

        // TODO 初始化driver池
        pool = new Vector<>(initialSize);
        // TODO 改为多线程执行：
        executorService = Executors.newFixedThreadPool(initialSize);
        List<Future<?>> futures = new ArrayList<>(); // 用于阻塞主线程，同时获取线程执行结果(主要是异常)
        for (int i = 0; i < initialSize; i++) {
            Future<?> future =  executorService.submit(() -> {
                pool.add(new ManagedChromeDriver(residentPageUrl, options, collectTask));
            });
            futures.add(future);
        }
        // 停止接收新任务
        executorService.shutdown();
        // 等待所有任务执行完成
        for (Future<?> future : futures) {
            try {
                future.get(); // 阻塞主线程
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException("初始化FirefoxDriverPool失败", e);
            }
        }
    }

    /**
     * 拿出可用driver
     * @return
     */
    public ManagedChromeDriver getAvailableDriver(){
        ManagedChromeDriver availableDriver;
        lock.lock();
        try {
            while ((availableDriver = findAvailableDriver()) == null) {
                lock.unlock(); // 释放锁等待，避免死锁
                Sleeper.sleep(100, TimeUnit.SECONDS);
                lock.lock();
            }
            availableDriver.markBusy();
        } finally {
            lock.unlock();
        }
        return availableDriver;
    }

    private ManagedChromeDriver findAvailableDriver() {
        for (ManagedChromeDriver driver : pool) {
            if (!driver.isBusy()) {
                return driver;
            }
        }
        return null;
    }

    public void releaseDriver(ManagedChromeDriver driver) {
        lock.lock();
        try {
            if (driver != null) {
                driver.closeNonResidentTabs(residentPageUrl);
                driver.markAvailable();
            }
        } finally {
            lock.unlock();
        }
    }

    // 其他可能需要的方法，比如扩容、缩容等
}