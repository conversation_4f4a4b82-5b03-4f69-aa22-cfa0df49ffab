package com.ruifox.collect.designmode.flyweight;

import com.ruifox.collect.manager.RedisGetManager;
import lombok.Getter;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskCookie;
import com.ruifox.collect.util.JsonUtil;
import org.openqa.selenium.Cookie;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/*增强浏览器对象类*/
@Getter
public class ManagedChromeDriver {
    private ChromeDriver driver;
    private boolean isBusy;

    // 存储了driver及其绑定的常驻标签页
    private final Map<ChromeDriver, String> firefoxDriver2WindowHandleMap = new ConcurrentHashMap<ChromeDriver, String>();

    // TODO 初始化时打开常驻标签页
    public ManagedChromeDriver(String residentPageUrl, ChromeOptions options, CollectTask collectTask) {
        this.driver = new ChromeDriver(options);
        this.isBusy = false;

        // TODO driver启动页
        driver.get(residentPageUrl);
        
        // TODO 添加Cookie设置的操作
        if (collectTask.getCookieId()!=null && collectTask.getCookieId()!=0) {
            TaskCookie taskCookie = RedisGetManager.getTaskCookie(collectTask);
            if (taskCookie != null && taskCookie.getIsDownload() == 0) {

                // 删除全部cookie
                driver.manage().deleteAllCookies();
                
                // 添加cookie
                List<Map<String,String>> list = JsonUtil.Json2Obj(taskCookie.getCookie(), List.class);
                for (Map<String, String> map : list) {
                    driver.manage().addCookie(new Cookie(map.get("key"), map.get("value"), map.get("path")));
                }

                // 重新打开页面
                driver.get(residentPageUrl);
            }
        }

        // TODO 登录页初始化
        WebSpecialHanding webSpecialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
        webSpecialHanding.loginInit(driver, collectTask);

        // TODO 绑定当前driver与常驻标签页（标签页句柄）
        String windowHandle = driver.getWindowHandle();
        firefoxDriver2WindowHandleMap.put(driver, windowHandle);
    }

    // TODO 关闭新打开的标签页，回退到常驻标签页
    public void closeNonResidentTabs(String residentPageUrl) {
        try {
            // 关闭非常驻标签页
            Set<String> windowHandles = driver.getWindowHandles();
            for (String handle : windowHandles) {
                driver.switchTo().window(handle);
                if (!driver.getCurrentUrl().equals(residentPageUrl)) {
                    driver.close();
                }
            }
            // 切换回常驻标签页
            driver.switchTo().window(firefoxDriver2WindowHandleMap.get(driver));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public synchronized void markBusy() {
        this.isBusy = true;
    }

    public synchronized void markAvailable() {
        this.isBusy = false;
    }

    public synchronized boolean isBusy() {
        return isBusy;
    }

    // TODO 可能还需要一个方法来在不再需要时正确关闭驱动
    public void quit() {
        if (driver != null) {
            driver.quit();
            driver = null;
        }
    }
}