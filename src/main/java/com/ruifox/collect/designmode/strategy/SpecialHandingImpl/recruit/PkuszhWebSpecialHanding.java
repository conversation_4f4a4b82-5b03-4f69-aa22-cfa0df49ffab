package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.recruit;

import com.ruifox.collect.common.constants.DoctorDataConstant;
import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.common.constants.RecruitConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskTemplate;
import com.ruifox.collect.util.ElementUtil;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class PkuszhWebSpecialHanding implements WebSpecialHanding {
    @Override
    public String getBeanFlag() {
        return "www.pkuszh.com-12";
    }

    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {
        // 标题
        String title = listItemMap.get(RecruitConstant.TITLE);
        if (StringUtils.isNotBlank(title)) {
            title = StringUtils.trim(title);
            listItemMap.put(RecruitConstant.TITLE, title);
        } else {
            listItemMap.put(RecruitConstant.TITLE, "");
        }

        // 学历
        String education = listItemMap.get(RecruitConstant.EDUCATION);
        if (StringUtils.isNotBlank(education)) {
            education = education.trim();
            listItemMap.put(RecruitConstant.EDUCATION, education);
        } else {
            listItemMap.put(RecruitConstant.EDUCATION, "");
        }

        // 招聘人数
        String numbers = listItemMap.get(RecruitConstant.NUMBERS);
        if (StringUtils.isNotBlank(numbers)) {
            numbers = numbers.trim();
            listItemMap.put(RecruitConstant.NUMBERS, numbers);
        } else {
            listItemMap.put(RecruitConstant.NUMBERS, "");
        }

        // 发布时间
        String publishTime = listItemMap.get(RecruitConstant.PUBLISH_TIME);
        if (StringUtils.isNotBlank(publishTime)) {
            publishTime = publishTime.trim();
            try {
                Date date = DateUtils.parseDate(publishTime, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                listItemMap.put(RecruitConstant.PUBLISH_TIME, timestampInSeconds);
            } catch (Exception e) {
                listItemMap.put(RecruitConstant.PUBLISH_TIME, publishTime);
            }
        } else {
            listItemMap.put(RecruitConstant.PUBLISH_TIME, "");
        }

        // 截止时间
        String deadlineDate = listItemMap.get(RecruitConstant.DEADLINE_DATE);
        if (StringUtils.isNotBlank(deadlineDate)) {
            deadlineDate = deadlineDate.trim();
            try {
                Date date = DateUtils.parseDate(deadlineDate, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                listItemMap.put(RecruitConstant.DEADLINE_DATE, timestampInSeconds);
            } catch (Exception e) {
                listItemMap.put(RecruitConstant.DEADLINE_DATE, deadlineDate);
            }
        } else {
            listItemMap.put(RecruitConstant.DEADLINE_DATE, "");
        }

        // 工作地点
        String workLocation = listItemMap.get(RecruitConstant.WORK_LOCATION);
        if (StringUtils.isNotBlank(workLocation)) {
            workLocation = workLocation.trim();
            listItemMap.put(RecruitConstant.WORK_LOCATION, workLocation);
        } else {
            listItemMap.put(RecruitConstant.WORK_LOCATION, "");
        }

        // 正文
        String content = listItemMap.get(NewsDataConstant.CONTENT);
        Document contextDom = Jsoup.parse(content, request.getUrl());
        if (StringUtils.isNotBlank(content)) {
            // 1.删除不需要的元素（写了一个常用的通用方法
            ElementUtil.deleteNoContent(contextDom);

            //删除正文中a标签，且该标签所对应的是原网站的html
            ElementUtil.deleteHtmlA(contextDom, collectTask);

            // 2.更新正文（写了一个常用的通用方法
            content = ElementUtil.getContentRemoveHtml(contextDom);

            TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);
            WebSpecialHanding webSpecialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
            //下载正文中的元素
            List<String> linkList = Stream.of(taskTemplate.getContentALoc(), taskTemplate.getContentImgLoc(), taskTemplate.getContentVideoLoc())
                    .flatMap(loc -> contextDom.select(loc).stream().map(element -> {
                        String attr = "";
                        if (loc.equals(taskTemplate.getContentALoc())) {
                            attr = element.attr("abs:href");
                        } else if (loc.equals(taskTemplate.getContentImgLoc()) || loc.equals(taskTemplate.getContentVideoLoc())) {
                            // 如果是base64格式的图片，无法获取获取绝对地址
                            String src = element.attr("src");
                            if (src.contains("base64")) {
                                attr = src;
                            } else {
                                attr = element.attr("abs:src");
                            }
                        } else {
                            attr = "";
                        }
                        // TODO 加了一个保险，重新编码下载链接中的参数部分（这里可能需要处理URL参数中带中文造成乱码的情况
                        attr = HttpClientUtils.ReEncodeHttpUrl(attr);
                        return attr;
                    }))
                    .filter(url -> webSpecialHanding.specialHandingForJudgeHttpUrl(url, collectTask)) //过滤无用链接
                    .distinct() //去重
                    .collect(Collectors.toList());

            // TODO 多线程异步下载文件
            Map<String, String> map = FileDataManager.downloadFromHttpUrl(linkList, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);

            // TODO 更新正文
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (FileUtil.localUrlChick(entry.getValue())) {
                    content = content.replace(entry.getKey(), entry.getValue());
                }
            }

            listItemMap.put(DoctorDataConstant.CONTENT, content);
        } else {
            listItemMap.put(DoctorDataConstant.CONTENT, "");
        }
    }

    @Override
    public void specialHandingForNotEmbedded(Map<String, String> independentDataMap, CollectTask collectTask, Request request) {

    }

    @Override
    public boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask) {
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || url.contains("/oss/")
                || url.contains("mailto") || url.contains(".jsp")
                || url.contains("docs.qq.com") || url.contains("www.sphmc.org")) {
            return false;
        }
        return true;
    }
}
