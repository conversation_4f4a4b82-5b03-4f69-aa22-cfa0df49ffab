package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.news;

import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.ElementUtil;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.Map;

@Component
public class aersWebSpecialHandingNews implements WebSpecialHanding {


    @Override
    public String getBeanFlag() {
        return "aers.scfypt.com-1";
    }

    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {

    }

    @Override
    public void specialHandingForNotEmbedded(Map<String, String> independentDataMap, CollectTask collectTask, Request request) {

        // 正文
        String content = independentDataMap.get(NewsDataConstant.CONTENT);
        if (StringUtils.isNotBlank(content)) {
            Document contextDom = Jsoup.parse(content);
            // 1.删除不需要的元素（写了一个常用的通用方法
            ElementUtil.deleteNoContent(contextDom);

            //删除正文中a标签，且该标签所对应的是原网站的html
            ElementUtil.deleteHtmlA(contextDom, collectTask);

            //更新正文内容格式
            ElementUtil.modifyFormat(contextDom);

            // 2.更新正文（写了一个常用的通用方法
            content = ElementUtil.getContentRemoveHtml(contextDom);

            independentDataMap.put(NewsDataConstant.CONTENT, content);
        } else {
            independentDataMap.put(NewsDataConstant.CONTENT, "");
        }

    }

    @Override
    public boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask) {
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || url.contains("/oss/")
                || url.contains("mailto") || url.contains("jsp")
                || url.contains("javascript")) {
            return false;
        }
        return true;
    }
}
