package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl2;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station.StationCategoryMapper;
import com.ruifox.collect.dao.mapper.station.StationMDownloadDataMapper;
import com.ruifox.collect.dao.mapper.station.StationMDownloadMapper;
import com.ruifox.collect.dao.mapper.station2.StationCategoryMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMDownloadDataMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMReferenceMapper;
import com.ruifox.collect.designmode.strategy.ImportTypeJava2;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station.StationCategory;
import com.ruifox.collect.module.entity.station.StationMDownload;
import com.ruifox.collect.module.entity.station.StationMDownloadData;
import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.module.entity.station2.StationMDownloadData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeDownloadJava2 implements ImportTypeJava2 {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeBuildDynamicDataSource();

            // 按栏目ID查询类别
            StationCategory2 stationCategory2 = ApplicationContextProvider.getBeanByType(StationCategoryMapper2.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory2 == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory2.getId(), stationCategory2.getName());
            }

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_download
                StationMReference reference = new StationMReference();

                reference.setDataId(-1L);
                reference.setCatId(collectTask.getImportCategoryId());
                reference.setPublishTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000"));
                reference.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                reference.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                reference.setViews(Integer.parseInt(views));
                finalMap.put("stationMDownload", reference);
                

                
                
                StationMDownloadData2 stationMDownloadData = new StationMDownloadData2();

                stationMDownloadData.setUuid(UUID.randomUUID().toString());
                String title = StringUtils.trim(localRecordMap.get("title").toString());
                stationMDownloadData.setTitle(title);
                stationMDownloadData.setComefrom(StringUtils.trim(localRecordMap.get("come_from").toString()));
                stationMDownloadData.setCreateTime((double) System.currentTimeMillis());
                stationMDownloadData.setUpdateTime((double) System.currentTimeMillis());
                stationMDownloadData.setCreateUserId(1);
                stationMDownloadData.setUpdateUserId(1);
                stationMDownloadData.setState(2);
                


                String file = localRecordMap.get("content").toString().trim();
                List<Map<Object,Object>> downloads = new ArrayList<>();
                if (StringUtils.isNotBlank(file)) {
                    File file1 = new File(file);
                    file = CrawlerManager.changeFileUrl(file1);
                    Map<Object,Object> map = new HashMap<>();
                    map.put("id", Long.parseLong(CrawlerManager.randomNumber()));
                    map.put("name", title);
                    map.put("file", file);
                    downloads.add(map);
                }
                stationMDownloadData.setDownloads(JsonUtil.obj2String(downloads));
                

                finalMap.put("stationMDownloadData", stationMDownloadData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            for (Map<String, Object> finalMap : finalMaps) {
                StationMReference stationMDownload2 = (StationMReference) finalMap.get("stationMDownload");
                StationMDownloadData2 stationMDownloadData2 = (StationMDownloadData2) finalMap.get("stationMDownloadData");

                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 落库 -> station_m_reference
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).insert(stationMDownload2);

                // TODO 切换为动态数据源
                DynamicDataSource.changeResourceDynamicDataSource();
                // 落库 -> resource_data_doctor
                ApplicationContextProvider.getBeanByType(StationMDownloadDataMapper2.class).insert(stationMDownloadData2);

                //落库 -> folder_resource
                FolderResource folderResource= new FolderResource().builder()
                        .userId(1).folderId(2).modelId(stationCategory2.getModelId())
                        .resourceId(stationMDownloadData2.getDataId())
                        .createTime((double) System.currentTimeMillis())
                        .updateTime((double) System.currentTimeMillis())
                        .version(1).sort(stationMDownloadData2.getDataId())
                        .listOrder(0).state(2).isDeleted(0).build();
                ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

                stationMDownload2.setDataId(Long.valueOf(folderResource.getId()));
                stationMDownload2.setSortLevel(Math.toIntExact(stationMDownload2.getId()));


                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 更新 -> station_m_reference
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).updateById(stationMDownload2);

            };
        }
        finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "10";
    }
}
