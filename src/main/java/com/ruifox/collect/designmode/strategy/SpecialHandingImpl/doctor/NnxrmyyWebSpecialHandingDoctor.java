package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.doctor;

import com.ruifox.collect.common.constants.DoctorDataConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.ElementUtil;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 宁南县人民医院医生信息特殊处理类
 * 处理医生头像下载、数据清洗和格式化
 */
@Component
public class NnxrmyyWebSpecialHandingDoctor implements WebSpecialHanding {

    /**
     * 必须重写！这是这个策略类的"身份证"。
     * 项目会根据域名和objectId自动匹配到这个类。
     * @return "www.nnxrmyy.com" 是域名, "2" 是医生模型的objectId.
     */
    @Override
    public String getBeanFlag() {
        return "www.nnxrmyy.com-2";
    }

    /**
     * 清洗从列表页获取的数据。
     * 对于这个网站，列表页的数据很干净，可能不需要太多处理。
     */
    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {
        // 1. 清洗医生姓名（医生表中用title字段存储医生姓名）
        String name = listItemMap.get("title");
        if (name != null) {
            listItemMap.put("title", name.trim());
        }

        // 2. 清洗职务信息（医生表中用doc_position字段存储职称）
        String docPosition = listItemMap.get("doc_position");
        if (docPosition != null) {
            listItemMap.put("doc_position", docPosition.trim());
        }

        // 3. 清洗科室信息（医生表中用depart字段存储科室）
        String department = listItemMap.get("depart");
        if (department != null) {
            listItemMap.put("depart", department.trim());
        }

        // 4. 处理擅长
        String description = listItemMap.get("goodat");
        if (description != null) {
            // 移除多余的空白字符和换行
            description = description.replaceAll("\\s+", " ").trim();

            // 检查是否包含"专业特长"或"专业擅长"，如果包含则提取
            Pattern specialtyPattern = Pattern.compile("(专业特长|专业擅长)[：:]?(.*)");
            Matcher matcher = specialtyPattern.matcher(description);
            if (matcher.find()) {
                String specialtyContent = matcher.group(2).trim(); // 只要后面的内容
                description = specialtyContent;
            } else {
                // 如果没有找到"专业特长"或"专业擅长"，则设为空
                description = "";
            }

            listItemMap.put("goodat", description);
        } else {
            // 如果原本就是null，设为空字符串
            listItemMap.put("goodat", "");
        }

        // 5. 处理头像下载（从thumb字段获取图片URL）
        String thumbUrl = listItemMap.get("thumb");
        if (thumbUrl != null && !thumbUrl.trim().isEmpty()) {
            try {
                // 补全相对路径为绝对路径
                if (thumbUrl.startsWith("/")) {
                    thumbUrl = "https://www.nnxrmyy.com" + thumbUrl;
                }
                
                // 使用项目标准的文件下载方法
                String localThumbPath = FileDataManager.downloadFromHttpUrl(
                    thumbUrl, 
                    request.getUrl(), 
                    request.getExtra(RequestConstant.PAGE_ID), 
                    collectTask
                );
                
                if (StringUtils.isNotBlank(localThumbPath)) {
                    listItemMap.put("thumb", localThumbPath);
                }
            } catch (Exception e) {
                System.err.println("头像下载失败: " + thumbUrl + ", 错误: " + e.getMessage());
            }
        }
    }

    /**
     * 清洗从详情页获取的数据。
     * 主要任务是下载医生头像并处理个人简介。
     */
    @Override
    public void specialHandingForNotEmbedded(Map<String, String> detailItemMap, CollectTask collectTask, Request request) {
        String content = detailItemMap.get(DoctorDataConstant.CONTENT);
        if (StringUtils.isNotBlank(content)) {
            Document contextDom = Jsoup.parse(content);
            // 1.删除不需要的元素（写了一个常用的通用方法
            ElementUtil.deleteNoContent(contextDom);

            //删除正文中a标签，且该标签所对应的是原网站的html
            ElementUtil.deleteHtmlA(contextDom,collectTask);

            //格式化
            ElementUtil.modifyFormat(contextDom);

            // 2.更新正文（写了一个常用的通用方法
            content = ElementUtil.getContentRemoveHtml(contextDom);

            // 3.进一步处理：删除<h2>医生简介</h2>标签
            if (StringUtils.isNotBlank(content)) {
                content = content.replaceAll("<h2>医生简介</h2>", "").trim();
            }


            detailItemMap.put(DoctorDataConstant.CONTENT, content);
        } else {
            detailItemMap.put(DoctorDataConstant.CONTENT, "");
        }
    }

    @Override
    public boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask) {
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || !url.contains(collectTask.getHost())
                || url.contains("/oss/")) {
            return false;
        }
        return true;
    }
}