package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.tender;

import com.ruifox.collect.common.constants.DoctorDataConstant;
import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.ElementUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class HyxzyyWebSpecialHandingTender implements WebSpecialHanding {
    @Override
    public String getBeanFlag() {
        return "www.hyxzyy.net-9";
    }

    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {
        //发布时间
        String publish_time = listItemMap.get(NewsDataConstant.PUBLISH_TIME);
        if (StringUtils.isNotBlank(publish_time)) {
            try {
                publish_time = publish_time.substring(1,publish_time.length() - 1);
                publish_time = publish_time.trim();
                Date date = DateUtils.parseDate(publish_time, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                listItemMap.put(NewsDataConstant.PUBLISH_TIME, timestampInSeconds);
            } catch (Exception e) {
                listItemMap.put(NewsDataConstant.PUBLISH_TIME, publish_time);
            }
        } else {
            listItemMap.put(NewsDataConstant.PUBLISH_TIME, "");
        }
    }

    @Override
    public void specialHandingForNotEmbedded(Map<String, String> independentDataMap, CollectTask collectTask, Request request) {
        // 标题
        String title = independentDataMap.get(NewsDataConstant.TITLE);
        if (StringUtils.isNotBlank(title)) {
            title = StringUtils.trim(title);
            independentDataMap.put(NewsDataConstant.TITLE, title);
        } else {
            independentDataMap.put(NewsDataConstant.TITLE, "");
        }

        // 正文
        String content = independentDataMap.get(NewsDataConstant.CONTENT);
        if (StringUtils.isNotBlank(content)) {
            Document contextDom = Jsoup.parse(content);
            // 1.删除不需要的元素（写了一个常用的通用方法
            ElementUtil.deleteNoContent(contextDom);

            //删除正文中a标签，且该标签所对应的是原网站的html
            ElementUtil.deleteHtmlA(contextDom, collectTask);
           /* Elements select = contextDom.select("img");
            for(Element e : select) {
                String attr = e.attr("src");
                if(attr.startsWith("data:image")) {
                    e.removeAttr("src");
                }
            }*/

            // 2.更新正文（写了一个常用的通用方法
            content = ElementUtil.getContentRemoveHtml(contextDom);

            independentDataMap.put(DoctorDataConstant.CONTENT, content);
        } else {
            independentDataMap.put(DoctorDataConstant.CONTENT, "");
        }
    }

    @Override
    public boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask) {
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || url.contains("/oss/")
                || url.contains("mailto") || url.contains(".jsp")
                || url.contains("mp.weixin.qq.com") || url.contains("www.johnsonsbaby.com.cn")
                || url.contains("http://www.xywy.com")) {
            return false;
        }
        return true;
    }
}
