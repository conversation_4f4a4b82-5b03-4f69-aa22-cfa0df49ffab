package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ImportTypeVolunteer implements ImportType {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());

            // TODO 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();
            // 构造本地路径替换前缀
            String localPathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/"; // 示例路径：C:/www.cdlyy.com/
            // 基本数据表名
            String dataTableName = collectTask.getDataTableName();
            // 查询本地所有数据
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(dataTableName);

            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.error("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            }
            log.info("当前导入栏目ID:{},当前导入栏目名称:{}", hmsCategory.getId(), hmsCategory.getName());
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain()+"oss/";

            // TODO 切回本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();

            // 处理本地数据至hms_c_donor_volunteers和hms_c_donor_volunteers_data
            long i = 1;
            String sortTmp = "0";
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if(sort.equals(sortTmp)){
                    continue;
                }
                sortTmp = sort;

                System.out.println("当前正在处理第 " + (i++) + " 条数据...");
                HashMap<String, Object> finalMap = new HashMap<>();

                // 导入时的特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                } else {
                    specialHanding.newsSpecialHanding(localRecordMap, collectTask);
                }

                // TODO 处理至hms_c_donor_volunteers
                HmsCDonorVolunteers hmsCDonorVolunteers = new HmsCDonorVolunteers();
                hmsCDonorVolunteers.setDataid(1L);
                hmsCDonorVolunteers.setOldCatid(null);
                hmsCDonorVolunteers.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCDonorVolunteers.setUsername(1);
                hmsCDonorVolunteers.setEndOperator(1);
                hmsCDonorVolunteers.setUrl("");
                hmsCDonorVolunteers.setIslink((int) localRecordMap.get("flag") == 0 ? null : (String) localRecordMap.get("target_url"));
                hmsCDonorVolunteers.setTop(0);
                hmsCDonorVolunteers.setListorder(-99L);
                hmsCDonorVolunteers.setSort(1);
                hmsCDonorVolunteers.setState(0);
                hmsCDonorVolunteers.setStatus(99);

                // FIXME
                /*String publishTime = String.valueOf(localRecordMap.get("publish_time"));
                if (StringUtils.isBlank(publishTime)) {
                    publishTime = "0";
                }*/
                hmsCDonorVolunteers.setPublishTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCDonorVolunteers.setExpireTime(0);
                hmsCDonorVolunteers.setInputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCDonorVolunteers.setUpdateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCDonorVolunteers.setViews(0);
                hmsCDonorVolunteers.setIsLocked(0);

                finalMap.put("hmsCDonorVolunteers", hmsCDonorVolunteers);


                // TODO 处理至hms_c_donor_volunteers_data
                HmsCDonorVolunteersData hmsCDonorVolunteersData = new HmsCDonorVolunteersData();
                hmsCDonorVolunteersData.setDid(hmsCDonorVolunteers.getDataid());

                hmsCDonorVolunteersData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));

                String title = String.valueOf(localRecordMap.get("title"));
                if(StringUtils.isBlank(title))
                    title = "";
                hmsCDonorVolunteersData.setTitle(title);

                String sex = String.valueOf(localRecordMap.get("sex"));
                if(StringUtils.isNotBlank(sex)){
                    if(sex.equals("男"))
                        sex = "1";
                    else if(sex.equals("女"))
                        sex = "2";
                    else
                        sex = "";
                }else{
                    sex = "";
                }
                hmsCDonorVolunteersData.setSex(sex);

                String registeredBirthplace = String.valueOf(localRecordMap.get("registered_birthplace"));
                if(StringUtils.isBlank(registeredBirthplace))
                    registeredBirthplace = "";
                hmsCDonorVolunteersData.setRegistered_birthplace(registeredBirthplace);

                String ethnicGroup = String.valueOf(localRecordMap.get("ethnic_group"));
                if(StringUtils.isBlank(ethnicGroup))
                    ethnicGroup = "";
                hmsCDonorVolunteersData.setEthnic_group(ethnicGroup);

                String career = String.valueOf(localRecordMap.get("career"));
                if(StringUtils.isBlank(career))
                    career = "";
                hmsCDonorVolunteersData.setCareer(career);

                String address = String.valueOf(localRecordMap.get("address"));
                if(StringUtils.isBlank(address))
                    address = "";
                hmsCDonorVolunteersData.setAddress(address);

                String birthday = String.valueOf(localRecordMap.get("birthday"));
                if(StringUtils.isBlank(birthday))
                    birthday = "0";
                hmsCDonorVolunteersData.setBirthday(Integer.parseInt(birthday));

                String tabooDay = String.valueOf(localRecordMap.get("taboo_day"));
                if(StringUtils.isBlank(tabooDay))
                    tabooDay = "0";
                hmsCDonorVolunteersData.setTaboo_day(Integer.parseInt(tabooDay));

                String registrationDay = String.valueOf(localRecordMap.get("registration_day"));
                if(StringUtils.isBlank(registrationDay))
                    registrationDay = "0";
                hmsCDonorVolunteersData.setRegistration_day(Integer.parseInt(registrationDay));

                String realizationDay = String.valueOf(localRecordMap.get("realization_day"));
                if(StringUtils.isBlank(realizationDay))
                    realizationDay = "0";
                hmsCDonorVolunteersData.setRealization_day(Integer.parseInt(realizationDay));

                String biographicSketch = String.valueOf(localRecordMap.get("biographic_sketch"));
                if(StringUtils.isBlank(biographicSketch))
                    biographicSketch = "";
                hmsCDonorVolunteersData.setBiographic_sketch(biographicSketch);

                String flowers = String.valueOf(localRecordMap.get("flowers"));
                if(StringUtils.isBlank(flowers))
                    flowers = "0";
                hmsCDonorVolunteersData.setFlowers(Integer.parseInt(flowers));

                String song = String.valueOf(localRecordMap.get("song"));
                if(StringUtils.isBlank(song))
                    song = "0";
                hmsCDonorVolunteersData.setSong(Integer.parseInt(song));

                String candle = String.valueOf(localRecordMap.get("candle"));
                if(StringUtils.isBlank(candle))
                    candle = "0";
                hmsCDonorVolunteersData.setCandle(Integer.parseInt(candle));

                String wine = String.valueOf(localRecordMap.get("wine"));
                if(StringUtils.isBlank(wine))
                    wine = "0";
                hmsCDonorVolunteersData.setWine(Integer.parseInt(wine));

                String thumb = String.valueOf(localRecordMap.get("thumb"));
                if(StringUtils.isBlank(thumb))
                    thumb = "";
                thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                hmsCDonorVolunteersData.setThumb(thumb);

                finalMap.put("hmsCDonorVolunteersData", hmsCDonorVolunteersData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCDonorVolunteers hmsCDonorVolunteers = (HmsCDonorVolunteers) finalMap.get("hmsCDonorVolunteers");
                    HmsCDonorVolunteersData hmsCDonorVolunteersData = (HmsCDonorVolunteersData) finalMap.get("hmsCDonorVolunteersData");

                    // 落库 -> hms_c_donor_volunteers
                    ApplicationContextProvider.getBeanByType(HmsCDonorVolunteersMapper.class).insert(hmsCDonorVolunteers);
                    hmsCDonorVolunteers.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCDonorVolunteers.getId()) + ".html");
                    hmsCDonorVolunteers.setListorder(hmsCDonorVolunteers.getId());
                    hmsCDonorVolunteers.setDataid(hmsCDonorVolunteers.getId());

                    // 更新 -> hms_c_donor_volunteers
                    ApplicationContextProvider.getBeanByType(HmsCDonorVolunteersMapper.class).updateById(hmsCDonorVolunteers);
                    hmsCDonorVolunteersData.setDid(hmsCDonorVolunteers.getId());

                    // 落库 -> hms_c_donor_volunteers_data
                    ApplicationContextProvider.getBeanByType(HmsCDonorVolunteersDataMapper.class).insert(hmsCDonorVolunteersData);
                }
                return null;
            });
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "14";
    }
}
