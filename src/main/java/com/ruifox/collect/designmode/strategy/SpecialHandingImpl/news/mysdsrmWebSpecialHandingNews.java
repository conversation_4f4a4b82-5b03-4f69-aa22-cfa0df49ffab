package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.news;

import com.ruifox.collect.common.constants.DoctorDataConstant;
import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.ElementUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.Date;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class mysdsrmWebSpecialHandingNews implements WebSpecialHanding {


    @Override
    public String getBeanFlag() {
        return "www.scmy120.com-1";
    }

    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {

        // 处理作者
        String author = listItemMap.get(NewsDataConstant.AUTHOR);
        // 处理author为null或空字符串的情况
        if (author == null) {
            author = "";
        }
        Pattern pattern = Pattern.compile("作者：\\s*(.+)");
        Matcher matcher = pattern.matcher(author);
        if (matcher.find()) {
            // 匹配成功：提取作者名并去除首尾空格
            author = matcher.group(1).trim();
        } else {
            // 匹配失败：将作者设置为空字符串
            author = "";
        }
        listItemMap.put(NewsDataConstant.AUTHOR, author);

        // 处理发布时间
        String publish_time = listItemMap.get(NewsDataConstant.PUBLISH_TIME);
        if (StringUtils.isNotBlank(publish_time)) {
            try {
                Date date = DateUtils.parseDate(publish_time, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                listItemMap.put(NewsDataConstant.PUBLISH_TIME, timestampInSeconds);
            } catch (Exception e) {
                // 如果解析失败，保留原始字符串
                listItemMap.put(NewsDataConstant.PUBLISH_TIME, publish_time);
            }
        } else {
            listItemMap.put(NewsDataConstant.PUBLISH_TIME, "");
        }

        // 处理缩略图
        String thumbUrl = listItemMap.get("thumb");
        if (thumbUrl != null && !thumbUrl.trim().isEmpty()) {
            try {
                // 补全相对路径为绝对路径
                if (thumbUrl.startsWith("/")) {
                    thumbUrl = "http://www.scmy120.com" + thumbUrl;
                }

                // 使用项目标准的文件下载方法
                String localThumbPath = FileDataManager.downloadFromHttpUrl(
                        thumbUrl,
                        request.getUrl(),
                        request.getExtra(RequestConstant.PAGE_ID),
                        collectTask
                );

                if (StringUtils.isNotBlank(localThumbPath)) {
                    listItemMap.put("thumb", localThumbPath);
                }
            } catch (Exception e) {
                System.err.println("头像下载失败: " + thumbUrl + ", 错误: " + e.getMessage());
            }
        }

    }

    @Override
    public void specialHandingForNotEmbedded(Map<String, String> independentDataMap, CollectTask collectTask, Request request) {

        // 正文
        String content = independentDataMap.get(NewsDataConstant.CONTENT);
        if (StringUtils.isNotBlank(content)) {
            Document contextDom = Jsoup.parse(content);
            // 1.删除不需要的元素（写了一个常用的通用方法
            ElementUtil.deleteNoContent(contextDom);

            //删除正文中a标签，且该标签所对应的是原网站的html
            ElementUtil.deleteHtmlA(contextDom, collectTask);

            //更新正文内容格式
            ElementUtil.modifyFormat(contextDom);

            // 2.更新正文（写了一个常用的通用方法
            content = ElementUtil.getContentRemoveHtml(contextDom);

            independentDataMap.put(NewsDataConstant.CONTENT, content);
        } else {
            independentDataMap.put(NewsDataConstant.CONTENT, "");
        }

        String fullContent = independentDataMap.get(NewsDataConstant.CONTENT);
        String str = Jsoup.parse(fullContent).text();
        String description = str.length() > 50 ? str.substring(0, 50) : str;
        independentDataMap.put(NewsDataConstant.DESCRIPTION, description);

        String text = independentDataMap.get("text");
        if (StringUtils.isNotBlank(text)) {
            String views = extractWithRegex(text, "(\\d+)次浏览");
            String comeFrom = extractWithRegex(text, "来源：(.*)");
            independentDataMap.put(NewsDataConstant.VIEWS, views);
            independentDataMap.put(NewsDataConstant.COME_FROM, comeFrom);
        }
    }


    /**
     * 一个辅助方法，使用正则表达式从文本中提取匹配的第一个组
     * @param text 待搜索的文本
     * @param regex 正则表达式
     * @return 匹配到的字符串，如果未找到则返回null
     */
    private static String extractWithRegex(String text, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            // group(1) 返回第一个捕获组的内容
            return matcher.group(1);
        }
        return null;
    }
}
