package com.ruifox.collect.designmode.strategy.ImportTypeImpl.handing;

import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.designmode.strategy.ImportTypeImpl.ImportSpecialHanding;
import com.ruifox.collect.module.entity.CollectTask;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class Cpch1965SpecialHanding implements ImportSpecialHanding {
    @Override
    public String getHost() {
        return "www.cpch1965.com-1";
    }

    @Override
    public void newsSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        String content = localRecordMap.get(NewsDataConstant.CONTENT).toString();
        Document document = Jsoup.parse(content);
        if(document.select("p.title")!=null && document.select("p.time")!=null) {
            document.select("p.title").remove();
            document.select("p.time").remove();
        }
        localRecordMap.put(NewsDataConstant.CONTENT,document.body().toString());
    }
}
