package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeDownloadJava implements ImportTypeJava {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_download
                StationMDownload stationMDownload = new StationMDownload();

                stationMDownload.setDataId(-1L);
                stationMDownload.setCatId(collectTask.getImportCategoryId());
                stationMDownload.setPublishUserId(1);
                stationMDownload.setCreateTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000"));
                stationMDownload.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                stationMDownload.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                stationMDownload.setViews(Integer.parseInt(views));
                finalMap.put("stationMDownload", stationMDownload);

                StationMDownloadData stationMDownloadData = new StationMDownloadData();

                stationMDownloadData.setUuid(UUID.randomUUID().toString());
                String title = StringUtils.trim(localRecordMap.get("title").toString());
                stationMDownloadData.setTitle(title);
                stationMDownloadData.setComefrom(StringUtils.trim(localRecordMap.get("come_from").toString()));
                stationMDownloadData.setPublishTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000"));

                String file = localRecordMap.get("content").toString().trim();
                List<Map<Object,Object>> downloads = new ArrayList<>();
                if (StringUtils.isNotBlank(file)) {
                    File file1 = new File(file);
                    file = CrawlerManager.changeFileUrl(file1);
                    Map<Object,Object> map = new HashMap<>();
                    map.put("id", Long.parseLong(CrawlerManager.randomNumber()));
                    map.put("name", title);
                    map.put("file", file);
                    downloads.add(map);
                }
                stationMDownloadData.setDownloads(JsonUtil.obj2String(downloads));

                finalMap.put("stationMDownloadData", stationMDownloadData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    StationMDownload stationMDownload = (StationMDownload) finalMap.get("stationMDownload");
                    StationMDownloadData stationMDownloadData = (StationMDownloadData) finalMap.get("stationMDownloadData");

                    // 落库 -> station_m_article
                    ApplicationContextProvider.getBeanByType(StationMDownloadMapper.class).insert(stationMDownload);
                    stationMDownload.setSortLevel(Math.toIntExact(stationMDownload.getId()));


                    // 落库 -> station_m_article_data
                    ApplicationContextProvider.getBeanByType(StationMDownloadDataMapper.class).insert(stationMDownloadData);
                    stationMDownload.setDataId(stationMDownloadData.getDataId());

                    // 更新 -> station_m_article
                    ApplicationContextProvider.getBeanByType(StationMDownloadMapper.class).updateById(stationMDownload);

                }
                return null;
            });
        }
        finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "10";
    }
}
