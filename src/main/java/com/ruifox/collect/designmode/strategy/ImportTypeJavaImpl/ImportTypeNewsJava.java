package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.station.StationCategoryMapper;
import com.ruifox.collect.dao.mapper.station.StationMArticleDataMapper;
import com.ruifox.collect.dao.mapper.station.StationMArticleMapper;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.station.StationCategory;
import com.ruifox.collect.module.entity.station.StationMArticle;
import com.ruifox.collect.module.entity.station.StationMArticleData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Component
@Slf4j
public class ImportTypeNewsJava implements ImportTypeJava {

    private static final Pattern EMOJI_PATTERN = Pattern.compile(
            "[\u2600-\u27BF\u2B50-\u2B55\u3030\u303D\u3297\u3299]" +
                    "|\uD83C[\uDDE6-\uDDFF\uDF00-\uDFFF]" +
                    "|\uD83D[\uDC00-\uDE4F\uDE80-\uDEFF]" +
                    "|\uD83E[\uDD00-\uDDFF]"
    );

    public static String removeSpecialCharacters(String input) {
        if (input == null) {
            return null;
        }
        // 使用正则表达式替换匹配到的 4 字节 UTF - 8 字符为空字符串
        return EMOJI_PATTERN.matcher(input).replaceAll("");
    }

    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");
                if(i>800){
                    continue;
                }


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_article
                StationMArticle stationMArticle = new StationMArticle();

                stationMArticle.setDataId(-1L);
                stationMArticle.setCatId(collectTask.getImportCategoryId());
                stationMArticle.setPublishUserId(1);
                stationMArticle.setCreateTime(Double.valueOf(localRecordMap.get("publish_time").toString() + "000"));
                stationMArticle.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                stationMArticle.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                stationMArticle.setViews(Integer.parseInt(views));
                finalMap.put("stationMArticle", stationMArticle);

                StationMArticleData stationMArticleData = new StationMArticleData();

                stationMArticleData.setUuid(UUID.randomUUID().toString());
                stationMArticleData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));
                stationMArticleData.setSubTitle(null);
                stationMArticleData.setComefrom(StringUtils.trim(localRecordMap.get("come_from").toString()));
                stationMArticleData.setIsLink((Integer) localRecordMap.get("flag"));
                stationMArticleData.setIgnoreReason(null);
                stationMArticleData.setPhotographer(null);
                stationMArticleData.setPublishTime(Double.valueOf(localRecordMap.get("publish_time").toString() + "000"));

                // 作者处理
                String author = (String) localRecordMap.get("author");
                if (!StringUtils.isBlank(author)) {
                    author = author.trim();
                }
                stationMArticleData.setAuthor(author);

                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "正文解析失败", e.getMessage());
                }

                // 去除正文中的无效图片
                Elements select2 = document.select("img");
                for (Element e : select2) {
                    String attr = e.attr("src");
                    if (!attr.contains("/oss/") && !attr.contains(collectTask.getHost())) {
                        e.remove();
                    }
                }

                Elements imgList = document.select("img");
                Elements linkList = document.select("a");
                Elements videoList = document.select("video");

                // 缩略图处理 （只有一张图片）
                String thumb = localRecordMap.get("thumb").toString();
                if (StringUtils.isBlank(thumb)) {
                    if (!imgList.isEmpty()) {
                        Element element = imgList.get(0);
                        String imgUrl = element.attr("src");
                        boolean check = FileUtil.localUrlChick(imgUrl);
                        if (check) {
                            String attr = imgList.first().attr("src");
                            thumb = attr;
                        } else {
                            thumb = "";
                        }
                    } else {
                        thumb = "";
                    }
                }
                //调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMArticleData.setThumb(thumb);


                // 摘要处理
                String description = localRecordMap.get("description").toString();
                if (StringUtils.isBlank(description)) {
                    String text = document.text();
                    text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                stationMArticleData.setDescription(description);


                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                // 链接路径集合处理
                try {
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNotBlank(attachUrl)) {
                            if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                continue;
                            }
                            if (attachUrl.contains(".shtml")) {
                                element.attr("href", "");
                                continue;
                            }
                            boolean check = FileUtil.localUrlChick(attachUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                element.attr("href", url);
                            } else {
                                // 移除失效链接
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                // 视频路径集合处理
                try {
                    for (Element element : videoList) {
                        String videoUrl = element.attr("src");
                        if (StringUtils.isNotBlank(videoUrl)) {
                            String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                            element.attr("src", url);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                content = document.toString();

                stationMArticleData.setContent(content);

                // 外链处理，uri不变，将正文换成链接地址
                if (stationMArticleData.getIsLink().equals(1)) {
                    stationMArticleData.setContent(localRecordMap.get("target_url").toString());
                    finalMap.put("stationMArticle", stationMArticle);
                }

                finalMap.put("stationMArticleDate", stationMArticleData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    StationMArticle stationMArticle = (StationMArticle) finalMap.get("stationMArticle");
                    StationMArticleData stationMArticleData = (StationMArticleData) finalMap.get("stationMArticleDate");

                    // 落库 -> station_m_article
                    ApplicationContextProvider.getBeanByType(StationMArticleMapper.class).insert(stationMArticle);
                    stationMArticle.setSortLevel(Math.toIntExact(stationMArticle.getId()));


                    // 落库 -> station_m_article_data
                    ApplicationContextProvider.getBeanByType(StationMArticleDataMapper.class).insert(stationMArticleData);
                    stationMArticle.setDataId(stationMArticleData.getDataId());

                    // 更新 -> station_m_article
                    ApplicationContextProvider.getBeanByType(StationMArticleMapper.class).updateById(stationMArticle);

                }
                return null;
            });
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "1";
    }
}
