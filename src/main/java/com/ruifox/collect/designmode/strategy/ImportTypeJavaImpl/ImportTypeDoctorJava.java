package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.station.StationCategoryMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorDataMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorMapper;
import com.ruifox.collect.dao.mapper.station.StationModelFieldMapper;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.station.StationCategory;
import com.ruifox.collect.module.entity.station.StationMDoctor;
import com.ruifox.collect.module.entity.station.StationMDoctorData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeDoctorJava implements ImportTypeJava {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            // 得到医生职称和医生职称ID的集合
            Map<Object, Object> record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithDocPosition(stationCategory.getModelId());
            String setting = (String) record.get("setting");
            Map<Object, Object> map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> docPositionNum2docPositionMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> docPosition2DocPositionNumMap = new HashMap<>();
            docPositionNum2docPositionMaps.forEach(map1 -> docPosition2DocPositionNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));

            //得到医生院内职称集合
            /*record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper2.class)
                    .selectByModelIdWithHosPosition(stationCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> hosPositionNum2hosPositionMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> hosPosition2HosPositionNumMap = new HashMap<>();
            hosPositionNum2hosPositionMaps.forEach(map1 -> hosPosition2HosPositionNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));*/

            //得到医生教务职称集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithEduPosition(stationCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> eduPositionNum2eduPositionMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> eduPosition2EduPositionNumMap = new HashMap<>();
            eduPositionNum2eduPositionMaps.forEach(map1 -> eduPosition2EduPositionNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));

            //得到医生教学岗位集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithEduPost(stationCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> eduPostNum2eduPostMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> eduPost2EduPostNumMap = new HashMap<>();
            eduPostNum2eduPostMaps.forEach(map1 -> eduPost2EduPostNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));


            // 获取医生的部门集合
            LambdaQueryWrapper<StationCategory> lqw = new LambdaQueryWrapper<>();
            List<Integer> ids = new ArrayList<>();
            lqw.in(StationCategory::getType, 9);
            List<StationCategory> stationCategories = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectList(lqw);

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入，为了排序，时间更近的需要后倒入保证id更大，排序越大排在更新的位置
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // 根据采集对象处理数据至对应类型 处理至station_m_doctor
                StationMDoctor stationMDoctor = new StationMDoctor();

                stationMDoctor.setDataId(-1L);
                stationMDoctor.setCatId(collectTask.getImportCategoryId());
                stationMDoctor.setPublishUserId(1);
                stationMDoctor.setCreateTime((double) System.currentTimeMillis());
                stationMDoctor.setState(99);
                stationMDoctor.setIsLock(0);
                stationMDoctor.setIsTop(0);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                stationMDoctor.setUri("/" + uriRandom + ".html");

                finalMap.put("stationMDoctor", stationMDoctor);

                StationMDoctorData stationMDoctorData = new StationMDoctorData();

                stationMDoctorData.setUuid(UUID.randomUUID().toString());
                stationMDoctorData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));
                stationMDoctorData.setIgnoreReason(null);

                // 头像处理
                String thumb = localRecordMap.get("thumb").toString();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMDoctorData.setThumb(thumb);

                // 性别处理
                /*String sex = localRecordMap.get("sex").toString();
                if(StringUtils.isNotBlank(sex)) {
                    if (sex.equals("男")) {
                        stationMDoctorData.setSex(1);
                    } else if (sex.equals("女")) {
                        stationMDoctorData.setSex(2);
                    }
                }*/


                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "正文解析失败", e.getMessage());
                }

                // 去除正文中的无效图片
                Elements select2 = document.select("img");
                for (Element e : select2) {
                    String attr = e.attr("src");
                    if (!attr.contains("/oss/") && !attr.contains(collectTask.getHost())) {
                        e.remove();
                    }
                    if (attr.contains(".gif")) {
                        e.remove();
                    }
                }

                Elements imgList = document.select("img");

                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                content = document.toString();

                stationMDoctorData.setContent(content);

                String goodat = (String) localRecordMap.get("goodat");
                if (StringUtils.isNotBlank(goodat)) {
                    goodat = goodat.trim();
                } else {
                    goodat = "";
                }
                stationMDoctorData.setGoodat(goodat);

                // 科室处理
                String depart = String.valueOf(localRecordMap.get("depart")).trim();
                List<Integer> departInfoList = new ArrayList<>();
                if (depart.contains(",")) {
                    String[] departs = depart.split(",");
                    for (String departStr : departs) {
                        for (StationCategory category : stationCategories) {
                            if (departStr.equals(category.getName())) {
                                departInfoList.add((category.getId()));
                                break;
                            }
                        }
                    }
                } else {
                    for (StationCategory category : stationCategories) {
                        if (depart.equals(category.getName())) {
                            departInfoList.add((category.getId()));
                            break;
                        }
                    }
                }
                if (departInfoList.isEmpty()) {
                    throw new RuntimeException("请检查是否有当前科室栏目: " + localRecordMap.get("depart"));
                }
                stationMDoctorData.setDepart(JSONObject.toJSONString(departInfoList));

                // 医师职称处理
                String docPositionStr = (String) localRecordMap.get("doc_position");
                if (StringUtils.isNotBlank(docPositionStr)) {
                    Integer docPosition = docPosition2DocPositionNumMap.get(docPositionStr);
                    stationMDoctorData.setDocPosition(docPosition);
                }

                //教务职称处理
                String eduPositionStr = (String) localRecordMap.get("edu_position");
                Integer eduPosition = eduPosition2EduPositionNumMap.get(eduPositionStr);
                stationMDoctorData.setEduPosition(eduPosition);

                //教学岗位处理
                String eduPostStr = (String) localRecordMap.get("edu_post");
                Integer eduPost = eduPost2EduPostNumMap.get(eduPostStr);
                stationMDoctorData.setEduPost(eduPost);


                finalMap.put("stationMDoctorData", stationMDoctorData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    StationMDoctor stationMDoctor = (StationMDoctor) finalMap.get("stationMDoctor");
                    StationMDoctorData stationMDoctorData = (StationMDoctorData) finalMap.get("stationMDoctorData");

                    // 落库 -> station_m_doctor
                    ApplicationContextProvider.getBeanByType(StationMDoctorMapper.class).insert(stationMDoctor);
                    stationMDoctor.setSortLevel(Math.toIntExact(stationMDoctor.getId()));

                    // 落库 -> station_m_doctor_data
                    ApplicationContextProvider.getBeanByType(StationMDoctorDataMapper.class).insert(stationMDoctorData);
                    stationMDoctor.setDataId(stationMDoctorData.getDataId());

                    // 更新 -> station_m_doctor
                    ApplicationContextProvider.getBeanByType(StationMDoctorMapper.class).updateById(stationMDoctor);

                }
                return null;
            });
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "2";
    }

}
