package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeImageJava implements ImportTypeJava {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_article
                StationMImage stationMImage = new StationMImage();

                stationMImage.setDataId(-1);
                stationMImage.setCatId(collectTask.getImportCategoryId());
                stationMImage.setPublishUserId(1);
                stationMImage.setCreateTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000"));
                stationMImage.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                stationMImage.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                stationMImage.setViews(Integer.parseInt(views));
                finalMap.put("stationMImage", stationMImage);

                StationMImageData stationMImageData = new StationMImageData();

                stationMImageData.setUuid(UUID.randomUUID().toString());
                stationMImageData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));
                stationMImageData.setComefrom(StringUtils.trim(localRecordMap.get("come_from").toString()));
                stationMImageData.setPhotographer(null);


                // 作者处理
                String author = (String) localRecordMap.get("author");
                if (!StringUtils.isBlank(author)) {
                    author = author.trim();
                }
                stationMImageData.setAuthor(author);

                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "正文解析失败", e.getMessage());
                }

                // 去除正文中的无效图片
                Elements select2 = document.select("img");
                for (Element e : select2) {
                    String attr = e.attr("src");
                    if (!attr.contains("/oss/") && !attr.contains(collectTask.getHost())) {
                        e.remove();
                    }
                }

                Elements imgList = document.select("img");

                // 摘要处理
                String description = localRecordMap.get("description").toString();
                if (StringUtils.isBlank(description)) {
                    String text = document.text();
                    text = "    " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                stationMImageData.setDescription(description);


                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                //----------------------------------对图片放置方法的处理
                String images = "";
                stationMImageData.setImages(images);

                finalMap.put("stationMImageData", stationMImageData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    StationMImage stationMImage = (StationMImage) finalMap.get("stationMImage");
                    StationMImageData stationMImageData = (StationMImageData) finalMap.get("stationMImageData");

                    // 落库 -> station_m_image
                    ApplicationContextProvider.getBeanByType(StationMImageMapper.class).insert(stationMImage);
                    stationMImage.setSortLevel(Math.toIntExact(stationMImage.getId()));


                    // 落库 -> station_m_image_data
                    ApplicationContextProvider.getBeanByType(StationMImageDataMapper.class).insert(stationMImageData);
                    stationMImage.setDataId(stationMImageData.getDataId());

                    // 更新 -> station_m_image
                    ApplicationContextProvider.getBeanByType(StationMImageMapper.class).updateById(stationMImage);

                }
                return null;
            });
        }
        finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "6";
    }
}
