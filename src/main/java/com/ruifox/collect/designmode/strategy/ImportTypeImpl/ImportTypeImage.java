package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCImageDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCImageMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.hms.HmsSiteMapper;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.*;
import com.ruifox.collect.module.entity.hms.HmsCImage;
import com.ruifox.collect.module.entity.hms.HmsCImageData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.hms.HmsSite;
import com.ruifox.collect.util.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;


/**
 * Description: ImportTypeImage
 *
 * <AUTHOR>
 * @date 2024/6/5 下午2:48
 * @fileName ImportTypeImage.java
 **/
@Component
@Slf4j
public class ImportTypeImage implements ImportType {
    /**
     * 单任务导入
     *
     * @param collectTask     采集任务实体
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO: ZhangXinYu 2024/6/5 | 图片页面导入
            log.info("当前正在导入图片页面数据...");
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());
            
            // // TODO: ZhangXinYu 2024/6/17 | 切换本地数据源
            DynamicDataSource.changeDefaultDataSource();
            
            // 构造本地路径替换前缀
            String localPathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/";  // 示例路径：C:\www.cdlyy.com\
            
            // 当前任务对应数据表名
            String dataTableName = collectTask.getDataTableName();
            
            // 查询对应数据表名中的所有数据
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderByReleased(dataTableName);
            
            // 当前任务对应文件表名
            String fileTableName = collectTask.getFileTableName();

            // TODO: ZhangXinYu 2024/6/17 | 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class)
                                                                .selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请设置当前导入的栏目ID!");
                return;
            }

            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain()+"oss/";
            
            DynamicDataSource.changeDefaultDataSource();
            
            // TODO: ZhangXinYu 2024/6/17 | 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            
            // 处理本地数据至hms_c_image和hms_c_image_data
            long i = 1;
            for (Map<Object, Object> localRecordMap : localRecords) {
                System.out.println("当前正在处理第 " + i++ + " 条数据...");
                
                // TODO: ZhangXinYu 2024/7/2 | 新增特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                }else {
                    specialHanding.imageSpecialHanding(localRecordMap, null);
                }
                
                HashMap<String, Object> finalMap = new HashMap<>();
                
                // 处理至hms_c_image_data
                // 构建images
                List<Map<String, Object>> images = new ArrayList<>();
                
                // 获取图片信息
                String content = localRecordMap.get("content").toString();
                Document document = Jsoup.parse(content);
                document.select("img").forEach(element -> {
                    String src = element.attr("src");
                    if (src != null && !src.isEmpty()) {
                        Map<String, Object> image = new HashMap<>();
                        image.put("id", System.currentTimeMillis());
                        try {
                            image.put("title", URLEncoder.encode((String) localRecordMap.get("title"),"UTF-8"));
                        } catch (UnsupportedEncodingException e) {
                            throw new RuntimeException(e);
                        }
                        src = src.replace(localPathPrefix, cloudPathPrefix);
                        image.put("url", src);
                        images.add(image);
                    }
                });
                
                
                Object views = localRecordMap.get("views");
                if (views == null) {
                    views = 0;
                } else {
                    if (views instanceof String) {
                        views = Integer.valueOf((String) views);
                    } else if (views instanceof Integer) {
                        views = (Integer) views;
                    } else {
                        views = 0;
                    }
                }
                
                // TODO: ZhangXinYu 2024/6/17 | 来源处理
                String comeFrom = (String) localRecordMap.get("come_from");
                if (comeFrom == null || comeFrom.isEmpty()) {
                    comeFrom = "";
                } else {
                    if (comeFrom.equals("管理员")) {
                        comeFrom = "";
                    }
                }
                
                // TODO: ZhangXinYu 2024/6/17 | 作者处理
                String author = (String) localRecordMap.get("author");
                if (author == null || author.isEmpty()) {
                    author = String.valueOf(new ArrayList<>());
                } else {
                    if (author.equals("管理员")) {
                        author = String.valueOf(new ArrayList<>());
                    } else {
                        author = author.replace("，", " ");
                        author = author.replace(",", " ");
                        String[] authors = author.split(" ");
                        author = Arrays.toString(authors);
                    }
                }
                
                // TODO: ZhangXinYu 2024/6/17 | 摄影师处理
                String photographer = (String) localRecordMap.get("photographer");
                if (photographer == null || photographer.isEmpty()) {
                    photographer = String.valueOf(new ArrayList<>());
                } else {
                    if (photographer.equals("管理员")) {
                        photographer = String.valueOf(new ArrayList<>());
                    } else {
                        photographer = photographer.replace("，", " ");
                        photographer = photographer.replace(",", " ");
                        String[] photographers = photographer.split(" ");
                        photographer = Arrays.toString(photographers);
                    }
                }
                
                
                HmsCImageData hmsCImageData = HmsCImageData
                        .builder()
                        .title((String) localRecordMap.get("title"))
                        .images(JsonUtil.obj2String(images))
                        .comefrom(comeFrom)
                        .author(author)
                        .photographer(photographer)
                        .description((String) localRecordMap.get("description"))
                        .views((Integer) views)
                        .build();
                
                // 在插入时间时进行一次验证，防止没有转换为秒时间戳
                String publishTime = String.valueOf(localRecordMap.get("publish_time"));
                if (StringUtils.isBlank(publishTime)) {
                    publishTime = "0";
                }
                
                // 处理至hms_c_image
                HmsCImage hmsCImage = HmsCImage
                        .builder()
                        .dataid(hmsCImageData.getDid())
                        .catid(Integer.valueOf(String.valueOf(hmsCategory.getId())))
                        .username(1)
                        .endOperator(1)
                        .isLocked(0)
                        .url((String) localRecordMap.get("origin_url"))
                        .islink((int) localRecordMap.get("flag") == 1 ? null : (String) localRecordMap.get("target_url"))
                        .top(0)
                        .listorder(i++)
                        .state(0)
                        .status(99)
                        .publishTime(Integer.valueOf(publishTime))
                        .expireTime(0)
                        .inputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)))
                        .updateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)))
                        .build();
                
                // TODO: ZhangXinYu 2024/6/17 |
                finalMap.put("hmsCImageData", hmsCImageData);
                finalMap.put("hmsCImage", hmsCImage);
                finalMaps.add(finalMap);
            }
            

            // 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();

            // 落库
            TransactionUtil.executeInTransaction(() -> {         // NOTE: ZhangXinYu 2024/6/17 | 这里做编程式事务，因为有切换数据源，，声明式事务用不了，，且编程式事务中不能切换数据源
                
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCImageData hmsCImageData = (HmsCImageData) finalMap.get("hmsCImageData");
                    HmsCImage hmsCImage = (HmsCImage) finalMap.get("hmsCImage");
                    
                    // 落库 -> hms_c_image
                    ApplicationContextProvider.getBeanByType(HmsCImageMapper.class).insert(hmsCImage);
                    hmsCImage.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCImage.getId()) + ".html");
                    hmsCImage.setListorder(hmsCImage.getId());
                    hmsCImage.setDataid(hmsCImage.getId());
                    
                    // 更新 -> hms_c_image
                    ApplicationContextProvider.getBeanByType(HmsCImageMapper.class).updateById(hmsCImage);
                    hmsCImageData.setDid(hmsCImage.getId());
                    
                    // 落库 -> hms_c_image_data
                    ApplicationContextProvider.getBeanByType(HmsCImageDataMapper.class).insert(hmsCImageData);
                }
                
                return null;
            });
            
            log.info("当前导入图片页面数据完成...");
            
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            DynamicDataSource.changeDefaultDataSource();
        }
    }
    
    /**
     * 服务于多导入类型下的策略模式(新闻/医生/单页/图集...)
     *
     * @return 任务类型ID
     */
    @Override
    public String getBeanFlag() {
        return "6";  // TODO: ZhangXinYu 2024/6/5 | 图集(图片 = 6)
    }
}
