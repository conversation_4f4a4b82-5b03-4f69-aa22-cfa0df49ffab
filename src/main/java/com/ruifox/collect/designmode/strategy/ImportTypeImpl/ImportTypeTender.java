package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.ElementUtil;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ImportTypeTender implements ImportType {
    @Override
    public void singleImport(CollectTask collectTask) {
        try {
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());

            // TODO 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();
            // 构造本地路径替换前缀
            String localPathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/"; // 示例路径：C:/www.cdlyy.com/
            // 基本数据表名
            String dataTableName = collectTask.getDataTableName();
            // 查询本地所有数据
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(dataTableName);

            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.error("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            }
            log.info("当前导入栏目ID:{},当前导入栏目名称:{}", hmsCategory.getId(), hmsCategory.getName());
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain()+"oss/";

            // TODO 切回本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();

            // 处理本地数据至hms_c_news和hms_c_news_data
            long i = 1;
            String sortTmp = "0";
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if(sort.equals(sortTmp)){
                    continue;
                }
                sortTmp = sort;

                System.out.println("当前正在处理第 " + (i++) + " 条数据...");
                HashMap<String, Object> finalMap = new HashMap<>();

                // 导入时的特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                } else {
                    specialHanding.newsSpecialHanding(localRecordMap, collectTask);
                }

                // TODO 处理至hms_c_news\
                HmsCTender hmsCTender = new HmsCTender();
                hmsCTender.setDataid(1L);
                hmsCTender.setOldCatid(null);
                hmsCTender.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCTender.setUsername(1);
                hmsCTender.setEndOperator(1);
                hmsCTender.setUrl("");
                hmsCTender.setIslink((int) localRecordMap.get("flag") == 0 ? null : (String) localRecordMap.get("target_url"));
                hmsCTender.setTop(0);
                hmsCTender.setListorder(-99L);
                hmsCTender.setSort(1);
                hmsCTender.setState(0);
                hmsCTender.setStatus(99);

                // FIXME
                String publishTime = String.valueOf(localRecordMap.get("publish_time"));
                if (StringUtils.isBlank(publishTime)) {
                    publishTime = "0";
                }
                hmsCTender.setPublishTime(Integer.parseInt(publishTime));
                hmsCTender.setExpireTime(0);
                hmsCTender.setInputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCTender.setUpdateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCTender.setViews(0);
                hmsCTender.setIsLocked(0);

                finalMap.put("hmsCTender", hmsCTender);


                // TODO 处理至hms_c_news_data
                HmsCTenderData hmsCTenderData = new HmsCTenderData();
                hmsCTenderData.setDid(hmsCTender.getDataid());

                hmsCTenderData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));

                String comefrom = String.valueOf(localRecordMap.get("comefrom"));
                if(StringUtils.isBlank(comefrom))
                    comefrom = "";
                hmsCTenderData.setComefrom(comefrom);

                String subject = String.valueOf(localRecordMap.get("subject"));
                if(StringUtils.isBlank(subject))
                    subject = "";
                hmsCTenderData.setSubject(subject);

                String applyform = String.valueOf(localRecordMap.get("applyform"));
                if(StringUtils.isBlank(applyform))
                    applyform = "";
                hmsCTenderData.setApplyform(applyform);

                String openTime = String.valueOf(localRecordMap.get("open_time"));
                if(StringUtils.isBlank(openTime))
                    openTime = "0";
                hmsCTenderData.setOpenTime(Integer.parseInt(openTime));

                String signTime = String.valueOf(localRecordMap.get("sign_time"));
                if(StringUtils.isBlank(signTime))
                    signTime = "0";
                hmsCTenderData.setSignTime(Integer.parseInt(signTime));

                String endTime = String.valueOf(localRecordMap.get("end_time"));
                if(StringUtils.isBlank(endTime))
                    endTime = "0";
                hmsCTenderData.setEndTime(Integer.parseInt(endTime));

                hmsCTenderData.setContent("");

                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                }
                Elements imgList = document.select("img");
                for (Element element : imgList) {
                    String imgUrl = element.attr("src");
                    if (StringUtils.isNotBlank(imgUrl)) {
                        boolean check = FileUtil.localUrlChick(imgUrl);
                        if (!check) {
                            element.remove();
                        }
                    }
                }

                // 摘要处理
                String description = localRecordMap.get("description").toString();
                if(StringUtils.isBlank(description)){
                    String text = document.text();
                    text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                hmsCTenderData.setDescription(description);

                // 去除正文中表格的标签的style属性
                content = ElementUtil.getContentRemoveHtml(document);
                // TODO 这里图片处理完了，可以替换掉正文中的文件路径了
                if (StringUtils.isNotBlank(content)) {
                    content = content.replace(localPathPrefix, cloudPathPrefix);
                }
                hmsCTenderData.setContent(content);

                finalMap.put("hmsCTenderData", hmsCTenderData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCTender hmsCTender = (HmsCTender) finalMap.get("hmsCTender");
                    HmsCTenderData hmsCTenderData = (HmsCTenderData) finalMap.get("hmsCTenderData");

                    // 落库 -> hms_c_tender
                    ApplicationContextProvider.getBeanByType(HmsCTenderMapper.class).insert(hmsCTender);
                    hmsCTender.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCTender.getId()) + ".html");
                    hmsCTender.setListorder(hmsCTender.getId());
                    hmsCTender.setDataid(hmsCTender.getId());

                    // 更新 -> hms_c_tender
                    ApplicationContextProvider.getBeanByType(HmsCTenderMapper.class).updateById(hmsCTender);
                    hmsCTenderData.setDid(hmsCTender.getId());

                    // 落库 -> hms_c_tender_data
                    ApplicationContextProvider.getBeanByType(HmsCTenderDataMapper.class).insert(hmsCTenderData);
                }
                return null;
            });
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "9";
    }
}
