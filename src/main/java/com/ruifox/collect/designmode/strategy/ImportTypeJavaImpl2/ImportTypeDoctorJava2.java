package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl2;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;

import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.designmode.strategy.ImportTypeJava2;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;

import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.resource.FolderResource;

import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.module.entity.station2.StationMDoctorData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeDoctorJava2 implements ImportTypeJava2 {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
//            DynamicDataSource.changeDynamicDataSource();
            DynamicDataSource.changeBuildDynamicDataSource();

            // 按栏目ID查询类别
            StationCategory2 stationCategory2 = ApplicationContextProvider.getBeanByType(StationCategoryMapper2.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory2 == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory2.getId(), stationCategory2.getName());
            }

            // TODO 切换远程数据源
            DynamicDataSource.changeResourceDynamicDataSource();
            // 得到医生职称和医生职称ID的集合
            Map<Object, Object> record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper2.class)
                    .selectByModelIdWithDocPosition(stationCategory2.getModelId());
            String setting = (String) record.get("setting");
            Map<Object, Object> map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> docPositionNum2docPositionMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> docPosition2DocPositionNumMap = new HashMap<>();
            docPositionNum2docPositionMaps.forEach(map1 -> docPosition2DocPositionNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));

            //得到医生院内职称集合
            /*record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper2.class)
                    .selectByModelIdWithHosPosition(stationCategory2.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> hosPositionNum2hosPositionMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> hosPosition2HosPositionNumMap = new HashMap<>();
            hosPositionNum2hosPositionMaps.forEach(map1 -> hosPosition2HosPositionNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));*/

            //得到医生教务职称集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper2.class)
                    .selectByModelIdWithEduPosition(stationCategory2.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> eduPositionNum2eduPositionMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> eduPosition2EduPositionNumMap = new HashMap<>();
            eduPositionNum2eduPositionMaps.forEach(map1 -> eduPosition2EduPositionNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));

            //得到医生教学岗位集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper2.class)
                    .selectByModelIdWithEduPost(stationCategory2.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<Object, Object>> eduPostNum2eduPostMaps = (List<Map<Object, Object>>) map.get("options");
            Map<String, Integer> eduPost2EduPostNumMap = new HashMap<>();
            eduPostNum2eduPostMaps.forEach(map1 -> eduPost2EduPostNumMap.put(map1.get("label").toString(), Integer.valueOf(map1.get("value").toString())));


            // 获取医生的部门集合
            DynamicDataSource.changeBuildDynamicDataSource();
            LambdaQueryWrapper<StationCategory2> lqw = new LambdaQueryWrapper<>();
            List<Integer> ids = new ArrayList<>();
            lqw.in(StationCategory2::getType, 9);
            List<StationCategory2> stationCategories = ApplicationContextProvider.getBeanByType(StationCategoryMapper2.class).selectList(lqw);

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入，为了排序，时间更近的需要后倒入保证id更大，排序越大排在更新的位置
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // 根据采集对象处理数据至对应类型 处理至station_m_doctor
                StationMReference reference = new StationMReference();
                reference.setDataId(-1L);
                reference.setCatId(collectTask.getImportCategoryId());
//                stationMDoctor2.setPublishUserId(1);
                reference.setPublishTime((double) System.currentTimeMillis());
                reference.setState(99);
//                stationMDoctor2.setIsLock(0);
                reference.setIsTop(0);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                reference.setUri("/" + uriRandom + ".html");

                finalMap.put("stationMDoctor", reference);


                StationMDoctorData2 stationMDoctorData2 = new StationMDoctorData2();

                stationMDoctorData2.setUuid(UUID.randomUUID().toString());
                stationMDoctorData2.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));
                stationMDoctorData2.setIgnoreReason(null);
                stationMDoctorData2.setCreateTime((double) System.currentTimeMillis());
                stationMDoctorData2.setUpdateTime((double) System.currentTimeMillis());
                stationMDoctorData2.setCreateUserId(1);
                stationMDoctorData2.setUpdateUserId(1);
                stationMDoctorData2.setState(2);
                // 头像处理
                String thumb = localRecordMap.get("thumb").toString();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMDoctorData2.setThumb(thumb);

                // 性别处理
                /*String sex = localRecordMap.get("sex").toString();
                if(StringUtils.isNotBlank(sex)) {
                    if (sex.equals("男")) {
                        stationMDoctorData2.setSex(1);
                    } else if (sex.equals("女")) {
                        stationMDoctorData2.setSex(2);
                    }
                }*/


                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;

                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "正文解析失败", e.getMessage());
                }

                // 去除正文中的无效图片
                Elements select2 = document.select("img");
                for (Element e : select2) {
                    String attr = e.attr("src");
                    if (!attr.contains("/oss/") && !attr.contains(collectTask.getHost())) {
                        e.remove();
                    }
                    if (attr.contains(".gif")) {
                        e.remove();
                    }
                }

                Elements imgList = document.select("img");

                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                content = document.toString();

                stationMDoctorData2.setContent(content);

                String goodat = (String) localRecordMap.get("goodat");
                if (StringUtils.isNotBlank(goodat)) {
                    goodat = goodat.trim();
                } else {
                    goodat = "";
                }
                stationMDoctorData2.setGoodat(goodat);

                // 科室处理
                String depart = String.valueOf(localRecordMap.get("depart")).trim();
                List<Integer> departInfoList = new ArrayList<>();
                if (depart.contains(",")) {
                    String[] departs = depart.split(",");
                    for (String departStr : departs) {
                        for (StationCategory2 category : stationCategories) {
                            if (departStr.equals(category.getName())) {
                                departInfoList.add((category.getId()));
                                break;
                            }
                        }
                    }
                } else {
                    for (StationCategory2 category : stationCategories) {
                        if (depart.equals(category.getName())) {
                            departInfoList.add((category.getId()));
                            break;
                        }
                    }
                }
//                if (departInfoList.isEmpty()) {
//                    throw new RuntimeException("请检查是否有当前科室栏目: " + localRecordMap.get("depart"));
//                }
                stationMDoctorData2.setDepart(JSONObject.toJSONString(departInfoList));

                // 医师职称处理
                String docPositionStr = (String) localRecordMap.get("doc_position");
                if (StringUtils.isNotBlank(docPositionStr)) {
                    Integer docPosition = docPosition2DocPositionNumMap.get(docPositionStr);
                    stationMDoctorData2.setDocPosition(docPosition);
                }

                //教务职称处理
                String eduPositionStr = (String) localRecordMap.get("edu_position");
                Integer eduPosition = eduPosition2EduPositionNumMap.get(eduPositionStr);
                stationMDoctorData2.setEduPosition(eduPosition);

                //教学岗位处理
                String eduPostStr = (String) localRecordMap.get("edu_post");
                Integer eduPost = eduPost2EduPostNumMap.get(eduPostStr);
                stationMDoctorData2.setEduPost(eduPost);


                finalMap.put("stationMDoctorData", stationMDoctorData2);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeResourceDynamicDataSource();

            // TODO 添加事务的处理

            for (Map<String, Object> finalMap : finalMaps) {
                StationMReference stationMDoctor2 = (StationMReference) finalMap.get("stationMDoctor");
                StationMDoctorData2 stationMDoctorData2 = (StationMDoctorData2) finalMap.get("stationMDoctorData");

                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 落库 -> station_m_reference
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).insert(stationMDoctor2);

                // TODO 切换为动态数据源
                DynamicDataSource.changeResourceDynamicDataSource();
                // 落库 -> resource_data_doctor
                ApplicationContextProvider.getBeanByType(StationMDoctorDataMapper2.class).insert(stationMDoctorData2);

                //落库 -> folder_resource
                FolderResource folderResource= new FolderResource().builder()
                                .userId(1).folderId(2).modelId(stationCategory2.getModelId())
                        .resourceId(stationMDoctorData2.getDataId())
                                .createTime((double) System.currentTimeMillis())
                                        .updateTime((double) System.currentTimeMillis())
                                                .version(1).sort(stationMDoctorData2.getDataId())
                                                                .listOrder(0).state(2).isDeleted(0).build();
                ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

                stationMDoctor2.setDataId(Long.valueOf(folderResource.getId()));
                stationMDoctor2.setSortLevel(Math.toIntExact(stationMDoctor2.getId()));


                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 更新 -> station_m_reference
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).updateById(stationMDoctor2);

            };
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "2";
    }

}
