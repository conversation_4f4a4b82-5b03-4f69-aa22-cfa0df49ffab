package com.ruifox.collect.designmode.strategy;

import com.ruifox.collect.module.entity.CollectTask;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.slf4j.LoggerFactory;
import us.codecraft.webmagic.Request;

import java.util.Map;

/**
 * 针对数据采集的特殊处理
 */
public interface WebSpecialHanding {

    /**
     * 服务于多医院多任务类型下的策略模式
     * @return 域名 + 任务类型
     */
    String getBeanFlag();

    /**
     * 列表项
     */
    void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request);

    /**
     * 详情页
     */
    void specialHandingForNotEmbedded(Map<String, String> independentDataMap, CollectTask collectTask, Request request);


     // TODO 登录页初始化（一般用于网站首页的初始化，例如需要通过登录或者点击到指定的页面）
    default void loginInit(RemoteWebDriver driver, CollectTask collectTask) {
        LoggerFactory.getLogger(WebSpecialHanding.class).info("driver登录页-->不需要特殊处理....");
    }

    // TODO 打开页初始化（一般用于页面源码下载时，例如触发点击事件让元素出现）
    default void openInit(RemoteWebDriver driver, CollectTask collectTask) {
        LoggerFactory.getLogger(WebSpecialHanding.class).info("driver打开页-->不需要特殊处理....");
    }

    // TODO 判断链接是否需要下载，true -> 需要进行下载 ; false -> 不需要进行下载
    default boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask){
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || url.contains(collectTask.getHost())
                || url.contains("/oss/") || url.contains("mailto")) {
            return false;
        }
        return true;
    }

}
