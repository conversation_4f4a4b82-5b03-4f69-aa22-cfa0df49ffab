package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.HmsCMans;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.hms.HmsSite;
import com.ruifox.collect.util.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ImportTypeDoctor implements ImportType {

    @Override
    public void singleImport(CollectTask collectTask) {
        try {
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());

            // TODO 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();
            // 构造本地路径替换前缀
            String localPathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/"; // 示例路径：C:/Collect_Data/www.cdlyy.com/
            // 基本数据表名
            String dataTableName = collectTask.getDataTableName();
            // 查询本地所有数据(按项目组要求的排序规则进行排序)
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(dataTableName);

            // 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();

            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.error("请设置当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            }
            log.info("当前导入栏目ID:{},当前导入栏目名称:{}", hmsCategory.getId(), hmsCategory.getName());
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain() + "oss/";

            // 得到医生职称和医生职称ID的集合
            Map<Object, Object> record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithDocPosition(hmsCategory.getModelId());
            String setting = (String) record.get("setting");
            Map<Object, Object> map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> docPositionNum2docPositionMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> docPosition2DocPositionNumMap = new HashMap<>();
            docPositionNum2docPositionMaps.forEach(map1 -> docPosition2DocPositionNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            //得到医生院内职称集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithHosPosition(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> hosPositionNum2hosPositionMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> hosPosition2HosPositionNumMap = new HashMap<>();
            hosPositionNum2hosPositionMaps.forEach(map1 -> hosPosition2HosPositionNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            //得到医生教务职称集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithEduPosition(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> eduPositionNum2eduPositionMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> eduPosition2EduPositionNumMap = new HashMap<>();
            eduPositionNum2eduPositionMaps.forEach(map1 -> eduPosition2EduPositionNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            //得到医生教学岗位集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithEduPost(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> eduPostNum2eduPostMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> eduPost2EduPostNumMap = new HashMap<>();
            eduPostNum2eduPostMaps.forEach(map1 -> eduPost2EduPostNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            //得到医生专家级别集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithLevel(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> levelNum2eduPostMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> level2EduPostNumMap = new HashMap<>();
            levelNum2eduPostMaps.forEach(map1 -> level2EduPostNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            // 获取医生的部门集合
            LambdaQueryWrapper<HmsCategory> lqw = new LambdaQueryWrapper<>();
            lqw.eq(HmsCategory::getPid, 5)
                    .isNull(HmsCategory::getDeletedAt);
            List<HmsCategory> hmsCategories = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectList(lqw);

            // TODO 切回本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();

            // 处理本地数据至hms_c_mans和hms_c_mans_data
            long i = 1;
            String sortTmp = "0";
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;

                System.out.println("当前正在处理第 " + (i++) + " 条数据...");

                // 获取正文
                Object contentObj = localRecordMap.get("content");
                String content;
                if (contentObj != null) {
                    content = (String) contentObj;
                } else {
                    content = "";
                }
                Document document = null;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                }
                ElementUtil.deleteNoContent(document);

                // 特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                } else {
                    specialHanding.doctorSpecialHanding(localRecordMap, collectTask);
                }

                HashMap<String, Object> finalMap = new HashMap<>();

                // 9.1 处理至hms_c_mans
                HmsCMans hmsCMans = new HmsCMans();
                hmsCMans.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCMans.setUsername(1);
                hmsCMans.setStatus((byte) 99);
                hmsCMans.setState(0);
                hmsCMans.setPublishTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCMans.setExpireTime(0);
                hmsCMans.setInputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCMans.setUpdateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));

                Elements imgList = document.select("img");
                hmsCMans.setContentImageNum(imgList.size());
                Elements linkList = document.select("a");
                hmsCMans.setContentLinkNum(linkList.size());

                hmsCMans.setTop(0L);
                hmsCMans.setSort(0);
                hmsCMans.setOldCatid(null);
                hmsCMans.setEndOperator(1);
                hmsCMans.setIsLocked(false);

                // 处理至hms_c_mans_data
                HmsCMansData hmsCMansData = new HmsCMansData();
                hmsCMansData.setDid(hmsCMans.getId());

                // 姓名处理
                String title = (String) localRecordMap.get("title");
                if (StringUtils.isBlank(title)) {
                    title = "";
                } else {
                    title = StringUtils.trim(title);
                }
                hmsCMansData.setTitle(title);

                // 封面处理
                String thumb = (String) localRecordMap.get("thumb");
                if (StringUtils.isBlank(thumb)) {
                    hmsCMansData.setThumb(null);
                } else {
                    thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                    thumb = thumb.replace("\\", "/");
                    hmsCMansData.setThumb(thumb);
                }

                // 科室处理
                String depart = (String) localRecordMap.get("depart");
                String departId = "";
                if(StringUtils.isNotBlank(depart)) {
                    if (depart.contains(",")) {
                        String[] departs = depart.split(",");
                        for (String departStr : departs) {
                            for (HmsCategory category : hmsCategories) {
                                if (departStr.equals(category.getName())) {
                                    departId += category.getId() + ",";
                                }
                            }
                        }
                        departId = departId.substring(0, departId.length() - 1);
                    } else {
                        for (HmsCategory category : hmsCategories) {
                            if (depart.equals(category.getName())) {
                                departId += category.getId();
                            }
                        }
                    }
                    if (departId.isBlank()) {
                        throw new RuntimeException("请检查是否有当前科室栏目: " + localRecordMap.get("depart"));
                    }
                }
                hmsCMansData.setDepart("");

                // 医师职称处理
                String docPositionStr = (String) localRecordMap.get("doc_position");
                if (StringUtils.isBlank(docPositionStr)) {
                    hmsCMansData.setDocPosition(null);
                } else {
                    if (docPositionStr.equals("主任技师")) {
                        docPositionStr = docPositionStr.replace("主任技师", "主任技师(主任检验师)");
                    } else if (docPositionStr.equals("主任检验师")) {
                        docPositionStr = docPositionStr.replace("主任检验师", "主任技师(主任检验师)");
                    } else if (docPositionStr.equals("副主任技师")) {
                        docPositionStr = docPositionStr.replace("副主任技师", "副主任技师(副主任检验师)");
                    } else if (docPositionStr.equals("副主任检验师")) {
                        docPositionStr = docPositionStr.replace("副主任检验师", "副主任技师(副主任检验师)");
                    } else if (docPositionStr.equals("主管技师")) {
                        docPositionStr = docPositionStr.replace("主管技师", "主管技师(主管检验师)");
                    } else if (docPositionStr.equals("主管检验师")) {
                        docPositionStr = docPositionStr.replace("主管检验师", "主管技师(主管检验师)");
                    } else if (docPositionStr.equals("技师")) {
                        docPositionStr = docPositionStr.replace("技师", "技师(检验师)");
                    } else if (docPositionStr.equals("检验师")) {
                        docPositionStr = docPositionStr.replace("检验师", "技师(检验师)");
                    } else if (docPositionStr.equals("技士")) {
                        docPositionStr = docPositionStr.replace("技士", "技士(检验士)");
                    } else if (docPositionStr.equals("检验士")) {
                        docPositionStr = docPositionStr.replace("检验士", "技士(检验士)");
                    } else if (docPositionStr.equals("住院医师")) {
                        docPositionStr = docPositionStr.replace("住院医师", "住院医师/医师");
                    } else if (docPositionStr.equals("医师")) {
                        docPositionStr = docPositionStr.replace("医师", "住院医师/医师");
                    }

                    Integer docPosition = docPosition2DocPositionNumMap.get(docPositionStr);
                    hmsCMansData.setDocPosition(docPosition == null ? "" : String.valueOf(docPosition));
                }

                //院内职称处理
                String hosPositionStr = (String) localRecordMap.get("hos_position");
                String hosPositionId = "";
                if(StringUtils.isNotBlank(hosPositionStr)) {
                    if (hosPositionStr.contains(",")) {
                        String[] hosPositions = hosPositionStr.split(",");
                        for (String hosPositionStr1 : hosPositions) {
                            hosPositionId += hosPosition2HosPositionNumMap.get(hosPositionStr1) + ",";
                        }
                        hosPositionId = hosPositionId.substring(0, hosPositionId.length() - 1);
                    } else {
                        hosPositionId += hosPosition2HosPositionNumMap.get(hosPositionStr);
                    }
                    if (hosPositionId.isBlank()) {
                        throw new RuntimeException("请检查是否有当前院内职称栏目: " + localRecordMap.get("hos_position"));
                    }
                }
                hmsCMansData.setHosPosition(hosPositionId);

                //教务职称处理
                String eduPositionStr = (String) localRecordMap.get("edu_position");
                String eduPositionId = "";
                if(StringUtils.isNotBlank(eduPositionStr)) {
                    if (eduPositionStr.contains(",")) {
                        String[] eduPositions = eduPositionStr.split(",");
                        for (String eduPositionStr1 : eduPositions) {
                            eduPositionId += eduPosition2EduPositionNumMap.get(eduPositionStr1) + ",";
                        }
                        eduPositionId = eduPositionId.substring(0, eduPositionId.length() - 1);
                    } else {
                        eduPositionId += eduPosition2EduPositionNumMap.get(eduPositionStr);
                    }
                    if (eduPositionId.isBlank()) {
                        throw new RuntimeException("请检查是否有当前教务职称栏目: " + localRecordMap.get("edu_position"));
                    }
                }
                hmsCMansData.setEduPosition(eduPositionId);

                //教学岗位处理
                String eduPostStr = (String) localRecordMap.get("edu_post");
                String eduPostId = "";
                if(StringUtils.isNotBlank(eduPostStr)) {
                    if (eduPostStr.contains(",")) {
                        String[] eduPosts = eduPostStr.split(",");
                        for (String eduPostStr1 : eduPosts) {
                            eduPostId += eduPost2EduPostNumMap.get(eduPostStr1) + ",";
                        }
                        eduPostId = eduPostId.substring(0, eduPostId.length() - 1);
                    } else {
                        eduPostId += eduPost2EduPostNumMap.get(eduPostStr);
                    }
                    if (eduPostId.isBlank()) {
                        throw new RuntimeException("请检查是否有当前教学岗位栏目: " + localRecordMap.get("edu_post"));
                    }
                }
                hmsCMansData.setEduPost(eduPostId);

                //专家级别处理
                String levelStr = (String) localRecordMap.get("level");
                String levelId = "";
                if(StringUtils.isNotBlank(levelStr)) {
                    if (levelStr.contains(",")) {
                        String[] levels = levelStr.split(",");
                        for (String levelStr1 : levels) {
                            levelId += level2EduPostNumMap.get(levelStr1) + ",";
                        }
                        levelId = levelId.substring(0, levelId.length() - 1);
                    } else {
                        levelId += level2EduPostNumMap.get(levelStr);
                    }
                    if (levelId.isBlank()) {
                        throw new RuntimeException("请检查是否有当前专家级别栏目: " + localRecordMap.get("level"));
                    }
                }
                hmsCMansData.setLevel(levelId);

                // 擅长处理
                String goodAt = (String) localRecordMap.get("goodat");
                if (StringUtils.isBlank(goodAt)) {
                    goodAt = "";
                } else {
                    goodAt = StringUtils.trim(goodAt);
                }
                hmsCMansData.setGoodat(goodAt);

                // 简介处理
                String brief = (String) localRecordMap.get("content");
                if (StringUtils.isBlank(brief)) {
                    brief = "";
                } else {
                    brief = StringUtils.trim(brief);
                }
                hmsCMansData.setContent(brief);

                // 浏览量处理
                String views = (String) localRecordMap.get("views");
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                hmsCMansData.setViews(Integer.parseInt(views));

                // 来源处理
                String comeFrom = (String) localRecordMap.get("come_from");
                if (StringUtils.isBlank(comeFrom)) {
                    comeFrom = "";
                } else {
                    comeFrom = StringUtils.trim(comeFrom);
                }
                hmsCMansData.setComefrom(comeFrom);

                // 图片路径集合处理
                try {
                    List<String> imgUrls = new ArrayList<>();
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            imgUrls.add(imgUrl);
                        }
                    }
                    hmsCMansData.setContentImage(JsonUtil.obj2String(imgUrls));
                } catch (Exception e) {
                    hmsCMansData.setContentImage("[]");
                }

                // 链接路径集合处理
                try {
                    List<String> attachUrls = new ArrayList<>();
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNotBlank(attachUrl)) {
                            attachUrls.add(attachUrl);
                        }
                    }
                    hmsCMansData.setContentLink(JsonUtil.obj2String(attachUrls));
                } catch (Exception e) {
                    hmsCMansData.setContentLink("[]");
                }

                content = ElementUtil.getContentRemoveHtml(document);
                // TODO 这里图片处理完了，可以替换掉正文中的文件路径了
                if (StringUtils.isNotBlank(content)) {
                    content = content.replace(localPathPrefix, cloudPathPrefix);
                    content = content.replace("\\", "/");
                    localRecordMap.put("content", content);
                }


                hmsCMansData.setPoliticalVisage(null);
                hmsCMansData.setHonor(null);
                hmsCMansData.setInformation("[]");

                // 补全hms_mans的字段
                try {
                    Document document1 = new Html(hmsCMansData.getContent()).getDocument();
                    hmsCMans.setContentNum(document1.text().length());
                } catch (Exception e) {
                    hmsCMans.setContentNum(0);
                }

                finalMap.put("hmsCMans", hmsCMans);
                finalMap.put("hmsCMansData", hmsCMansData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCMans hmsCMans = (HmsCMans) finalMap.get("hmsCMans");
                    HmsCMansData hmsCMansData = (HmsCMansData) finalMap.get("hmsCMansData");

                    // 落库 -> hms_c_mans
                    ApplicationContextProvider.getBeanByType(HmsCMansMapper.class).insert(hmsCMans);
                    hmsCMans.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCMans.getId()) + ".html");
                    hmsCMans.setListorder(hmsCMans.getId());
                    hmsCMans.setDataid(hmsCMans.getId());

                    // 更新 -> hms_c_mans
                    ApplicationContextProvider.getBeanByType(HmsCMansMapper.class).updateById(hmsCMans);
                    hmsCMansData.setDid(hmsCMans.getId());

                    // 落库 -> hms_c_mans_data
                    ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class).insert(hmsCMansData);
                }
                return null;
            });
        } finally {
            RedisGetManager.deleteRedisById(collectTask.getId());
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "2";
    }
}