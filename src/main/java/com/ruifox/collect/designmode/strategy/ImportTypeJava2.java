package com.ruifox.collect.designmode.strategy;

import com.ruifox.collect.module.entity.CollectTask;

import java.util.List;
import java.util.Map;

public interface ImportTypeJava2 {
    void singleImport(CollectTask collectTask) throws Exception;

    default List<Map<String, Object>> singleImport(List<Map<Object, Object>> localRecords, CollectTask collectTask) {
        return null;
    }

    default List<String> getTableName() {
        return null;
    }

    /**
     * 服务于多导入类型下的策略模式(新闻/医生/单页/图集...)
     */
    String getBeanFlag();
}
