package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruifox.collect.dao.mapper.*;
import com.ruifox.collect.dao.mapper.hms.HmsCNewspaperDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewspaperMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.hms.HmsSiteMapper;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.*;
import com.ruifox.collect.module.entity.hms.HmsCNewspaper;
import com.ruifox.collect.module.entity.hms.HmsCNewspaperData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.hms.HmsSite;
import com.ruifox.collect.util.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * Description: ImportTypeImage
 *
 * <AUTHOR>
 * @date 2024/6/5 下午2:48
 * @fileName ImportTypeImage.java
 **/
@Component
@Slf4j
public class ImportTypeNewspaper implements ImportType {
    /**
     * 单任务导入
     * @param collectTask     采集任务实体
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void singleImport(CollectTask collectTask) {
        try {
            // 院报页面导入
            log.info("当前正在导入院报页面数据...");
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());
            
            // 切换本地数据源
            DynamicDataSource.changeDefaultDataSource();
            
            // 构造本地路径替换前缀
            String localPathPrefix = "C:\\" + collectTask.getHost() + "\\";  // 示例路径：C:\www.cdlyy.com\
            
            // 当前任务对应数据表名
            String dataTableName = collectTask.getDataTableName();
            
            // 查询对应数据表名中的所有数据
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(dataTableName);
            
            // 当前任务对应文件表名
            String fileTableName = collectTask.getFileTableName();
            
            // 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请设置当前导入的栏目ID!");
                return;
            }
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain();
            
            // 切换本地数据源
            DynamicDataSource.changeDefaultDataSource();
            
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            
            long i = 1;
            for (Map<Object, Object> localRecordMap : localRecords) {
                System.out.println("当前正在处理第 " + i++ + " 条数据...");
                
                // 新增特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                }else {
                    specialHanding.newspaperSpecialHanding(localRecordMap, null);
                }
                
                HashMap<String, Object> finalMap = new HashMap<>();
                
                // 获取访问量
                String views = localRecordMap.get("views").toString();
                if(StringUtils.isBlank(views)) {
                    views = "0";
                }
                
                // 来源处理
                String comeFrom = (String) localRecordMap.get("come_from");
                if (comeFrom == null || comeFrom.isEmpty()) {
                    comeFrom = "";
                } else {
                    if (comeFrom.equals("管理员")) {
                        comeFrom = "";
                    }
                }
                
                // 发布年限处理
                String publishTime = (String) localRecordMap.get("publish_time");
                Date data = new Date(publishTime);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String formattedDate = sdf.format(data);
                
                // 从正文中提取图片集 -> newspaper
                String content = (String) localRecordMap.get("content");
                
                List<Map<String, Object>> imgList = new ArrayList<>();
                if (content != null && !content.isEmpty()) {
                    Html contentHtml = new Html(content);
                    // 从内容源码中获取图片url以及图片顺序...
                    Document contentHtmlDocument = contentHtml.getDocument();
                    Elements imgs = contentHtmlDocument.select("img");
                    for (int i1 = 0; i1 < imgs.size(); i1++) {
                        String url = imgs.get(i1).attr("src");
                        if (url == null || url.isEmpty()) {
                            continue;
                        }
                        boolean check = FileUtil.localUrlChick(url);
                        if (!check) { // 说明图片不是我们想要的
                            continue;
                        }
                        
                        url = url.replace(localPathPrefix, cloudPathPrefix);
                        url = url.replace("\\", "/");
                        
                        if (!url.startsWith(cloudPathPrefix)) { // 说明图片不是我们想要的
                            continue;
                        }
                        
                        Map<String, Object> map = new HashMap<>();
                        ArrayList<String> arrayList = new ArrayList<>();
                        map.put("title", String.valueOf(i1));
                        map.put("url", url);
                        map.put("article", arrayList);
                        imgList.add(map);
                    }
                    
                }

                // 获取标题
                String title = (String) localRecordMap.get("title");

                // 获取总期数-----可能有变化
                String publishNums = "总" + title.substring(title.indexOf("第"), title.indexOf("期") + 1);

                // 获取期数-----可能有变化
                String publishIndex =  title.substring(title.indexOf("第") + 1, title.indexOf("期"));

                HmsCNewspaperData hmsCNewspaperData = HmsCNewspaperData
                        .builder()
                        .newspaper(JsonUtil.obj2String(imgList))
                        .title((String) localRecordMap.get("title"))
                        .publishNums(publishNums)
                        .publishIndex(publishIndex)
                        .publishYear(formattedDate)
                        .articleList(null)
                        .views(Integer.parseInt(views))
                        .comefrom(comeFrom)
                        .build();
                
                HmsCNewspaper hmsCNewspaper = HmsCNewspaper
                        .builder()
                        .catid(Integer.valueOf(String.valueOf(hmsCategory.getId())))
                        .username(1)
                        .endOperator(1)
                        .isLocked(0)
                        .url("")
                        .islink((int) localRecordMap.get("flag") == 1 ? null : (String) localRecordMap.get("target_url"))
                        .top(0)
                        .listorder(i++)
                        .state(0)
                        .status(99)
                        .publishTime(Integer.parseInt(publishTime))
                        .expireTime(0)
                        .inputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)))
                        .updateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)))
                        .build();
                
                // TODO: ZhangXinYu 2024/6/17 |
                finalMap.put("hmsCNewspaperData", hmsCNewspaperData);
                finalMap.put("hmsCNewspaper", hmsCNewspaper);
                finalMaps.add(finalMap);
            }
            
            // TODO: ZhangXinYu 2024/6/17 | 落库
            log.info("数据解析完成，开始导入...");
            
            // TODO: ZhangXinYu 2024/6/17 | 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            
            TransactionUtil.executeInTransaction(() -> {
                
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCNewspaperData hmsCNewspaperData = (HmsCNewspaperData) finalMap.get("hmsCNewspaperData");
                    HmsCNewspaper hmsCNewspaper = (HmsCNewspaper) finalMap.get("hmsCNewspaper");
                    
                    // 落库 -> hms_c_image
                    ApplicationContextProvider.getBeanByType(HmsCNewspaperMapper.class).insert(hmsCNewspaper);
                    hmsCNewspaper.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCNewspaper.getId()) + ".html");
                    hmsCNewspaper.setListorder(hmsCNewspaper.getId());
                    hmsCNewspaper.setDataid(hmsCNewspaper.getId());
                    
                    // 更新url
                    ApplicationContextProvider.getBeanByType(HmsCNewspaperMapper.class).updateById(hmsCNewspaper);
                    hmsCNewspaperData.setDid(hmsCNewspaper.getId());
                    
                    // 落库 -> hms_c_image_data
                    ApplicationContextProvider.getBeanByType(HmsCNewspaperDataMapper.class).insert(hmsCNewspaperData);
                }
                
                return null;
            });
            
            log.info("当前导入院报页面数据完成...");
            
        } finally {
            DynamicDataSource.changeDefaultDataSource();
        }
    }
    
    /**
     * 服务于多导入类型下的策略模式(新闻/医生/单页/图集...)
     *
     * @return 任务类型ID
     */
    @Override
    public String getBeanFlag() {
        return "8";
    }
}
