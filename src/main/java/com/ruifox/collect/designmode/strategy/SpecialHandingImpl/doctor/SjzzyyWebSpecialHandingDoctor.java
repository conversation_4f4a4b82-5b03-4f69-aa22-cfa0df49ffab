package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.doctor;

import com.ruifox.collect.common.constants.DoctorDataConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.ElementUtil;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.Map;

@Component
public class SjzzyyWebSpecialHandingDoctor implements WebSpecialHanding {
    @Override
    public String getBeanFlag() {
        return "www.sjzzyy.com-2";
    }

    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {

    }

    @Override
    public void specialHandingForNotEmbedded(Map<String, String> independentDataMap, CollectTask collectTask, Request request) {
        //姓名
        String title = independentDataMap.get(DoctorDataConstant.TITLE);
        title = title.trim();
        if (!title.isEmpty()) {
            independentDataMap.put(DoctorDataConstant.TITLE, title);
        } else {
            independentDataMap.put(DoctorDataConstant.TITLE, "");
        }

        //头像
        String thumb = independentDataMap.get(DoctorDataConstant.THUMB);
        if (StringUtils.isNotBlank(thumb)) {
            thumb = FileDataManager.downloadFromHttpUrl(thumb, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);
            independentDataMap.put(DoctorDataConstant.THUMB, thumb);
        } else {
            independentDataMap.put(DoctorDataConstant.THUMB, "");
        }

        // 科室
        String depart = independentDataMap.get(DoctorDataConstant.DEPART);
        if (StringUtils.isNotBlank(depart)) {
            depart = depart.substring(depart.indexOf(" 科室名称") + " 科室名称".length());
            depart = depart.trim();
            independentDataMap.put(DoctorDataConstant.DEPART, depart);
        }else{
            independentDataMap.put(DoctorDataConstant.DEPART, "");
        }

        //教学职称
        String edu_position = independentDataMap.get(DoctorDataConstant.EDU_POSITION);
        if (StringUtils.isNotBlank(edu_position)) {
            edu_position = edu_position.substring(edu_position.indexOf(" 教学职称") + " 教学职称".length());
            edu_position = edu_position.trim();
            independentDataMap.put(DoctorDataConstant.EDU_POSITION, edu_position);
        } else {
            independentDataMap.put(DoctorDataConstant.EDU_POSITION, "");
        }

        //医师职称
        String doc_position = independentDataMap.get(DoctorDataConstant.DOC_POSITION);
        if (StringUtils.isNotBlank(doc_position)) {
            doc_position = doc_position.substring(doc_position.indexOf(" 临床职称") + " 临床职称".length());
            doc_position = doc_position.trim();
            independentDataMap.put(DoctorDataConstant.DOC_POSITION, doc_position);
        } else {
            independentDataMap.put(DoctorDataConstant.DOC_POSITION, "");
        }

        // 擅长
        String goodat = independentDataMap.get(DoctorDataConstant.GOODAT);
        if(StringUtils.isNotBlank(goodat)) {
            goodat = StringUtils.trim(goodat);
            independentDataMap.put(DoctorDataConstant.GOODAT, goodat);
        } else {
            independentDataMap.put(DoctorDataConstant.GOODAT, "");
        }


        // 正文
        String content = independentDataMap.get(DoctorDataConstant.CONTENT);
        if (StringUtils.isNotBlank(content)) {
            Document contextDom = Jsoup.parse(content);
            // 1.删除不需要的元素（写了一个常用的通用方法
            ElementUtil.deleteNoContent(contextDom);

            //删除正文中a标签，且该标签所对应的是原网站的html
            ElementUtil.deleteHtmlA(contextDom,collectTask);

            //格式化
            ElementUtil.modifyFormat(contextDom);

            // 2.更新正文（写了一个常用的通用方法
            content = ElementUtil.getContentRemoveHtml(contextDom);

            independentDataMap.put(DoctorDataConstant.CONTENT, content);
        } else {
            independentDataMap.put(DoctorDataConstant.CONTENT, "");
        }
    }

    @Override
    public boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask) {
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || !url.contains(collectTask.getHost())
                || url.contains("/oss/")) {
            return false;
        }
        return true;
    }
}
