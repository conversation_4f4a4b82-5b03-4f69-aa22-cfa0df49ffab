package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.volunteers;

import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class ChengduredcrossWebSpecialHandingVolunteers implements WebSpecialHanding {
    @Override
    public String getBeanFlag() {
        return "www.chengduredcross.cn-14";
    }

    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {
        // 头像
        String thumb = listItemMap.get(NewsDataConstant.THUMB);
        if (StringUtils.isNotBlank(thumb)) {
            thumb = thumb.replace("../", "");
            thumb = FileDataManager.downloadFromHttpUrl(thumb, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);
            listItemMap.put(NewsDataConstant.THUMB, thumb);
        } else {
            listItemMap.put(NewsDataConstant.THUMB, "");
        }
    }

    @Override
    public void specialHandingForNotEmbedded(Map<String, String> independentDataMap, CollectTask collectTask, Request request) {
        // 姓名
        String title = independentDataMap.get("title");
        if (StringUtils.isNotBlank(title)) {
            title = title.trim();
            independentDataMap.put("title", title);
        } else {
            independentDataMap.put("title", "");
        }

        // 性别
        String sex = independentDataMap.get("sex");
        if (StringUtils.isNotBlank(sex)) {
            sex = sex.trim();
            independentDataMap.put("sex", sex);
        } else {
            independentDataMap.put("sex", "");
        }

        // 籍贯
        String registeredBirthplace = independentDataMap.get("registered_birthplace");
        if (StringUtils.isNotBlank(registeredBirthplace)) {
            registeredBirthplace = registeredBirthplace.trim();
            independentDataMap.put("registered_birthplace", registeredBirthplace);
        } else {
            independentDataMap.put("registered_birthplace", "");
        }

        // 民族
        String ethnicGroup = independentDataMap.get("ethnic_group");
        if (StringUtils.isNotBlank(ethnicGroup)) {
            ethnicGroup = ethnicGroup.trim();
            independentDataMap.put("ethnic_group", ethnicGroup);
        } else {
            independentDataMap.put("ethnic_group", "");
        }

        // 职业
        String career = independentDataMap.get("career");
        if (StringUtils.isNotBlank(career)) {
            career = career.trim();
            independentDataMap.put("career", career);
        } else {
            independentDataMap.put("career", "");
        }

        // 地址
        String address = independentDataMap.get("address");
        if (StringUtils.isNotBlank(address)) {
            address = address.trim();
            independentDataMap.put("address", address);
        } else {
            independentDataMap.put("address", "");
        }

        // 生日
        String birthday = independentDataMap.get("birthday");
        if (StringUtils.isNotBlank(birthday)) {
            birthday = birthday.trim();
            try {
                Date date = DateUtils.parseDate(birthday, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                independentDataMap.put("birthday", timestampInSeconds);
            } catch (Exception e) {
                independentDataMap.put("birthday", birthday);
            }
        } else {
            independentDataMap.put("birthday", "");
        }

        // 忌日
        String tabooDay = independentDataMap.get("taboo_day");
        if (StringUtils.isNotBlank(tabooDay)) {
            tabooDay = tabooDay.trim();
            try {
                Date date = DateUtils.parseDate(tabooDay, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                independentDataMap.put("taboo_day", timestampInSeconds);
            } catch (Exception e) {
                independentDataMap.put("taboo_day", tabooDay);
            }
        } else {
            independentDataMap.put("taboo_day", "");
        }

        // 捐献登记日
        String registrationDay = independentDataMap.get("registration_day");
        if (StringUtils.isNotBlank(registrationDay)) {
            registrationDay = registrationDay.trim();
            try {
                Date date = DateUtils.parseDate(registrationDay, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                independentDataMap.put("registration_day", timestampInSeconds);
            } catch (Exception e) {
                independentDataMap.put("registration_day", registrationDay);
            }
        } else {
            independentDataMap.put("registration_day", "");
        }

        // 捐献实现日
        String realizationDay = independentDataMap.get("realization_day");
        if (StringUtils.isNotBlank(realizationDay)) {
            realizationDay = realizationDay.trim();
            try {
                Date date = DateUtils.parseDate(realizationDay, "yyyy-MM-dd");
                String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                independentDataMap.put("realization_day", timestampInSeconds);
            } catch (Exception e) {
                independentDataMap.put("realization_day", realizationDay);
            }
        } else {
            independentDataMap.put("realization_day", "");
        }

        // 生平简介
        String biographicSketch = independentDataMap.get("biographic_sketch");
        if (StringUtils.isNotBlank(biographicSketch)) {
            independentDataMap.put("biographic_sketch", biographicSketch);
        } else {
            independentDataMap.put("biographic_sketch", "");
        }

        // 献花数
        String flowers = independentDataMap.get("flowers");
        if (StringUtils.isNotBlank(flowers)) {
            flowers = flowers.trim();
            flowers = flowers.substring(1, flowers.indexOf(")"));
            independentDataMap.put("flowers", flowers);
        } else {
            independentDataMap.put("flowers", "");
        }

        // 献歌数
        String song = independentDataMap.get("song");
        if (StringUtils.isNotBlank(song)) {
            song = song.trim();
            song = song.substring(1, song.indexOf(")"));
            independentDataMap.put("song", song);
        } else {
            independentDataMap.put("song", "");
        }

        // 点烛数
        String candle = independentDataMap.get("candle");
        if (StringUtils.isNotBlank(candle)) {
            candle = candle.trim();
            candle = candle.substring(1, candle.indexOf(")"));
            independentDataMap.put("candle", candle);
        } else {
            independentDataMap.put("candle", "");
        }

        // 祭酒数
        String wine = independentDataMap.get("wine");
        if (StringUtils.isNotBlank(wine)) {
            wine = wine.trim();
            wine = wine.substring(1, wine.indexOf(")"));
            independentDataMap.put("wine", wine);
        } else {
            independentDataMap.put("wine", "");
        }
    }

    @Override
    public boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask) {
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || url.contains("/oss/")
                || url.contains("mailto") || url.contains("jsp")
                || !url.contains(collectTask.getHost())
                || url.contains("javascript")) {
            return false;
        }
        return true;
    }
}
