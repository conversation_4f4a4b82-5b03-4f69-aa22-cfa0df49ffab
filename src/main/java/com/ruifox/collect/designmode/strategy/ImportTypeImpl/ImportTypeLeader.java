package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.ElementUtil;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ImportTypeLeader implements ImportType {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());
            // TODO 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();
            // 构造本地路径替换前缀
            String localPathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/"; // 示例路径：C:/www.cdlyy.com/
            // 基本数据表名
            String dataTableName = collectTask.getDataTableName();
            // 查询本地所有数据
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(dataTableName);

            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.error("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            }
            log.info("当前导入栏目ID:{},当前导入栏目名称:{}", hmsCategory.getId(), hmsCategory.getName());
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain()+"oss/";

            // 获取党内职务数据集合
            Map<Object, Object> record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithPartyPosition(hmsCategory.getModelId());
            String setting = (String) record.get("setting");
            Map<Object, Object> map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> partyPositionNum2PartyPositionMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> partyPosition2PartyPositionNumMap = new HashMap<>();
            partyPositionNum2PartyPositionMaps.forEach(map1 -> partyPosition2PartyPositionNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            // 获取领导职务数据集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithPartyPosition(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> leaderPositionNum2LeaderPositionMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> leaderPosition2LeaderPositionNumMap = new HashMap<>();
            leaderPositionNum2LeaderPositionMaps.forEach(map1 -> leaderPosition2LeaderPositionNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            // 得到医生职称数据集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithDocPosition(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> docPositionNum2docPositionMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> docPosition2DocPositionNumMap = new HashMap<>();
            docPositionNum2docPositionMaps.forEach(map1 -> docPosition2DocPositionNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            // 得到教务职称数据集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithEduPosition(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> eduPositionNum2eduPositionMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> eduPosition2EduPositionNumMap = new HashMap<>();
            eduPositionNum2eduPositionMaps.forEach(map1 -> eduPosition2EduPositionNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            // 得到教学岗位数据集合
            record = ApplicationContextProvider.getBeanByType(HmsModelFieldMapper.class)
                    .selectByModelIdWithEduPost(hmsCategory.getModelId());
            setting = (String) record.get("setting");
            map = JsonUtil.Json2Obj(setting, Map.class);
            List<Map<String, String>> eduPostNum2eduPostMaps = (List<Map<String, String>>) map.get("options");
            Map<String, Integer> eduPost2EduPostNumMap = new HashMap<>();
            eduPostNum2eduPostMaps.forEach(map1 -> eduPost2EduPostNumMap.put(map1.get("value"), Integer.valueOf(map1.get("key"))));

            // TODO 切回本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();

            // 处理本地数据至hms_c_leader和hms_c_leader_data
            long i = 1;
            String sortTmp = "0";
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if(sort.equals(sortTmp)){
                    continue;
                }
                sortTmp = sort;

                System.out.println("当前正在处理第 " + (i++) + " 条数据...");
                HashMap<String, Object> finalMap = new HashMap<>();

                // 导入时的特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                } else {
                    specialHanding.newsSpecialHanding(localRecordMap, collectTask);
                }

                // TODO 处理至hms_c_leader
                HmsCLeader hmsCLeader = new HmsCLeader();
                hmsCLeader.setDataid(1L);
                hmsCLeader.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCLeader.setUsername(1);
                hmsCLeader.setEndOperator(1);
                hmsCLeader.setUrl("");
                hmsCLeader.setIslink((int) localRecordMap.get("flag") == 0 ? null : (String) localRecordMap.get("target_url"));
                hmsCLeader.setTop(0);
                hmsCLeader.setListorder(-99L);
                hmsCLeader.setSort(1);
                hmsCLeader.setState(0);
                hmsCLeader.setStatus(99);

                String publishTime = String.valueOf(localRecordMap.get("publish_time"));
                if (StringUtils.isBlank(publishTime)) {
                    publishTime = "0";
                }
                hmsCLeader.setPublishTime(Integer.parseInt(publishTime));
                hmsCLeader.setExpireTime(0);
                hmsCLeader.setInputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCLeader.setUpdateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCLeader.setIsLocked(0);

                finalMap.put("hmsCLeader", hmsCLeader);


                // TODO 处理至hms_c_leader_data
                HmsCLeaderData hmsCLeaderData = new HmsCLeaderData();
                hmsCLeaderData.setDid(hmsCLeader.getDataid());

                hmsCLeaderData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));

                String comefrom = String.valueOf(localRecordMap.get("comefrom"));
                if(StringUtils.isBlank(comefrom))
                    comefrom = "";
                hmsCLeaderData.setComefrom(comefrom);

                String thumb = String.valueOf(localRecordMap.get("thumb"));
                if(StringUtils.isNotBlank(thumb)){
                    thumb = thumb.trim();
                    thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                }else {
                    thumb = "";
                }
                hmsCLeaderData.setThumb(thumb);

                String content = String.valueOf(localRecordMap.get("content"));
                if(StringUtils.isBlank(content))
                    content = "";
                hmsCLeaderData.setContent(content);

                // 任职时间处理
                String tenure = String.valueOf(localRecordMap.get("tenure"));
                if(StringUtils.isBlank(tenure))
                    tenure = "";
                hmsCLeaderData.setTenure(tenure);

                // 相关职务处理
                String relatedPosition = String.valueOf(localRecordMap.get("related_position"));
                if(StringUtils.isBlank(relatedPosition))
                    relatedPosition = "";
                hmsCLeaderData.setRelatedPosition(relatedPosition);

                // 党内职务处理
                String partyPosition = String.valueOf(localRecordMap.get("party_position"));
                List<String> partyPositionList = new ArrayList<>();
                if(StringUtils.isNotBlank(partyPosition)){
                    if(partyPosition.contains(",")){
                        String[] partPositions = partyPosition.split(",");
                        for (String partPositionStr : partPositions) {
                            if(partyPosition2PartyPositionNumMap.containsKey(partPositionStr)){
                                partyPositionList.add(partyPosition2PartyPositionNumMap.get(partPositionStr).toString());
                            }
                        }
                    }
                }
                hmsCLeaderData.setPartyPosition(JsonUtil.obj2String(partyPositionList));

                // 领导职务处理
                String leaderPosition = String.valueOf(localRecordMap.get("leader_position"));
                List<String> leaderPositionList = new ArrayList<>();
                if(StringUtils.isNotBlank(leaderPosition)){
                    if(leaderPosition.contains(",")){
                        String[] leaderPositions = leaderPosition.split(",");
                        for (String leaderPositionStr : leaderPositions) {
                            if(leaderPosition2LeaderPositionNumMap.containsKey(leaderPositionStr)){
                                leaderPositionList.add(leaderPosition2LeaderPositionNumMap.get(leaderPositionStr).toString());
                            }
                        }
                    }
                }
                hmsCLeaderData.setLeaderPosition(JsonUtil.obj2String(leaderPositionList));

                // 医师职称处理
                String docPosition = String.valueOf(localRecordMap.get("doc_position"));
                List<String> docPositionList = new ArrayList<>();
                if(StringUtils.isNotBlank(docPosition)){
                    if(docPosition.contains(",")){
                        String[] docPositions = docPosition.split(",");
                        for (String docPositionStr : docPositions) {
                            if(docPosition2DocPositionNumMap.containsKey(docPositionStr)){
                                docPositionList.add(docPosition2DocPositionNumMap.get(docPositionStr).toString());
                            }
                        }
                    }
                }
                hmsCLeaderData.setDocPosition(JsonUtil.obj2String(docPositionList));

                // 教务职务处理
                String eduPosition = String.valueOf(localRecordMap.get("edu_position"));
                List<String> eduPositionList = new ArrayList<>();
                if(StringUtils.isNotBlank(eduPosition)){
                    if(eduPosition.contains(",")){
                        String[] eduPositions = eduPosition.split(",");
                        for (String eduPositionStr : eduPositions) {
                            if(eduPosition2EduPositionNumMap.containsKey(eduPositionStr)){
                                eduPositionList.add(eduPosition2EduPositionNumMap.get(eduPositionStr).toString());
                            }
                        }
                    }
                }
                hmsCLeaderData.setEduPosition(JsonUtil.obj2String(eduPositionList));

                // 教学岗位处理
                String eduPost = String.valueOf(localRecordMap.get("edu_post"));
                List<String> eduPostList = new ArrayList<>();
                if(StringUtils.isNotBlank(eduPost)){
                    if(eduPost.contains(",")){
                        String[] eduPosts = eduPost.split(",");
                        for (String eduPostStr : eduPosts) {
                            if(eduPost2EduPostNumMap.containsKey(eduPostStr)){
                                eduPostList.add(eduPost2EduPostNumMap.get(eduPostStr).toString());
                            }
                        }
                    }
                }
                hmsCLeaderData.setEduPost(JsonUtil.obj2String(eduPostList));

                finalMap.put("hmsCLeaderData", hmsCLeaderData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCLeader hmsCLeader = (HmsCLeader) finalMap.get("hmsCLeader");
                    HmsCLeaderData hmsCLeaderData = (HmsCLeaderData) finalMap.get("hmsCLeaderData");

                    // 落库 -> hms_c_leader
                    ApplicationContextProvider.getBeanByType(HmsCLeaderMapper.class).insert(hmsCLeader);
                    hmsCLeader.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCLeader.getId()) + ".html");
                    hmsCLeader.setListorder(hmsCLeader.getId());
                    hmsCLeader.setDataid(hmsCLeader.getId());

                    // 更新 -> hms_c_leader
                    ApplicationContextProvider.getBeanByType(HmsCLeaderMapper.class).updateById(hmsCLeader);
                    hmsCLeaderData.setDid(hmsCLeader.getId());

                    // 落库 -> hms_c_leader_data
                    ApplicationContextProvider.getBeanByType(HmsCLeaderDataMapper.class).insert(hmsCLeaderData);
                }
                return null;
            });
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "5";
    }
}
