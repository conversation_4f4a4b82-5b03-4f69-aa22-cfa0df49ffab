package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.print.DocFlavor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ImportTypeDownload implements ImportType {
    @Override
    public void singleImport(CollectTask collectTask) {
        try {
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());

            // TODO 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();
            // 构造本地路径替换前缀
            String localPathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/"; // 示例路径：C:/www.cdlyy.com/
            // 基本数据表名
            String dataTableName = collectTask.getDataTableName();
            // 查询本地所有数据
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(dataTableName);

            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.error("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            }
            log.info("当前导入栏目ID:{},当前导入栏目名称:{}", hmsCategory.getId(), hmsCategory.getName());
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain()+"oss/";

            // TODO 切回本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();

            // 处理本地数据至hms_c_news和hms_c_news_data
            long i = 1;
            String sortTmp = "0";
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if(sort.equals(sortTmp)){
                    continue;
                }
                sortTmp = sort;

                System.out.println("当前正在处理第 " + (i++) + " 条数据...");
                HashMap<String, Object> finalMap = new HashMap<>();

                // 导入时的特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                } else {
                    specialHanding.newsSpecialHanding(localRecordMap, collectTask);
                }

                // TODO 处理至hms_c_download\
                HmsCDownload hmsCDownload = new HmsCDownload();
                hmsCDownload.setDataid(1L);
                hmsCDownload.setOldCatid(null);
                hmsCDownload.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCDownload.setUsername(1);
                hmsCDownload.setEndOperator(1);
                hmsCDownload.setUrl("");
                hmsCDownload.setIslink((int) localRecordMap.get("flag") == 0 ? null : (String) localRecordMap.get("target_url"));
                hmsCDownload.setTop(0);
                hmsCDownload.setListorder(-99L);
                hmsCDownload.setSort(1);
                hmsCDownload.setState(0);
                hmsCDownload.setStatus(99);

                // FIXME
                String publishTime = String.valueOf(localRecordMap.get("publish_time"));
                if (StringUtils.isBlank(publishTime)) {
                    publishTime = "0";
                }
                hmsCDownload.setPublishTime(Integer.parseInt(publishTime));
                hmsCDownload.setExpireTime(0);
                hmsCDownload.setInputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCDownload.setUpdateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));

                String views = String.valueOf(localRecordMap.get("views"));
                if(StringUtils.isBlank(views)){
                    views = "0";
                }
                hmsCDownload.setViews(Integer.parseInt(views));
                hmsCDownload.setIsLocked(0);

                finalMap.put("hmsCDownload", hmsCDownload);


                // TODO 处理至hms_c_download_data
                HmsCDownloadData hmsCDownloadData = new HmsCDownloadData();
                hmsCDownloadData.setDid(hmsCDownload.getDataid());

                hmsCDownloadData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));

                String comefrom = String.valueOf(localRecordMap.get("comefrom"));
                if(StringUtils.isBlank(comefrom))
                    comefrom = "";
                hmsCDownloadData.setComefrom(comefrom);

                //获取file
                Map<String, Object> file = new HashMap<>();
                file.put("id", String.valueOf(SnowFlakeUtil.nextId()));
                file.put("name", localRecordMap.get("title"));
                String url = localRecordMap.get("file").toString();
                if(StringUtils.isNotBlank(url)){
                    url = url.replace(localPathPrefix, cloudPathPrefix);
                    file.put("url", url);
                }
                hmsCDownloadData.setFile(JsonUtil.obj2String(file));


                finalMap.put("hmsCDownloadData", hmsCDownloadData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCDownload hmsCDownload = (HmsCDownload) finalMap.get("hmsCDownload");
                    HmsCDownloadData hmsCDownloadData = (HmsCDownloadData) finalMap.get("hmsCDownloadData");

                    // 落库 -> hms_c_tender
                    ApplicationContextProvider.getBeanByType(HmsCDownloadMapper.class).insert(hmsCDownload);
                    hmsCDownload.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCDownload.getId()) + ".html");
                    hmsCDownload.setListorder(hmsCDownload.getId());
                    hmsCDownload.setDataid(hmsCDownload.getId());

                    // 更新 -> hms_c_tender
                    ApplicationContextProvider.getBeanByType(HmsCDownloadMapper.class).updateById(hmsCDownload);
                    hmsCDownloadData.setDid(hmsCDownload.getId());

                    // 落库 -> hms_c_tender_data
                    ApplicationContextProvider.getBeanByType(HmsCDownloadDataMapper.class).insert(hmsCDownloadData);
                }
                return null;
            });
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "10";
    }
}
