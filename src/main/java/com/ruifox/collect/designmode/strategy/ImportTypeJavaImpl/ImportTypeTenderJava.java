package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl;

import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeTenderJava implements ImportTypeJava {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            DynamicDataSource.changeDynamicDataSource();
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }
            DynamicDataSource.changeDefaultDataSource();
            // 获取本地结果集,按sort将排在后面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            // 防止导入重复数据(连sort都相同的数据)
            String tempsort = "-1";
            int i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(tempsort)) {
                    continue;
                }
                tempsort = sort;
                log.info("正在导入第" + (++i) + "条数据");

                // 存储每一次的数据
                Map<String, Object> finalMap = new HashMap<>();

                StationMTender stationMTender = new StationMTender();
                stationMTender.setCatId(collectTask.getImportCategoryId());
                stationMTender.setPublishUserId(1);
                String publishTime = localRecordMap.get("publish_time").toString();
                stationMTender.setCreateTime(Double.parseDouble(publishTime + "000"));
                stationMTender.setUpdateTime(Double.parseDouble(publishTime + "000"));
                stationMTender.setState(99);
                stationMTender.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                }
                stationMTender.setViews(Integer.parseInt(views));
                finalMap.put("stationMTender", stationMTender);

                StationMTenderData stationMTenderData = new StationMTenderData();
                stationMTenderData.setUuid(UUID.randomUUID().toString());
                stationMTenderData.setPublishTime(Double.parseDouble(publishTime + "000"));
                stationMTenderData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));

                // 项目编号处理
                String subject = localRecordMap.get("subject").toString();
                if (StringUtils.isBlank(subject)) {
                    subject = "";
                }
                stationMTenderData.setSubject(subject);

                // 来源处理
                String comefrom = localRecordMap.get("comefrom").toString();
                if (StringUtils.isBlank(comefrom)) {
                    comefrom = "";
                }
                stationMTenderData.setComefrom(comefrom);

                // 开标时间处理
                String openTime = localRecordMap.get("open_time").toString();
                if (StringUtils.isNotBlank(openTime)) {
                    stationMTenderData.setOpenTime(Double.parseDouble(openTime + "000"));
                }

                // 关联项目处理
                String applyform = localRecordMap.get("applyform").toString();
                if (StringUtils.isNotBlank(applyform)) {
                    stationMTenderData.setApplyform(Integer.parseInt(applyform));
                }

                // 报名开始时间处理
                String signTime = localRecordMap.get("sign_time").toString();
                if (StringUtils.isNotBlank(signTime)) {
                    stationMTenderData.setSignTime(Double.parseDouble(signTime + "000"));
                }

                // 报名截止时间处理
                String endTime = localRecordMap.get("end_time").toString();
                if (StringUtils.isNotBlank(endTime)) {
                    stationMTenderData.setEndTime(Double.parseDouble(endTime + "000"));
                }

                // 摘要处理
                String description = localRecordMap.get("description").toString();
                if (StringUtils.isNotBlank(description)) {
                    stationMTenderData.setDescription(description);
                }

                // 正文处理
                String content = localRecordMap.get("content").toString();
                if (StringUtils.isNotBlank(content)) {
                    Document document = Jsoup.parse(content);

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        if (StringUtils.isNotBlank(text)) {
                            description = text.substring(0, Math.min(120, text.length())).trim() + "...";
                            stationMTenderData.setDescription(description);
                        }
                    }

                    // 正文中所有的图片集合
                    Elements imgElements = document.select("img");
                    // 正文中所有的视频集合
                    Elements videoElements = document.select("video");
                    // 正文中所有的链接集合
                    Elements aElements = document.select("a");

                    // 对这些文件进行下载
                    for (Element imgElement : imgElements) {
                        String src = imgElement.attr("src");
                        File imgFile = new File(src);
                        String url = CrawlerManager.changeFileUrl(imgFile);
                        imgElement.attr("src", url);
                    }

                    for (Element videoElement : videoElements) {
                        String src = videoElement.attr("src");
                        File videoFile = new File(src);
                        String url = CrawlerManager.changeFileUrl(videoFile);
                        videoElement.attr("src", url);
                    }

                    for (Element aElement : aElements) {
                        String href = aElement.attr("href");
                        File aFile = new File(href);
                        String url = CrawlerManager.changeFileUrl(aFile);
                        aElement.attr("href", url);
                    }

                    stationMTenderData.setContent(document.toString());
                } else {
                    stationMTenderData.setContent("");
                }
                finalMap.put("stationMTenderData", stationMTenderData);
            }
            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    StationMTender stationMTender = (StationMTender) finalMap.get("stationMTender");
                    StationMTenderData stationMTenderData = (StationMTenderData) finalMap.get("stationMTenderData");

                    // 落库 -> station_m_article
                    ApplicationContextProvider.getBeanByType(StationMTenderMapper.class).insert(stationMTender);
                    stationMTender.setSortLevel(Math.toIntExact(stationMTender.getId()));


                    // 落库 -> station_m_article_data
                    ApplicationContextProvider.getBeanByType(StationMTenderDataMapper.class).insert(stationMTenderData);
                    stationMTender.setDataId(stationMTenderData.getDataId());

                    // 更新 -> station_m_article
                    ApplicationContextProvider.getBeanByType(StationMTenderMapper.class).updateById(stationMTender);

                }
                return null;
            });
        } finally {
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "9";
    }
}
