package com.ruifox.collect.designmode.strategy.ImportTypeImpl.handing;

import com.ruifox.collect.designmode.strategy.ImportTypeImpl.ImportSpecialHanding;
import com.ruifox.collect.module.entity.CollectTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

@Component
@Slf4j
public class MySpecialHanding implements ImportSpecialHanding {
    @Override
    public String getHost() {
        return "www.hospital-cqmu.com-1";
    }

    @Override
    public void newsSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        //处理浏览数据
        String views = (String) localRecordMap.get("views");
        String newView = "";
        if(views!=null){
            newView = views.substring(3,views.length()-1);
        }
        localRecordMap.put("views", newView);
    }
}
