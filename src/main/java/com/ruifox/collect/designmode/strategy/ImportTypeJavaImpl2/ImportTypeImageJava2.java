package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl2;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station2.StationCategoryMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMImageDataMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMReferenceMapper;
import com.ruifox.collect.designmode.strategy.ImportTypeJava2;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station.StationMImageData;
import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.module.entity.station2.StationMImageData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeImageJava2 implements ImportTypeJava2 {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeBuildDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory2 stationCategory2 = ApplicationContextProvider.getBeanByType(StationCategoryMapper2.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory2 == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory2.getId(), stationCategory2.getName());
            }

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_article
                
                StationMReference stationMImage = new StationMReference();

                stationMImage.setDataId(-1L);
                stationMImage.setCatId(collectTask.getImportCategoryId());
                stationMImage.setPublishTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000"));
                stationMImage.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                stationMImage.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                stationMImage.setViews(Integer.parseInt(views));
                finalMap.put("stationMImage", stationMImage);

                StationMImageData2 stationMImageData = new StationMImageData2();

                stationMImageData.setUuid(UUID.randomUUID().toString());
                stationMImageData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));
                stationMImageData.setComefrom(StringUtils.trim(localRecordMap.get("come_from").toString()));
                stationMImageData.setPhotographer(null);
                stationMImageData.setCreateTime((double) System.currentTimeMillis());
                stationMImageData.setUpdateTime((double) System.currentTimeMillis());
                stationMImageData.setCreateUserId(1);
                stationMImageData.setUpdateUserId(1);
                stationMImageData.setState(2);

                // 作者处理
                String author = (String) localRecordMap.get("author");
                if (!StringUtils.isBlank(author)) {
                    author = author.trim();
                }
                stationMImageData.setAuthor(author);

                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "正文解析失败", e.getMessage());
                }

                // 去除正文中的无效图片
                Elements select2 = document.select("img");
                for (Element e : select2) {
                    String attr = e.attr("src");
                    if (!attr.contains("/oss/") && !attr.contains(collectTask.getHost())) {
                        e.remove();
                    }
                }

                Elements imgList = document.select("img");

                // 摘要处理
                String description = localRecordMap.get("description").toString();
                if (StringUtils.isBlank(description)) {
                    String text = document.text();
                    text = "    " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                stationMImageData.setDescription(description);


                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                //----------------------------------对图片放置方法的处理
                String images = "";
                stationMImageData.setImages(images);

                finalMap.put("stationMImageData", stationMImageData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            for (Map<String, Object> finalMap : finalMaps) {
                StationMReference stationMImage2 = (StationMReference) finalMap.get("stationMImage");
                StationMImageData2 stationMImageData2 = (StationMImageData2) finalMap.get("stationMImageData");

                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 落库 -> station_m_reference
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).insert(stationMImage2);

                // TODO 切换为动态数据源
                DynamicDataSource.changeResourceDynamicDataSource();
                // 落库 -> resource_data_doctor
                ApplicationContextProvider.getBeanByType(StationMImageDataMapper2.class).insert(stationMImageData2);

                //落库 -> folder_resource
                FolderResource folderResource= new FolderResource().builder()
                        .userId(1).folderId(2).modelId(stationCategory2.getModelId())
                        .resourceId(stationMImageData2.getDataId())
                        .createTime((double) System.currentTimeMillis())
                        .updateTime((double) System.currentTimeMillis())
                        .version(1).sort(stationMImageData2.getDataId())
                        .listOrder(0).state(2).isDeleted(0).build();
                ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

                stationMImage2.setDataId(Long.valueOf(folderResource.getId()));
                stationMImage2.setSortLevel(Math.toIntExact(stationMImage2.getId()));


                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 更新 -> station_m_reference
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).updateById(stationMImage2);

            };
        }
        finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "6";
    }
}
