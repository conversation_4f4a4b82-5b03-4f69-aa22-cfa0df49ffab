package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.common.constants.ConfigConstant;
import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.hms.HmsSiteMapper;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.HmsCNews;
import com.ruifox.collect.module.entity.hms.HmsCNewsData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.hms.HmsSite;
import com.ruifox.collect.util.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ImportTypeNews implements ImportType {

    // FIXME 新闻、医生等的导入逻辑都是一样的，只是需要的字段不同，这个存在很多的可复用，后续修改

    @Override
    public void singleImport(CollectTask collectTask) {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), hmsCategory.getId(), hmsCategory.getName());
            }

            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain() + "oss/";

            // TODO 切回为本地数据源
            DynamicDataSource.changeDefaultDataSource();
            // 构造本地路径替换前缀（示例路径：C:/Collect_Data/www.cdlyy.com/
            String localPathPrefix = ConfigUtil.getProperties(ConfigConstant.SAVE_PATH) + collectTask.getHost() + "/";
            // 查询本地所有数据，根据sort将排在最后的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());

            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");

                // TODO 导入时的特殊处理方法（兜底操作，一般用于规范数据
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.info("当前不需要特殊处理！");
                } else {
                    specialHanding.newsSpecialHanding(localRecordMap, collectTask);
                }

                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至hms_c_news
                HmsCNews hmsCNews = new HmsCNews();
                hmsCNews.setDataid(-1L);
                hmsCNews.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCNews.setUsername(1);
                hmsCNews.setStatus((byte) 99);
                hmsCNews.setState(0);
                String publish_time = localRecordMap.get(NewsDataConstant.PUBLISH_TIME).toString();
                if (StringUtils.isBlank(publish_time)) {
                    publish_time = "0";
                }
                hmsCNews.setPublishTime(Integer.parseInt(publish_time));
                hmsCNews.setExpireTime(0);
                hmsCNews.setInputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCNews.setUpdateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCNews.setIslink((int) localRecordMap.get("flag") == 0 ? null : (String) localRecordMap.get("target_url"));

                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                }

                // 去除正文中的无效图片
                Elements select2 = document.select("img");
                for (Element e : select2) {
                    String attr = e.attr("src");
                    if (!attr.contains("/oss/") && !attr.contains(collectTask.getHost())) {
                        e.remove();
                    }
                }

                Elements imgList = document.select("img");
                Elements linkList = document.select("a");


                try {
                    hmsCNews.setContentNum(document.text().length());
                } catch (Exception e) {
                    hmsCNews.setContentNum(0);
                }
                hmsCNews.setContentImageNum(imgList.size());
                hmsCNews.setContentLinkNum(linkList.size());
                hmsCNews.setTop(0L);
                hmsCNews.setListorder(-99L);
                hmsCNews.setSort(1);
                hmsCNews.setOldCatid(null);
                hmsCNews.setEndOperator(1);
                hmsCNews.setIsLocked(false);
                finalMap.put("hmsCNews", hmsCNews);

                // TODO 处理至hms_c_news_data
                HmsCNewsData hmsCNewsData = new HmsCNewsData();
                hmsCNewsData.setDid(hmsCNews.getId());
                hmsCNewsData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));
                hmsCNewsData.setTopTitle(null);
                hmsCNewsData.setSubTitle(null);
                hmsCNewsData.setOriginalTitle(null);

                // 缩略图处理 （只有一张图片）
                String thumb = localRecordMap.get("thumb").toString();
                if (StringUtils.isBlank(thumb)) {
                    if (!imgList.isEmpty()) {
                        Element element = imgList.get(0);
                        String imgUrl = element.attr("src");
                        boolean check = FileUtil.localUrlChick(imgUrl);
                        if (check) {
                            String attr = imgList.first().attr("src");
                            thumb = attr;
                        } else {
                            thumb = "";
                        }
                    } else {
                        thumb = "";
                    }
                }
                thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                hmsCNewsData.setThumb(thumb);

                hmsCNewsData.setComefrom(StringUtils.trim(localRecordMap.get("come_from").toString()));

                // 摘要处理
                String description = localRecordMap.get("description").toString();
                if (StringUtils.isBlank(description)) {
                    String text = document.text();
                    text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                hmsCNewsData.setDescription(description);

                // 作者处理
                String author = (String) localRecordMap.get("author");
                List<String> authors = new ArrayList<>();
                if (!StringUtils.isBlank(author)) {
                    author = author.trim();
                    authors = Arrays.stream(author.split("\\s+")).collect(Collectors.toList());
                }
                hmsCNewsData.setAuthor(JsonUtil.obj2String(authors));

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                hmsCNewsData.setViews(Integer.parseInt(views));

                // 图片路径集合处理
                try {
                    List<String> imgUrls = new ArrayList<>();
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                imgUrl = imgUrl.replace(localPathPrefix, cloudPathPrefix);
                                imgUrls.add(imgUrl);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                    hmsCNewsData.setContentImage(JsonUtil.obj2String(imgUrls));
                } catch (Exception e) {
                    hmsCNewsData.setContentImage("[]");
                }

                // 链接路径集合处理
                try {
                    List<String> attachUrls = new ArrayList<>();
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNoneBlank(attachUrl)) {
                            if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                continue;
                            }
                            boolean check = FileUtil.localUrlChick(attachUrl);
                            if (check) {
                                attachUrl = attachUrl.replace(localPathPrefix, cloudPathPrefix);
                                attachUrls.add(attachUrl);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                            attachUrl = attachUrl.replace(localPathPrefix, cloudPathPrefix);
                            attachUrls.add(attachUrl);
                        }
                    }
                    hmsCNewsData.setContentLink(JsonUtil.obj2String(attachUrls));
                } catch (Exception e) {
                    hmsCNewsData.setContentLink("[]");
                }

                content = ElementUtil.getContentRemoveHtml(document);
                // TODO 这里图片处理完了，可以替换掉正文中的文件路径了
                if (StringUtils.isNotBlank(content)) {
                    content = content.replace(localPathPrefix, cloudPathPrefix);
                }


                hmsCNewsData.setContent(content);

                hmsCNewsData.setTopTitle(null);
                hmsCNewsData.setSubTitle(null);
                hmsCNewsData.setOriginalTitle(null);
                hmsCNewsData.setRelatedExpert(null);
                hmsCNewsData.setRelatedDepart(null);
                hmsCNewsData.setPhotographer("[]");
                hmsCNewsData.setRelevantFiles("[]");
                hmsCNewsData.setEditors(null);
                hmsCNewsData.setExecutiveEditor(null);
                hmsCNewsData.setExamineArticle(null);

                finalMap.put("hmsCNewsData", hmsCNewsData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCNews hmsCNews = (HmsCNews) finalMap.get("hmsCNews");
                    HmsCNewsData hmsCNewsData = (HmsCNewsData) finalMap.get("hmsCNewsData");

                    // 落库 -> hms_c_news
                    ApplicationContextProvider.getBeanByType(HmsCNewsMapper.class).insert(hmsCNews);
                    // 栏目缩写+类型id(文章、医师等)+栏目id+导入时数据的id
                    hmsCNews.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCNews.getId()) + ".html");
                    hmsCNews.setListorder(hmsCNews.getId());
                    hmsCNews.setDataid(hmsCNews.getId());

                    // 更新 -> hms_c_news
                    ApplicationContextProvider.getBeanByType(HmsCNewsMapper.class).updateById(hmsCNews);
                    hmsCNewsData.setDid(hmsCNews.getId());

                    // 落库 -> hms_c_news_data
                    ApplicationContextProvider.getBeanByType(HmsCNewsDataMapper.class).insert(hmsCNewsData);
                }
                return null;
            });
        } finally {
            RedisGetManager.deleteRedisById(collectTask.getId());
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "1";
    }
}
