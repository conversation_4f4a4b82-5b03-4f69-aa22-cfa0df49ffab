package com.ruifox.collect.designmode.strategy.SpecialHandingImpl.doctor;

import com.ruifox.collect.common.constants.DoctorDataConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.ElementUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Request;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class mysdsrmWebSpecialHandingDoctor implements WebSpecialHanding {
    /**
     * 必须重写！这是这个策略类的"身份证"。
     * 项目会根据域名和objectId自动匹配到这个类。
     * @return "www.scmy120.com" 是域名, "2" 是医生模型的objectId.
     */
    @Override
    public String getBeanFlag() {
        return "www.scmy120.com-2";
    }

    /**
     * 清洗从列表页获取的数据。
     * 对于这个网站，列表页的数据很干净，可能不需要太多处理。
     */
    @Override
    public void specialHandingForListItem(Map<String, String> listItemMap, CollectTask collectTask, Request request) {
        // 1. 清洗医生姓名（医生表中用title字段存储医生姓名）
        String name = listItemMap.get("title");
        if (name != null) {
            listItemMap.put("title", name.trim());
        }

        // 2. 清洗职务信息（医生表中用doc_position字段存储职称）
        String docPosition = listItemMap.get("doc_position");
        if (docPosition != null) {
            listItemMap.put("doc_position", docPosition.trim());
        }

        // 3. 清洗科室信息（医生表中用depart字段存储科室）
        String department = listItemMap.get("depart");
        if (department != null) {
            listItemMap.put("depart", department.trim());
        }

        // 4. 处理擅长
        String description = listItemMap.get("goodat");
        if (description != null) {
            // 如果原本就是null，设为空字符串
            listItemMap.put("goodat", "");
        }

        // 5. 处理头像下载（从thumb字段获取图片URL）
        String thumbUrl = listItemMap.get("thumb");
        if (thumbUrl != null && !thumbUrl.trim().isEmpty()) {
            try {
                // 补全相对路径为绝对路径
                if (thumbUrl.startsWith("/")) {
                    thumbUrl = "http://www.scmy120.com" + thumbUrl;
                }

                // 使用项目标准的文件下载方法
                String localThumbPath = FileDataManager.downloadFromHttpUrl(
                        thumbUrl,
                        request.getUrl(),
                        request.getExtra(RequestConstant.PAGE_ID),
                        collectTask
                );

                if (StringUtils.isNotBlank(localThumbPath)) {
                    listItemMap.put("thumb", localThumbPath);
                }
            } catch (Exception e) {
                System.err.println("头像下载失败: " + thumbUrl + ", 错误: " + e.getMessage());
            }
        }
    }

    /**
     * 清洗从详情页获取的数据。
     * 主要任务是下载医生头像并处理个人简介。
     */
    @Override
    public void specialHandingForNotEmbedded(Map<String, String> detailItemMap, CollectTask collectTask, Request request) {
        String content = detailItemMap.get(DoctorDataConstant.CONTENT);
        if (StringUtils.isNotBlank(content)) {
            Document contextDom = Jsoup.parse(content);
            // 1.删除不需要的元素（写了一个常用的通用方法
            ElementUtil.deleteNoContent(contextDom);

            //删除正文中a标签，且该标签所对应的是原网站的html
            ElementUtil.deleteHtmlA(contextDom,collectTask);

            //格式化
            ElementUtil.modifyFormat(contextDom);

            // 2.更新正文（写了一个常用的通用方法
            content = ElementUtil.getContentRemoveHtml(contextDom);

            detailItemMap.put(DoctorDataConstant.CONTENT, content);
        } else {
            detailItemMap.put(DoctorDataConstant.CONTENT, "");
        }
    }

    @Override
    public boolean specialHandingForJudgeHttpUrl(String url, CollectTask collectTask) {
        if (StringUtils.isBlank(url) || "about:blank".equals(url)
                || url.startsWith("javascript") || url.endsWith("/")
                || url.contains(".htm") || !url.contains(collectTask.getHost())
                || url.contains("/oss/")) {
            return false;
        }
        return true;
    }

    @Override
    public void openInit(RemoteWebDriver driver, CollectTask collectTask) {
        // 先执行默认的登录初始化逻辑
        log.info("driver登录页-->不需要特殊处理....");
        
        // 只有在特定条件下才点击【更多】按钮
        // 这里可以根据URL、任务ID或其他条件来判断是否需要点击
        if (needClickMoreButton(collectTask)) {
            clickMoreButton(driver);
        }
    }
    
    /**
     * 判断是否需要点击【更多】按钮
     * 可以根据URL、任务类型、或其他条件来判断
     */
    private boolean needClickMoreButton(CollectTask collectTask) {
        // 方法1：根据URL判断
        String targetUrl = collectTask.getTargetUrl();
        if (targetUrl != null && targetUrl.contains("show-")) {
            return true;
        }
    
        
        return false; // 默认不需要点击
    }
    
    /**
     * 点击【更多】按钮的具体实现
     */
    private void clickMoreButton(RemoteWebDriver driver) {
        try {
            log.info("开始特殊处理: 点击【更多】按钮触发弹窗");
            
            // 等待页面加载完成
            WebDriverWait wait = new WebDriverWait(driver, 10);
            
            // 先检查按钮是否存在
            List<WebElement> moreButtons = driver.findElements(By.cssSelector("div.gengduo"));
            if (moreButtons.isEmpty()) {
                log.info("页面中没有找到【更多】按钮，跳过点击操作");
                return;
            }
            
            // 等待【更多】按钮出现并可点击
            WebElement moreButton = wait.until(ExpectedConditions.elementToBeClickable(
                By.cssSelector("div.gengduo")
            ));
            
            log.info("找到【更多】按钮，准备点击");
            
            // 点击【更多】按钮触发弹窗
            moreButton.click();
            
            // 等待弹窗或更多内容加载完成
            Thread.sleep(2000); // 简单等待2秒让内容加载
            
            log.info("【更多】按钮点击成功，弹窗内容已加载");
            
        } catch (Exception e) {
            log.error("点击【更多】按钮失败: {}", e.getMessage());
            // 不抛出异常，让程序继续执行
        }
    }
}
