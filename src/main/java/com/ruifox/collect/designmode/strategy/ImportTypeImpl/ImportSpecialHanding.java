package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.ruifox.collect.module.entity.CollectTask;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public interface ImportSpecialHanding {
    org.slf4j.Logger log = LoggerFactory.getLogger(ImportSpecialHanding.class);


    /**
     * 采集数据特殊处理
     */
    default void dataSpecialHanding(List<Map<Object, Object>> localRecords, CollectTask collectTask) {
        log.warn("当前不需要特殊处理！！！");
    }


    /**
     * 新闻特殊处理
     */
    default void newsSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-新闻-没有特殊处理类！");
    }
    
    /**
     * 医生特殊处理
     */
    default void doctorSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-医生-没有特殊处理类！");
    }
    
    /**
     * 单页特殊处理
     */
    default void pageSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-单页-没有特殊处理类！");
    }
    
    
    /**
     * 教师特殊处理
     */
    default void teacherSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-教师-没有特殊处理类！");
    }
    
    /**
     * 领导特殊处理
     */
    default void leaderSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-领导-没有特殊处理类！");
    }
    
    /**
     * 图集特殊处理
     */
    default void imageSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-图片-没有特殊处理类！");
    }
    
    /**
     * 药剂特殊处理
     */
    default void medicamentSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-药剂-没有特殊处理类！");
    }

    /**
     * 院报特殊处理
     */
    default void newspaperSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-院报-没有特殊处理类！");
    }
    
    /**
     * 招标特殊处理
     */
    default void tenderSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-招标-没有特殊处理类！");
    }
    
    /**
     * 下载特殊处理
     */
    default void downloadSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-下载-没有特殊处理类！");
    }
    
    /**
     * 视频特殊处理
     */
    default void videoSpecialHanding(Map<Object, Object> localRecordMap, CollectTask collectTask) {
        log.warn("当前-视频-没有特殊处理类！");
    }
    
    String getHost();
}
