package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.*;
import com.ruifox.collect.dao.mapper.hms.HmsCVideoDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCVideoMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.hms.HmsSiteMapper;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.*;
import com.ruifox.collect.module.entity.hms.HmsCVideo;
import com.ruifox.collect.module.entity.hms.HmsCVideoData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.hms.HmsSite;
import com.ruifox.collect.util.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.util.*;


@Component
@Slf4j
public class ImportTypeVideo implements ImportType {


    @Override
    public void singleImport(CollectTask collectTask) {
        try {
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());
            // 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // 构造本地路径替换前缀
            String localPathPrefix = "C:\\" + collectTask.getHost() + "\\"; // 示例路径：C:\\www.cdlyy.com\\

            // 基本数据表名
            String dataTableName = collectTask.getDataTableName();

            // 查询本地所有数据(按项目组要求的排序规则进行排序)
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderByReleased(dataTableName);

            // 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();

            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请设置当前导入的栏目ID!");
                return;
            }
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain();

            log.info("当前导入栏目ID:{},当前导入栏目名称:{}", hmsCategory.getId(), hmsCategory.getName());

            // 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // TODO: ZhangXinYu 2024/6/17 | 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();

            // 处理本地数据至hms_c_video和hms_c_video_data
            long i = 1;
            for (Map<Object, Object> localRecordMap : localRecords) {
                System.out.println("当前正在处理第 " + i++ + " 条数据...");

                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                } else {
                    specialHanding.videoSpecialHanding(localRecordMap, null);
                }

                HashMap<String, Object> finalMap = new HashMap<>();

                //获取发布时间
                String publishTime = String.valueOf(localRecordMap.get("publish_time"));
                if (StringUtils.isBlank(publishTime)) {
                    publishTime = "0";
                }

                // 处理至hms_c_video
                HmsCVideo hmsCVideo = HmsCVideo
                        .builder()
                        .oldCatid(null)
                        .catid(Integer.valueOf(String.valueOf(hmsCategory.getId())))
                        .username(1)
                        .endOperator(1)
                        .url("")
                        .islink((int) localRecordMap.get("flag") == 1 ? null : (String) localRecordMap.get("target_url"))
                        .top(0)
                        .listorder(-99L)
                        .sort(1)
                        .state(0)
                        .status(99)
                        .expireTime(0)
                        .publishTime(Integer.valueOf(publishTime))
                        .inputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)))
                        .updateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)))
                        .isLocked(0)
                        .views((int) localRecordMap.get("views"))
                        .build();

                finalMap.put("hmsCVideo", hmsCVideo);

                // 获取正文
                String content = (String) localRecordMap.get("content");

                // 构造储存实体对象
                HmsCVideoData hmsCVideoData = HmsCVideoData
                        .builder()
                        .title((String) localRecordMap.get("title"))
                        .build();

                //封面图处理
                String thumb = (String) localRecordMap.get("thumb");
                if (thumb == null || thumb.isEmpty()) {
                    hmsCVideoData.setThumb("");
                } else {
                    boolean check = FileUtil.localUrlChick(thumb);
                    if (check) {
                        thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                        hmsCVideoData.setThumb(thumb);
                    } else {
                        hmsCVideoData.setThumb("");
                    }
                }

                // 来源处理
                String comeFrom = (String) localRecordMap.get("come_from");
                if (comeFrom == null || comeFrom.isEmpty()) {
                    comeFrom = "";
                } else {
                    if (comeFrom.equals("管理员")) {
                        comeFrom = "";
                    }
                }
                hmsCVideoData.setComefrom(comeFrom);


                //视频处理
                Document document = Jsoup.parse(content);
                Element videoElement = document.select("video").first();
                if (videoElement == null) {
                    hmsCVideoData.setVideo(null);
                } else {
                    String video = videoElement.attr("src");
                    video = video.replace(localPathPrefix, cloudPathPrefix);
                    hmsCVideoData.setVideo(video);
                }

                // 处理摘要和正文需要看具体的正文是什么样子的

                // 处理摘要
                document.select("video").remove();
                Elements imgElements = document.select("img");
                for (Element imgElement : imgElements) {
                    String attr = imgElement.attr("src");
                    if (attr == null || attr.isEmpty()) {
                        continue;
                    }
                    boolean check = FileUtil.localUrlChick(attr);
                    if (check) {
                        attr = attr.replace(localPathPrefix, cloudPathPrefix);
                        imgElement.attr("src", attr);
                    }
                }
                String summary = document.body().text();
                if (summary == null || summary.isEmpty()) {
                    summary = "";
                }
                hmsCVideoData.setDescription(summary);


                // 这里图片、视频处理完了，可以替换掉正文中的文件路径了
                if (content != null && !content.isEmpty()) {
                    content = content.replace(localPathPrefix, cloudPathPrefix);
                    localRecordMap.put("content", content);
                }
                hmsCVideoData.setContent(content);

                finalMap.put("hmsCVideoData", hmsCVideoData);
                finalMaps.add(finalMap);
            }

            DynamicDataSource.changeDynamicDataSource();

            // 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCVideo hmsCVideo = (HmsCVideo) finalMap.get("hmsCVideo");
                    HmsCVideoData hmsCVideoData = (HmsCVideoData) finalMap.get("hmsCVideoData");

                    // 落库 -> hms_c_video
                    ApplicationContextProvider.getBeanByType(HmsCVideoMapper.class).insert(hmsCVideo);
                    hmsCVideo.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCVideo.getId()) + ".html");
                    hmsCVideo.setListorder(hmsCVideo.getId());
                    hmsCVideo.setDataid(hmsCVideo.getId());

                    // 更新 -> hms_c_video
                    ApplicationContextProvider.getBeanByType(HmsCVideoMapper.class).updateById(hmsCVideo);
                    hmsCVideoData.setDid(hmsCVideo.getId());

                    // 落库 -> hms_c_video_data
                    ApplicationContextProvider.getBeanByType(HmsCVideoDataMapper.class).insert(hmsCVideoData);
                }

                return null;
            });

        } finally {
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "11";
    }
}
