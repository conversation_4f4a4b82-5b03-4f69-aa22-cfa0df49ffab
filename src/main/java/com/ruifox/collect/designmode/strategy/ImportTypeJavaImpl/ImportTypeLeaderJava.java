package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.util.*;

@Component
@Slf4j
public class ImportTypeLeaderJava implements ImportTypeJava {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            // 得到党内职务的集合
            Map<Object, Object> record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithPartyPosition(stationCategory.getModelId());
            Map<Object, Object> setting = JsonUtil.Json2Obj((String) record.get("setting"), Map.class);
            List<Map<Object, Object>> PartyPositionNum2PartyPositionMaps = (List<Map<Object, Object>>) setting.get("options");
            Map<String, Integer> PartyPositionNum2PartyPosition = new HashMap<>();
            PartyPositionNum2PartyPositionMaps.forEach(item -> PartyPositionNum2PartyPosition.put(item.get("label").toString(), Integer.parseInt(item.get("value").toString())));

            // 得到领导职务的集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithLeaderPosition(stationCategory.getModelId());
            setting = JsonUtil.Json2Obj((String) record.get("setting"), Map.class);
            List<Map<Object, Object>> LeaderPositionNum2LeaderPositionMaps = (List<Map<Object, Object>>) setting.get("options");
            Map<String, Integer> LeaderPositionNum2LeaderPosition = new HashMap<>();
            LeaderPositionNum2LeaderPositionMaps.forEach(item -> LeaderPositionNum2LeaderPosition.put(item.get("label").toString(), Integer.parseInt(item.get("value").toString())));

            // 得到教学岗位的集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithEduPost(stationCategory.getModelId());
            setting = JsonUtil.Json2Obj((String) record.get("setting"), Map.class);
            List<Map<Object, Object>> eduPostNum2eduPostMaps = (List<Map<Object, Object>>) setting.get("options");
            Map<String, Integer> eduPost2EduPostNumMap = new HashMap<>();
            eduPostNum2eduPostMaps.forEach(item -> eduPost2EduPostNumMap.put(item.get("label").toString(), Integer.parseInt(item.get("value").toString())));

            // 得到教务职称的集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithLeaderPosition(stationCategory.getModelId());
            setting = JsonUtil.Json2Obj((String) record.get("setting"), Map.class);
            List<Map<Object, Object>> eduPositionNum2eduPositionMaps = (List<Map<Object, Object>>) setting.get("options");
            Map<String, Integer> eduPosition2EduPositionNumMap = new HashMap<>();
            eduPositionNum2eduPositionMaps.forEach(item -> eduPosition2EduPositionNumMap.put(item.get("label").toString(), Integer.parseInt(item.get("value").toString())));

            // 得到医师职称的集合
            record = ApplicationContextProvider.getBeanByType(StationModelFieldMapper.class)
                    .selectByModelIdWithLeaderPosition(stationCategory.getModelId());
            setting = JsonUtil.Json2Obj((String) record.get("setting"), Map.class);
            List<Map<Object, Object>> docPositionNum2docPositionMaps = (List<Map<Object, Object>>) setting.get("options");
            Map<String, Integer> docPosition2DocPositionNumMap = new HashMap<>();
            docPositionNum2docPositionMaps.forEach(item -> docPosition2DocPositionNumMap.put(item.get("label").toString(), Integer.parseInt(item.get("value").toString())));


            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在前面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_article
                StationMLeader stationMLeader = new StationMLeader();

                stationMLeader.setDataId(-1L);
                stationMLeader.setCatId(collectTask.getImportCategoryId());
                stationMLeader.setPublishUserId(1);
                stationMLeader.setCreateTime(Double.valueOf(localRecordMap.get("publish_time").toString() + "000"));
                stationMLeader.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                stationMLeader.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                stationMLeader.setViews(Integer.parseInt(views));
                finalMap.put("stationMLeader", stationMLeader);

                StationMLeaderData stationMLeaderData = new StationMLeaderData();

                stationMLeaderData.setUuid(UUID.randomUUID().toString());
                stationMLeaderData.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));

                // 头像处理
                String thumb = localRecordMap.get("thumb").toString();
                if (StringUtils.isNotBlank(thumb)) {
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    stationMLeaderData.setThumb(thumb);
                } else {
                    stationMLeaderData.setThumb("");
                }

                //TODO 处理方式不同-----------------------------------

                // 党内职务处理

                // 领导职务处理

                // 教学岗位处理

                // 教务职称处理

                // 医师职称处理

                // 任职时间处理

                // 相关职务处理


                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "正文解析失败", e.getMessage());
                }

                content = document.text();
                stationMLeaderData.setContent(content);

                finalMap.put("stationMLeaderData", stationMLeaderData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    StationMLeader stationMLeader = (StationMLeader) finalMap.get("stationMLeader");
                    StationMLeaderData stationMLeaderData = (StationMLeaderData) finalMap.get("stationMLeaderData");

                    // 落库 -> station_m_leader
                    ApplicationContextProvider.getBeanByType(StationMLeaderMapper.class).insert(stationMLeader);
                    stationMLeader.setSortLevel(Math.toIntExact(stationMLeader.getId()));


                    // 落库 -> station_m_leader_data
                    ApplicationContextProvider.getBeanByType(StationMLeaderDataMapper.class).insert(stationMLeaderData);
                    stationMLeader.setDataId(stationMLeaderData.getDataId());

                    // 更新 -> station_m_leader
                    ApplicationContextProvider.getBeanByType(StationMLeaderMapper.class).updateById(stationMLeader);

                }
                return null;
            });
        }
        finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "5";
    }
}
