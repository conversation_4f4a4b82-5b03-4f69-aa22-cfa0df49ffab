package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Slf4j
public class ImportTypeNewspaperJava implements ImportTypeJava {
    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }


            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在前面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_article
                StationMNewspaper stationMNewspaper = new StationMNewspaper();

                stationMNewspaper.setDataId(-1L);
                stationMNewspaper.setCatId(collectTask.getImportCategoryId());
                stationMNewspaper.setPublishUserId(1);
                stationMNewspaper.setCreateTime(Double.valueOf(localRecordMap.get("publish_time").toString() + "000"));
                stationMNewspaper.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                stationMNewspaper.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                stationMNewspaper.setViews(Integer.parseInt(views));
                finalMap.put("stationMNewspaper", stationMNewspaper);

                StationMNewspaperData stationMNewspaperData = new StationMNewspaperData();

                stationMNewspaperData.setUuid(UUID.randomUUID().toString());
                // 标题处理
                String title = localRecordMap.get("title").toString();
                stationMNewspaperData.setTitle(StringUtils.trim(title));

                // 来源处理
                String comefrom = localRecordMap.get("comefrom").toString();
                if(StringUtils.isNotBlank(comefrom)){
                    stationMNewspaperData.setComefrom(comefrom);
                }else{
                    stationMNewspaperData.setComefrom("");
                }

                // 总期数处理(一般是从标题中获取)
                String publishNums = "总" + title.substring(title.indexOf("第"), title.indexOf("期") + 1);
                stationMNewspaperData.setPublishNums(Integer.parseInt(publishNums));

                // 期数处理(标题中获取)
                String publishIndex =  title.substring(title.indexOf("第") + 1, title.indexOf("期"));
                stationMNewspaperData.setPublishIndex(Integer.parseInt(publishIndex));

                // 发布年限处理
                stationMNewspaperData.setPublishYear(Integer.valueOf(localRecordMap.get("publish_time").toString() + "000"));

                // 院报内容处理
                String content = localRecordMap.get("content").toString();
                Document document = Jsoup.parse(content);
                Elements imgElements = document.select("img");
                int index = 0;
                List<Map<String, Object>> imgList = new ArrayList<>();
                for(Element imgElement : imgElements){
                    String imgSrc = imgElement.attr("src");
                    String url = "";
                    if(StringUtils.isNotBlank(imgSrc)){
                        url = CrawlerManager.changeFileUrl(new File(imgSrc));
                    }
                    Map<String, Object> imgMap = new HashMap<>();
                    imgMap.put("url", url);
                    imgMap.put("title", String.valueOf(++index));
                    List<String> articleList = new ArrayList<>();
                    imgMap.put("article", articleList);
                    imgList.add(imgMap);
                }
                stationMNewspaperData.setNewspaper(JsonUtil.obj2String(imgList));

                //TODO 关联文章列表处理
                stationMNewspaperData.setArticleList(null);


                finalMap.put("stationMNewspaperData", stationMNewspaperData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    StationMNewspaper stationMNewspaper = (StationMNewspaper) finalMap.get("stationMNewspaper");
                    StationMNewspaperData stationMNewspaperData = (StationMNewspaperData) finalMap.get("stationMNewspaperData");

                    // 落库 -> station_m_newspaper
                    ApplicationContextProvider.getBeanByType(StationMNewspaperMapper.class).insert(stationMNewspaper);
                    stationMNewspaper.setSortLevel(Math.toIntExact(stationMNewspaper.getId()));

                    // 落库 -> station_m_newspaper_data
                    ApplicationContextProvider.getBeanByType(StationMNewspaperDataMapper.class).insert(stationMNewspaperData);
                    stationMNewspaper.setDataId(stationMNewspaperData.getDataId());

                    // 更新 -> station_m_newspaper
                    ApplicationContextProvider.getBeanByType(StationMNewspaperMapper.class).updateById(stationMNewspaper);

                }
                return null;
            });
        }
        finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "8";
    }
}
