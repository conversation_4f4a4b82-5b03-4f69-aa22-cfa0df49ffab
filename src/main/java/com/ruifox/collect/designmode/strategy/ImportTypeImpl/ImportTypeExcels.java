package com.ruifox.collect.designmode.strategy.ImportTypeImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.designmode.factory.ImportSpecialHandingFactory;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.ElementUtil;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.time.LocalDateTime;
import java.util.*;

@Component
@Slf4j
public class ImportTypeExcels implements ImportType {
    @Override
    public void singleImport(CollectTask collectTask) {
        try {
            log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), collectTask.getImportCategoryId(), collectTask.getImportCategoryName());

            // TODO 切换为本地数据源
            DynamicDataSource.changeDefaultDataSource();
            // 构造本地路径替换前缀
            String localPathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/"; // 示例路径：C:/www.cdlyy.com/
            // 基本数据表名
            String dataTableName = collectTask.getDataTableName();
            // 查询本地所有数据
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(dataTableName);

            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.error("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            }
            log.info("当前导入栏目ID:{},当前导入栏目名称:{}", hmsCategory.getId(), hmsCategory.getName());
            // 云端替换路径前缀
            LambdaQueryWrapper<HmsSite> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(HmsSite::getStatus, 1);
            HmsSite hmsSite = ApplicationContextProvider.getBeanByType(HmsSiteMapper.class).selectList(lambdaQueryWrapper).get(0);
            String cloudPathPrefix = hmsSite.getDomain()+"oss/";

            // TODO 切回本地数据源
            DynamicDataSource.changeDefaultDataSource();

            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();

            // 处理本地数据至hms_c_excels和hms_c_excels_data
            long i = 1;
            String sortTmp = "0";
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if(sort.equals(sortTmp)){
                    continue;
                }
                sortTmp = sort;

                System.out.println("当前正在处理第 " + (i++) + " 条数据...");
                HashMap<String, Object> finalMap = new HashMap<>();

                // 导入时的特殊处理方法
                ImportSpecialHanding specialHanding = ImportSpecialHandingFactory.getSpecialHanding(collectTask);
                if (specialHanding == null) {
                    log.warn("当前没有特殊处理类！");
                } else {
                    specialHanding.newsSpecialHanding(localRecordMap, collectTask);
                }

                // TODO 处理至hms_c_news\
                HmsCExcels hmsCExcels = new HmsCExcels();
                hmsCExcels.setDataid(1L);
                hmsCExcels.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCExcels.setUsername(1);
                hmsCExcels.setEndOperator(1);
                hmsCExcels.setUrl("");
                hmsCExcels.setIslink((int) localRecordMap.get("flag") == 0 ? null : (String) localRecordMap.get("target_url"));
                hmsCExcels.setTop(0);
                hmsCExcels.setListorder(-99L);
                hmsCExcels.setSort(1);
                hmsCExcels.setState(0);
                hmsCExcels.setStatus(99);

                // FIXME
                /*String publishTime = String.valueOf(localRecordMap.get("publish_time"));
                if (StringUtils.isBlank(publishTime)) {
                    publishTime = "0";
                }*/
                //发布时间就是当前时间
                hmsCExcels.setPublishTime((int) System.currentTimeMillis());
                hmsCExcels.setExpireTime(0);
                hmsCExcels.setInputTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCExcels.setUpdateTime(Integer.valueOf(String.valueOf(System.currentTimeMillis() / 1000)));
                hmsCExcels.setViews(0);
                hmsCExcels.setIsLocked(0);

                finalMap.put("hmsCExcels", hmsCExcels);


                // TODO 处理至hms_c_news_data
                HmsCExcelsData hmsCExcelsData = new HmsCExcelsData();
                hmsCExcelsData.setDid(hmsCExcels.getDataid());

                String applicantCode = String.valueOf(localRecordMap.get("applicant_code"));
                if(StringUtils.isBlank(applicantCode))
                    applicantCode = "";
                hmsCExcelsData.setApplicantCode(applicantCode);

                String title = String.valueOf(localRecordMap.get("title"));
                if(StringUtils.isBlank(title))
                    title = "";
                hmsCExcelsData.setTitle(title);

                String applicant = String.valueOf(localRecordMap.get("applicant"));
                if(StringUtils.isBlank(applicant))
                    applicant = "";
                hmsCExcelsData.setApplicant(applicant);

                String patentType = String.valueOf(localRecordMap.get("patent_type"));
                if(StringUtils.isBlank(patentType))
                    patentType = "";
                hmsCExcelsData.setPatentType(patentType);

                String inventor = String.valueOf(localRecordMap.get("inventor"));
                if(StringUtils.isBlank(inventor))
                    inventor = "";
                hmsCExcelsData.setInventor(inventor);

                String applicantDate = String.valueOf(localRecordMap.get("applicant_date"));
                if(StringUtils.isBlank(applicantDate))
                    applicantDate = "0";
                hmsCExcelsData.setApplicantDate(Integer.parseInt(applicantDate));

                String inventorArea = String.valueOf(localRecordMap.get("inventor_area"));
                if(StringUtils.isBlank(inventorArea))
                    inventorArea = "";
                hmsCExcelsData.setInventorArea(inventorArea);

                String agent = String.valueOf(localRecordMap.get("agent"));
                if(StringUtils.isBlank(agent))
                    agent = "";
                hmsCExcelsData.setAgent(agent);

                String agency = String.valueOf(localRecordMap.get("agency"));
                if(StringUtils.isBlank(agency))
                    agency = "";
                hmsCExcelsData.setAgency(agency);

                String publicAccount = String.valueOf(localRecordMap.get("public_account"));
                if(StringUtils.isBlank(publicAccount))
                    publicAccount = "";
                hmsCExcelsData.setPublicAccount(publicAccount);

                //不知道是什么，都是0
                hmsCExcelsData.setPublicDate(0);

                String publicIpcType = String.valueOf(localRecordMap.get("public_ipc_type"));
                if(StringUtils.isBlank(publicIpcType))
                    publicIpcType = "0";
                hmsCExcelsData.setPublicIpcType(publicIpcType);

                String theAbstract = String.valueOf(localRecordMap.get("abstract"));
                if(StringUtils.isBlank(theAbstract))
                    theAbstract = "";
                hmsCExcelsData.setTheAbstract(theAbstract);

                String zView = String.valueOf(localRecordMap.get("z_view"));
                if(StringUtils.isBlank(zView))
                    zView = "";
                hmsCExcelsData.setZView(zView);

                String aArticle = String.valueOf(localRecordMap.get("a_article"));
                if(StringUtils.isBlank(aArticle))
                    aArticle = "";
                hmsCExcelsData.setAArticle(aArticle);

                String zyPic = String.valueOf(localRecordMap.get("zy_pic"));
                if(StringUtils.isBlank(zyPic))
                    zyPic = "";
                zyPic = zyPic.replace(localPathPrefix,cloudPathPrefix);
                hmsCExcelsData.setZyPic(zyPic);

                String zQlyq = String.valueOf(localRecordMap.get("z_qlyq"));
                if(StringUtils.isBlank(zQlyq))
                    zQlyq = "";
                hmsCExcelsData.setZQlyq(zQlyq);

                String wClass = String.valueOf(localRecordMap.get("w_class"));
                if(StringUtils.isBlank(wClass))
                    wClass = "";
                hmsCExcelsData.setWClass(wClass);

                String zDirectory = String.valueOf(localRecordMap.get("z_directory"));
                if(StringUtils.isBlank(zDirectory))
                    zDirectory = "";
                hmsCExcelsData.setZDirectory(zDirectory);

                String cpcType = String.valueOf(localRecordMap.get("cpc_type"));
                if(StringUtils.isBlank(cpcType))
                    cpcType = "";
                hmsCExcelsData.setCpcType(cpcType);

                String sqPublicNum = String.valueOf(localRecordMap.get("sq_public_num"));
                if(StringUtils.isBlank(sqPublicNum))
                    sqPublicNum = "";
                hmsCExcelsData.setSqPublicNum(sqPublicNum);

                String sqPublicDate = String.valueOf(localRecordMap.get("sq_public_date"));
                if(StringUtils.isBlank(sqPublicDate))
                    sqPublicDate = "";
                hmsCExcelsData.setSqPublicDate(sqPublicDate);

                String sqApplicant = String.valueOf(localRecordMap.get("sq_applicant"));
                if(StringUtils.isBlank(sqApplicant))
                    sqApplicant = "";
                hmsCExcelsData.setSqApplicant(sqApplicant);

                String sqInventor = String.valueOf(localRecordMap.get("sq_inventor"));
                if(StringUtils.isBlank(sqInventor))
                    sqInventor = "";
                hmsCExcelsData.setSqInventor(sqInventor);

                String sqIpcType = String.valueOf(localRecordMap.get("sq_ipc_type"));
                if(StringUtils.isBlank(sqIpcType))
                    sqIpcType = "";
                hmsCExcelsData.setSqIpcType(sqIpcType);

                String sqInventionName = String.valueOf(localRecordMap.get("sq_invention_name"));
                if(StringUtils.isBlank(sqInventionName))
                    sqInventionName = "";
                hmsCExcelsData.setSqInventionName(sqInventionName);

                String sqAbstract = String.valueOf(localRecordMap.get("sq_abstract"));
                if(StringUtils.isBlank(sqAbstract))
                    sqAbstract = "";
                hmsCExcelsData.setSqAbstract(sqAbstract);

                String briefDescription = String.valueOf(localRecordMap.get("brief_description"));
                if(StringUtils.isBlank(briefDescription))
                    briefDescription = "";
                hmsCExcelsData.setBriefDescription(briefDescription);

                String thumb = String.valueOf(localRecordMap.get("thumb"));
                if(StringUtils.isBlank(thumb))
                    thumb = "";
                thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                hmsCExcelsData.setThumb(thumb);


                List<Map<Object,Object>> zlPdfList = new ArrayList<>();
                Map<Object,Object> map = new HashMap<>();

                String zlPdf = String.valueOf(localRecordMap.get("zl_pdf"));
                if(!zlPdf.isBlank()){
                    map.put("id", System.currentTimeMillis());
                    map.put("name", title+".pdf");
                    zlPdf = zlPdf.replace(localPathPrefix,cloudPathPrefix);
                    map.put("url", zlPdf);
                    zlPdfList.add(map);
                    hmsCExcelsData.setZlPdf(JsonUtil.obj2String(zlPdfList));
                }else{
                    hmsCExcelsData.setZlPdf("");
                }



                finalMap.put("hmsCExcelsData", hmsCExcelsData);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeDynamicDataSource();

            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : finalMaps) {
                    HmsCExcels hmsCExcels = (HmsCExcels) finalMap.get("hmsCExcels");
                    HmsCExcelsData hmsCExcelsData = (HmsCExcelsData) finalMap.get("hmsCExcelsData");

                    // 落库 -> hms_c_excels
                    ApplicationContextProvider.getBeanByType(HmsCExcelsMapper.class).insert(hmsCExcels);
                    hmsCExcels.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCExcels.getId()) + ".html");
                    hmsCExcels.setListorder(hmsCExcels.getId());
                    hmsCExcels.setDataid(hmsCExcels.getId());

                    // 更新 -> hms_c_excels
                    ApplicationContextProvider.getBeanByType(HmsCExcelsMapper.class).updateById(hmsCExcels);
                    hmsCExcelsData.setDid(hmsCExcels.getId());

                    // 落库 -> hms_c_excels_data
                    ApplicationContextProvider.getBeanByType(HmsCExcelsDataMapper.class).insert(hmsCExcelsData);
                }
                return null;
            });
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Override
    public String getBeanFlag() {
        return "13";
    }
}
