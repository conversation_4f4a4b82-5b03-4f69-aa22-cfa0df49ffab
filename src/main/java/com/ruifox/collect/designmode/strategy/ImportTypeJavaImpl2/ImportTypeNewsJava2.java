package com.ruifox.collect.designmode.strategy.ImportTypeJavaImpl2;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;

import com.ruifox.collect.dao.mapper.station2.StationCategoryMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMArticleDataMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMReferenceMapper;
import com.ruifox.collect.designmode.strategy.ImportTypeJava2;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.resource.FolderResource;

import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.module.entity.station2.StationMArticleData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.selector.Html;

import java.io.File;
import java.util.*;
import java.util.regex.Pattern;

@Component
@Slf4j
public class ImportTypeNewsJava2 implements ImportTypeJava2 {

    private static final Pattern EMOJI_PATTERN = Pattern.compile(
            "[\u2600-\u27BF\u2B50-\u2B55\u3030\u303D\u3297\u3299]" +
                    "|\uD83C[\uDDE6-\uDDFF\uDF00-\uDFFF]" +
                    "|\uD83D[\uDC00-\uDE4F\uDE80-\uDEFF]" +
                    "|\uD83E[\uDD00-\uDDFF]"
    );

    public static String removeSpecialCharacters(String input) {
        if (input == null) {
            return null;
        }
        // 使用正则表达式替换匹配到的 4 字节 UTF - 8 字符为空字符串
        return EMOJI_PATTERN.matcher(input).replaceAll("");
    }

    @Override
    public void singleImport(CollectTask collectTask) throws Exception {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeBuildDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory2 stationCategory2 = ApplicationContextProvider.getBeanByType(StationCategoryMapper2.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory2 == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory2.getId(), stationCategory2.getName());
            }

            DynamicDataSource.changeDefaultDataSource();
            // 查询本地所有数据，根据sort将排在后面的先导入
            List<Map<Object, Object>> localRecords = ApplicationContextProvider.getBeanByType(CommonMapper.class).selectByTableNameOrderBySortDesc(collectTask.getDataTableName());
            // 缓存结果
            List<Map<String, Object>> finalMaps = new ArrayList<>();
            String sortTmp = "-1";
            long i = 0;
            for (Map<Object, Object> localRecordMap : localRecords) {
                String sort = localRecordMap.get("sort").toString();
                if (sort.equals(sortTmp)) {
                    continue;
                }
                sortTmp = sort;
                log.info("当前正在处理第 " + (++i) + " 条数据...");


                HashMap<String, Object> finalMap = new HashMap<>();
                // TODO 根据采集对象处理数据至对应类型 处理至station_m_article
                StationMReference reference = new StationMReference();
                reference.setDataId(-1L);
                reference.setCatId(collectTask.getImportCategoryId());
//                stationMArticle2.setPublishUserId(1);
//                stationMArticle2.setCreateTime(Double.valueOf(localRecordMap.get("publish_time").toString() + "000"));
                reference.setState(99);
                //8位随机字符
                String uriRandom = CrawlerManager.randomCharacterGenerator();
                reference.setUri("/" + uriRandom + ".html");

                // 浏览量处理
                String views = localRecordMap.get("views").toString();
                if (StringUtils.isBlank(views)) {
                    views = "0";
                } else {
                    views = StringUtils.trim(views);
                }
                reference.setViews(Integer.parseInt(views));
                if (localRecordMap.get("publish_time")!=null&&!localRecordMap.get("publish_time").equals("")){
                    reference.setPublishTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000") );
                }else {
                    reference.setPublishTime((double) System.currentTimeMillis());
                }

                finalMap.put("stationMArticle", reference);


                StationMArticleData2 stationMArticleData2 = new StationMArticleData2();

                stationMArticleData2.setUuid(UUID.randomUUID().toString());
                stationMArticleData2.setTitle(StringUtils.trim(localRecordMap.get("title").toString()));
                stationMArticleData2.setSubTitle(null);
                stationMArticleData2.setComefrom(StringUtils.trim(localRecordMap.get("come_from").toString()));
                stationMArticleData2.setIsLink((Integer) localRecordMap.get("flag"));
                stationMArticleData2.setIgnoreReason(null);
                stationMArticleData2.setPhotographer(null);
                stationMArticleData2.setCreateUserId(1);
                stationMArticleData2.setUpdateUserId(1);
                stationMArticleData2.setState(2);
                if (localRecordMap.get("publish_time")!=null&&!localRecordMap.get("publish_time").equals("")){
                    stationMArticleData2.setCreateTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000" ));
                    stationMArticleData2.setUpdateTime(Double.valueOf(localRecordMap.get("publish_time").toString()+"000" ) );
                }else {
                    stationMArticleData2.setCreateTime((double) System.currentTimeMillis());
                    stationMArticleData2.setUpdateTime((double) System.currentTimeMillis());
                }



//                stationMArticleData2.setPublishTime(Double.valueOf(localRecordMap.get("publish_time").toString() + "000"));

                // 作者处理
                String author = (String) localRecordMap.get("author");
                if (!StringUtils.isBlank(author)) {
                    author = author.trim();
                }
                stationMArticleData2.setAuthor(author);

                // 获取正文
                String content = localRecordMap.get("content") == null ? "" : localRecordMap.get("content").toString();
                Document document;
                try {
                    document = new Html(content).getDocument();
                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "正文解析失败", e.getMessage());
                }

                // 去除正文中的无效图片
                Elements select2 = document.select("img");
                for (Element e : select2) {
                    String attr = e.attr("src");
                    if (!attr.contains("/oss/") && !attr.contains(collectTask.getHost())) {
                        e.remove();
                    }
                }

                Elements imgList = document.select("img");
                Elements linkList = document.select("a");
                Elements videoList = document.select("video");

                // 缩略图处理 （只有一张图片）
                String thumb = localRecordMap.get("thumb").toString();
                if (StringUtils.isBlank(thumb)) {
                    if (!imgList.isEmpty()) {
                        Element element = imgList.get(0);
                        String imgUrl = element.attr("src");
                        boolean check = FileUtil.localUrlChick(imgUrl);
                        if (check) {
                            String attr = imgList.first().attr("src");
                            thumb = attr;
                        } else {
                            thumb = "";
                        }
                    } else {
                        thumb = "";
                    }
                }
                //调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMArticleData2.setThumb(thumb);


                // 摘要处理
                String description = localRecordMap.get("description").toString();
                if (StringUtils.isBlank(description)) {
                    String text = document.text();
                    text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                stationMArticleData2.setDescription(description);


                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                // 链接路径集合处理
                try {
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNotBlank(attachUrl)) {
                            if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                continue;
                            }
                            if (attachUrl.contains(".shtml")) {
                                element.attr("href", "");
                                continue;
                            }
                            boolean check = FileUtil.localUrlChick(attachUrl);
                            if (check) {
                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                element.attr("href", url);
                            } else {
                                // 移除失效链接
                                element.remove();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                // 视频路径集合处理
                try {
                    for (Element element : videoList) {
                        String videoUrl = element.attr("src");
                        if (StringUtils.isNotBlank(videoUrl)) {
                            String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                            element.attr("src", url);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), "", "下载失败", e.getMessage());
                }

                content = document.toString();

                stationMArticleData2.setContent(content);

                // 外链处理，uri不变，将正文换成链接地址
                if (stationMArticleData2.getIsLink().equals(1)) {
                    stationMArticleData2.setContent(localRecordMap.get("target_url").toString());
                    finalMap.put("stationMArticle", reference);
                }

                finalMap.put("stationMArticleData", stationMArticleData2);
                finalMaps.add(finalMap);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeResourceDynamicDataSource();

            // TODO 添加事务的处理

            for (Map<String, Object> finalMap : finalMaps) {
                StationMReference stationMArticle2 = (StationMReference) finalMap.get("stationMArticle");
                StationMArticleData2 stationMArticleData2 = (StationMArticleData2) finalMap.get("stationMArticleData");

                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 落库 -> station_m_article
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).insert(stationMArticle2);


                // TODO 切换为动态数据源
                DynamicDataSource.changeResourceDynamicDataSource();
                // 落库 -> station_m_article_data
                ApplicationContextProvider.getBeanByType(StationMArticleDataMapper2.class).insert(stationMArticleData2);


                //落库 -> folder_resource
                FolderResource folderResource= new FolderResource().builder()
                        .userId(1).folderId(2).modelId(stationCategory2.getModelId())
                        .resourceId(stationMArticleData2.getDataId())
                        .createTime((double) System.currentTimeMillis())
                        .updateTime((double) System.currentTimeMillis())
                        .version(1).sort(stationMArticleData2.getDataId())
                        .listOrder(0).state(2).isDeleted(0).build();
                ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);


                stationMArticle2.setDataId(Long.valueOf(folderResource.getId()));
                stationMArticle2.setSortLevel(Math.toIntExact(stationMArticle2.getId()));
                // TODO 切换为动态数据源
                DynamicDataSource.changeBuildDynamicDataSource();
                // 更新 -> station_m_article
                ApplicationContextProvider.getBeanByType(StationMReferenceMapper.class).updateById(stationMArticle2);

            };
        } finally {
            // 切回默认数据源
            DynamicDataSource.changeDefaultDataSource();
            RedisGetManager.deleteRedisById(collectTask.getId());
        }
    }

    @Override
    public String getBeanFlag() {
        return "1";
    }
}
