package com.ruifox.collect.designmode.factory;

import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.designmode.strategy.ImportTypeJava;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.system.ApplicationContextProvider;

import java.util.Map;

public class ImportTypeJavaFactory {
    public static ImportTypeJava getImportType(CollectTask collectTask){
        // 1.从IOC容器中获取所有该接口的bean
        Map<String, ImportTypeJava> importTypeJavaMap = ApplicationContextProvider.getBeansOfType(ImportTypeJava.class);
        for (ImportTypeJava importTypeJava : importTypeJavaMap.values()) {
            // 1.2.获取bean标记
            String beanFlag = importTypeJava.getBeanFlag();
            // 1.3 比对
            if(beanFlag.equals(String.valueOf(collectTask.getObjectId()))){
                return importTypeJava;
            }
        }
        throw new RuntimeException("未获取到导入类型处理类，导入类型ID："+collectTask.getObjectId());
    }
}
