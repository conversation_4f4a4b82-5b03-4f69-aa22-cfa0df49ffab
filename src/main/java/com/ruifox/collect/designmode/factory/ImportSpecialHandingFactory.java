package com.ruifox.collect.designmode.factory;

import com.ruifox.collect.designmode.strategy.ImportTypeImpl.ImportSpecialHanding;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.system.ApplicationContextProvider;

import java.util.Map;

public class ImportSpecialHandingFactory {
    
    public static ImportSpecialHanding getSpecialHanding(CollectTask collectTask) {
        // 1.从IOC容器中获取所有该接口的bean
        Map<String, ImportSpecialHanding> importSpecialHandingMap = ApplicationContextProvider.getBeansOfType(ImportSpecialHanding.class);
        for (ImportSpecialHanding importSpecialHanding : importSpecialHandingMap.values()) {
            // 1.2.获取bean标记
            String beanFlag = importSpecialHanding.getHost();
            // 1.3 比对
            if (beanFlag.equals(collectTask.getHost()+"-"+collectTask.getObjectId())) {
                return importSpecialHanding;
            }
        }
        return null;
    }
    
}
