package com.ruifox.collect.designmode.factory;

import com.ruifox.collect.designmode.flyweight.ChromeDriverPool;
import com.ruifox.collect.designmode.flyweight.ManagedChromeDriver;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.system.ConfigUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.remote.RemoteWebDriver;

@Slf4j
@Getter
public class WebDriverFactory {

    public static ChromeDriverPool driverPool;
    private static boolean isInit = false;

    public static String driverInit(int initialSize, String residentPageUrl,CollectTask collectTask) {
        ChromeOptions options = new ChromeOptions();
//        options.addArguments("--headless");
        //firefox->chrome
        //有些医院不关闭的话会导致图片下载失败
        //chrome浏览器配置
        options.addArguments("--blink-settings=imagesEnabled=false");
        options.addArguments("--disable-gpu");
        options.addArguments("--disable-software-rasterizer");

        // 设置 360 安全浏览器的可执行文件路径,配置可复用chrome的
        options.setBinary("C:\\develop\\chrome-win64\\chrome.exe");

        //火狐浏览器配置
//        options.addPreference("permissions.default.image", 2);
//        options.addArguments("--disable-gpu");
//        options.addArguments("--disable-software-rasterizer");

        options.addArguments("--window-size=1920,1080");

//        System.setProperty("webdriver.gecko.driver", ConfigUtil.getProperties("common.geckoDriver"));
        System.setProperty("webdriver.chrome.driver", ConfigUtil.getProperties("common.geckoDriver"));

        //TODO 现在修改了逻辑，driver启动页和常驻标签页都用搜狗来替代
        // NOTE 采用搜狗启动的话，如果遇到需要校验cookie的医院怎么办？？？目前想到的还是进行特殊处理，对需要校 验0cookie的医院启动页用首页
        if(collectTask.getCookieId() != null && collectTask.getCookieId() != 0){
            if (collectTask.getTargetUrl() != null && !collectTask.getTargetUrl().isEmpty()) {
                if (collectTask.getTargetUrl().contains("?")) {
                    residentPageUrl = collectTask.getTargetUrl() + "&Java_Selenium=temp";
                }else {
                    residentPageUrl = collectTask.getTargetUrl() + "?Java_Selenium=temp";
                }
            }
        }


        driverPool = new ChromeDriverPool(residentPageUrl, initialSize, options, collectTask);
        isInit = true;
        
        log.info("Driver池 初始化完成 !");
        return residentPageUrl;
    }
    
    // TODO 重置初始化状态
    public static void resetInit() {
        isInit = false;
    }
    
    // TODO 判断是否初始化
    public static boolean isInit() {
        return isInit;
    }
    
    public static RemoteWebDriver getChromeDriverFromPool(){
        ManagedChromeDriver managedDriver = driverPool.getAvailableDriver();
        return managedDriver.getDriver(); // 注意这里返回的是实际的 FirefoxDriver 实例
    }
    
    // TODO 记得在使用完驱动后调用 release 方法
    public static void releaseDriver(RemoteWebDriver driver) {
        for (ManagedChromeDriver managedDriver : driverPool.getPool()) {
            if (managedDriver.getDriver() == driver) {
                driverPool.releaseDriver(managedDriver);
                break;
            }
        }
    }
    
    /**
     * 获取火狐浏览器对象：解决北京大学深圳医院的js代码识别selenium+webmagic
     * @return 火狐浏览器对象
     */
    private static RemoteWebDriver getFireFoxDriver() {  // NOTE: ZhangXinYu 2024/6/21 | 我改为 private 了。防止用错了，这个是直接new一个
        FirefoxOptions firefoxOptions = new FirefoxOptions();
//        firefoxOptions.addArguments("--headless");
//        firefoxOptions.addPreference("permissions.default.image", 2);
        firefoxOptions.addArguments("--window-size=1920,1080");
        System.setProperty("webdriver.gecko.driver", ConfigUtil.getProperties("common.geckoDriver"));
        RemoteWebDriver firefoxDriver = new FirefoxDriver(firefoxOptions);
        return firefoxDriver;
    }
    
}
