package com.ruifox.collect.designmode.factory;

import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.system.ApplicationContextProvider;

import java.util.Map;

public class WebSpecialHandingFactory {

    public static WebSpecialHanding getSpecialHanding(CollectTask collectTask){
        // 1.从IOC容器中获取所有该接口的bean
        Map<String, WebSpecialHanding> specialHandingMap = ApplicationContextProvider.getBeansOfType(WebSpecialHanding.class);
        for (WebSpecialHanding webSpecialHanding : specialHandingMap.values()) {
            // 1.2.获取bean标记
            String beanFlag = webSpecialHanding.getBeanFlag();
            // 1.3 比对
            if(beanFlag.equals(collectTask.getHost() + "-" + collectTask.getObjectId())){
                return webSpecialHanding;
            }
        }
        return null;
    }
}
