package com.ruifox.collect.designmode.factory;

import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.system.ApplicationContextProvider;

import java.util.Map;

public class ImportTypeFactory {

    public static ImportType getImportType(CollectTask collectTask){
        // 1.从IOC容器中获取所有该接口的bean
        Map<String, ImportType> importTypeMap = ApplicationContextProvider.getBeansOfType(ImportType.class);
        for (ImportType importType : importTypeMap.values()) {
            // 1.2.获取bean标记
            String beanFlag = importType.getBeanFlag();
            // 1.3 比对
            if(beanFlag.equals(String.valueOf(collectTask.getObjectId()))){
                return importType;
            }
        }
        throw new RuntimeException("未获取到导入类型处理类，导入类型ID："+collectTask.getObjectId());
    }
}
