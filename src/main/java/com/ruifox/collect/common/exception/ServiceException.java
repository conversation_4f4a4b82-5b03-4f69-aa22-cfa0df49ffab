package com.ruifox.collect.common.exception;

import com.ruifox.collect.common.errors.BaseErrorInfo;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ServiceException extends RuntimeException {

    private static final long serialVersionUID = 5564446583860234738L;

    private String code;
    private String message;

    // NOTE 自定义异常，配合BaseErrorInfo、FailParse使用
    // NOTE 本来异常处理应该是自定义异常，但由于程序中基本上手动抛出异常都是throw new RuntimeException()，目前没使用自定义异常

    public ServiceException(BaseErrorInfo baseErrorInfo){
        this.code = baseErrorInfo.getCode();
        this.message = baseErrorInfo.getMessage();
    }

}