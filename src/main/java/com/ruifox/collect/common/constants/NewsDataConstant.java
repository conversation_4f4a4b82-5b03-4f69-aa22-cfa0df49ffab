package com.ruifox.collect.common.constants;

public class NewsDataConstant {

    //任务id，共用
    public static final String COLLECT_TASK_ID = "collect_task_id";
    //栏目名称，共用
    public static final String CLASSIFICATION = "classification";
    //采集页面的地址，共用
    public static final String TARGET_URL = "target_url";
    //每一页的id，共用
    public static final String PAGE_ID = "page_id";
    //数据的排序，共用
    public static final String SORT = "sort";
    //标题
    public static final String TITLE = "title";
    //标题、缩略图
    public static final String THUMB = "thumb";
    //摘要
    public static final String DESCRIPTION = "description";
    //发布时间
    public static final String PUBLISH_TIME = "publish_time";
    //来源
    public static final String COME_FROM = "come_from";
    //浏览量
    public static final String VIEWS = "views";
    //作者
    public static final String AUTHOR = "author";
    //正文
    public static final String CONTENT = "content";
    //信息状态码，共用，0
    public static final String FLAG = "flag";
    //页面源码，共用
    public static final String ORIGIN_CODE = "origin_code";

}
