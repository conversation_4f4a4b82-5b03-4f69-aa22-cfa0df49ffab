package com.ruifox.collect.common.constants;

public class BusinessConstant {

    public static final String EXCLUDE_PARSE_FLAG = "";
    public static final String SELF_ELEMENT = "0";
    public static final String EmptyPage = "<html><head></head><body></body></html>";
    public static final String FileErrorExtension = ".unknown";

    // 文件下载结果标识（200：成功，500：失败)
    // NOTE 目前其实没有对下载失败的记录（file表不会出现500），目前下载失败是在采集错误记录表里
    public static final int FileSuccess = 200;
    public static final int FileError = 500;

    // 元数据（新闻、医生等采集对象的相同字段名
    public static final String COLLECT_TASK_ID = "collect_task_id";
    public static final String CLASSIFICATION = "classification";
    public static final String TARGET_URL = "target_url";
    public static final String PAGE_ID = "page_id";
    public static final String SORT = "sort";
    public static final String FLAG = "flag";
    public static final String ORIGIN_CODE = "origin_code";

    // 采集类型
    public static final int NORMAL = 1;
    public static final int ASYNCHRONOUS = 2;
    public static final int SINGLE = 3;

}
