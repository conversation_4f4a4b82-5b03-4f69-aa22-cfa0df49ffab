package com.ruifox.collect.common.constants;

import java.util.List;

public class DoctorDataConstant {

    //姓名
    public static final String TITLE = "title";
    //头像
    public static final String THUMB = "thumb";
    //所属科室
    public static final String DEPART = "depart";
    //医师职称
    public static final String DOC_POSITION = "doc_position";
    //教务职称
    public static final String EDU_POSITION = "edu_position";
    //教学岗位
    public static final String EDU_POST = "edu_post";
    //院内职务
    public static final String HOS_POSITION = "hos_position";
    //专家级别
    public static final String LEVEL = "level";
    //擅长领域
    public static final String GOODAT = "goodat";
    //医生简介
    public static final String CONTENT = "content";

    // 职称等字段
    public static final List<String> docPositionList =
            List.of(
                    "主任医师", "副主任医师", "主治医师", "医师",
                    "主任护师", "副主任护师", "主管护师", "护师", "护士",
                    "主任药师", "副主任药师", "主管药师", "药师", "药士",
                    "主任检验师", "副主任检验师", "主管检验师", "检验师", "检验士");
    public static final List<String> eduPositionList =
            List.of(
                    "教授", "副教授", "研究员", "副研究员",
                    "讲师", "助教", "助理研究员", "研究实习员");
    public static final List<String> eduPostList =
            List.of(
                    "博士生导师", "硕士研究生导师");
    public static final List<String> hosPositionList =
            List.of(
                    "科主任", "科副主任");
    public static final List<String> levelList =
            List.of(
                    "一级专家", "二级专家");

}
