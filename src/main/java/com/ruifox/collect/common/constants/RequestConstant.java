package com.ruifox.collect.common.constants;

public class RequestConstant {

    public static final String COLLECT_TASK_ID = "collect_task_id";
    public static final String SORT = "sort";
    public static final String PAGE_ID = "page_id";

    // 采集类型
    public static final String SITUATION = "situation";
    public static final String SITUATION_INIT = "init";
    public static final String SITUATION_ASYNCHRONOUS = "asynchronous";
    public static final String SITUATION_PAGE = "page";
    public static final String SITUATION_DETAIL = "detail";
    public static final String SITUATION_ERROR = "ERROR";

    // 内链外链类型
    public static final String LINK_TYPE = "link_type";
    public static final Integer LINK_TYPE_EMBEDDED = 2;
    public static final Integer LINK_TYPE_INNER_LINK = 1;
    public static final Integer LINK_TYPE_OUTER_LINK = 0;

    // 预处理信息
    public static final String PRE_DATE = "pre_data";

    // 特殊的页面
    public static final String SPECIAL_PAGE = "special_page";
    public static final String SPECIAL_DETAIL = "1";
    public static final String SPECIAL_LIST = "2";
    public static final String SPECIAL_LIST_AND_IFRAME = "3";
    public static final String SPECIAL_LIST_DETAIL = "4";
    public static final String SPECIAL_DETAIL_DOCUMENT = "5";
    public static final String NOT_SPECIAL = "0";

}
