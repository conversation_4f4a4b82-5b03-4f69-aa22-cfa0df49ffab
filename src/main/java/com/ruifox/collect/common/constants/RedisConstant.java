package com.ruifox.collect.common.constants;

public class RedisConstant {

    // 采集基本相关信息
    public static final String COLLECT_TASK = "collect_task_";
    public static final String TASK_TYPE = "task_type_";
    public static final String TASK_OBJECT = "task_object_";
    public static final String TASK_TEMPLATE = "task_template_";
    public static final String TASK_COOKIE= "task_cookie_";


    // 数据信息、文件信息、源码、文件下载失败信息
    public static final String DATA_ITEMS = "data_items_";
    public static final String FILE_ITEMS = "file_items_";
    public static final String PAGE_SOURCE = "page_source_";
    public static final String FILE_FAIL = "file_fail_";

    // 文件下载失败信息表名
    public static final String FILE_FAIL_TABLE = "task_file_failed";



    // HMS数据
    public static final String MODEL_FIELD_POSITION_MAPS = "model_field_position_maps"; // 例如医生：[{"key":"1","value":"主治医生"}, ....]

    // excel数据保存路径
    public static final String FILE_PATH = "file_path";
    public static final String OTHER_FILE_PATH = "other_file_path";

}
