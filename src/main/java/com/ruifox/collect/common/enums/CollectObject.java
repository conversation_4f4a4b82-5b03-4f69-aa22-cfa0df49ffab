package com.ruifox.collect.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum CollectObject {
    NEWS(1, "新闻"),
    DOCTOR(2, "医生");

    @EnumValue
    private final int code;
    @JsonValue
    private final String name;

    CollectObject(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CollectObject fromCode(int code){
        for (CollectObject status : values()) {
            if(code == status.code)
                return status;
        }
        return null;
    }
}
