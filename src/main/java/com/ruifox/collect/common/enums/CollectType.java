package com.ruifox.collect.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum CollectType {
    NORMAL(1, "正常采集"),
    SINGLE(2, "单页采集"),
    ASYNCHRONOUS(3,"异步请求采集");

    @EnumValue
    private final int code;
    @JsonValue
    private final String name;

    CollectType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CollectType fromCode(int code){
        for (CollectType status : values()) {
            if(code == status.code)
                return status;
        }
        return null;
    }
}
