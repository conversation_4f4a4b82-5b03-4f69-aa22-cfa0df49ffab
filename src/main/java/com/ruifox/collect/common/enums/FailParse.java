package com.ruifox.collect.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum FailParse {
    CHECK(1,"采集任务预检失败"),
    PAGE(2,"列表页解析失败"),
    DETAIL(3,"详情页解析失败"),
    IMPORT(4, "单任务导入失败"),
    NULL(-1, "未知的异常");

    @EnumValue
    private final int code;
    @JsonValue
    private final String msg;

    FailParse(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String fromCode(int code){
        for (FailParse failParse : values()) {
            if(code == failParse.code)
                return failParse.msg;
        }
        return "未知的异常";
    }

    public static int fromMsg(String msg){
        for (FailParse failParse : values()) {
            if(msg.equals(failParse.msg))
                return failParse.code;
        }
        return -1;
    }

}
