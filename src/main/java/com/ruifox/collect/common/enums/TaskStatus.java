package com.ruifox.collect.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum TaskStatus {
    WAITING(1, "未开始"),
    RUNNING(2, "采集中"),
    PAUSE(3,"暂停"),
    SUCCESS(4,"成功"),
    FAIL(5,"失败");

    @EnumValue
    private final int code;
    @JsonValue
    private final String msg;

    TaskStatus(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static TaskStatus getTaskStatus(int code){
        for (TaskStatus status : values()) {
            if(code == status.code)
                return status;
        }
        return null;
    }

}
