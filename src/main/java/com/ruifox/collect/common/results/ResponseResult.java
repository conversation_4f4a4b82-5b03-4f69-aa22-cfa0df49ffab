package com.ruifox.collect.common.results;

import com.ruifox.collect.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult<T> {

    private Integer code;
    private Boolean success;
    private String message;
    private T data;

    public static final Integer success_code = 20000;
    public static final Integer fail_code = 50000;

    public static <T> ResponseResult<?> success(String message, T data){
        return new ResponseResult<>(success_code,true,message,data);
    }

    public static <T> ResponseResult<?> success(String message){
        return new ResponseResult<>(success_code,true, message,null);
    }

    public static <T> ResponseResult<?> success(T data){
        return new ResponseResult<>(success_code,true,null,data);
    }

    public static <T> ResponseResult<?> fail(ServiceException e){
        return new ResponseResult<>(fail_code,false,e.getMessage(),null);
    }

    public static <T> ResponseResult<?> fail(RuntimeException e){
        return new ResponseResult<>(fail_code,false,e.getMessage(),null);
    }

    public static <T> ResponseResult<?> fail(String message){
        return new ResponseResult<>(fail_code,false,message,null);
    }

    public static <T> ResponseResult<?> tokenFail(ServiceException e){
        return new ResponseResult<>(50008,false,e.getMessage(),null);
    }

}