package com.ruifox.collect.controller;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.module.dto.TemplateDTO;
import com.ruifox.collect.service.DownLoadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "文件下载模块")
@RequestMapping("/download")
public class DownLoadController {
    @Resource
    private DownLoadService downLoadService;

    @ApiOperation("二次下载文件")
    @GetMapping("/downloadAgain/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> downloadAgain(@PathVariable("collectTaskId") Integer collectTaskId){
        return downLoadService.downloadFileAgain(collectTaskId);
    }
}
