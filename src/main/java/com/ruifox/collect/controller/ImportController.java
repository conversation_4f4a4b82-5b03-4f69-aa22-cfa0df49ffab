package com.ruifox.collect.controller;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.service.ImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "数据导入模块")
@RequestMapping("/import")
public class ImportController {

    @Resource
    private ImportService importService;

    @ApiOperation("单任务导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "xxx", required = true)
    })
    @GetMapping("/singleImport")
    public ResponseResult<?> singleImport(
            @RequestParam("collectTaskId") Integer collectTaskId
    ) {
        return importService.singleImport(collectTaskId);
    }

    @ApiOperation("多任务导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskIds", value = "多个采集任务ID", example = "[1,2,3,....]", required = true)
    })
    @PostMapping("/multiImport")
    public ResponseResult<?> multiImport(
            @RequestBody List<Integer> collectTaskIds
    ) {
        return importService.multiImport(collectTaskIds);
    }

    @ApiOperation("连续任务导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startCollectTaskId", value = "起始采集任务ID", required = true),
            @ApiImplicitParam(name = "endCollectTaskId", value = "结束采集任务ID", required = true)
    })
    @GetMapping("/continuousImport")
    public ResponseResult<?> continuousImport(
            @RequestParam("startCollectTaskId") Integer startCollectTaskId,
            @RequestParam("endCollectTaskId") Integer endCollectTaskId
    ) {
        return importService.continuousImport(startCollectTaskId,endCollectTaskId);
    }

    @ApiOperation("java库任务导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "xxx", required = true)
    })
    @GetMapping("/javaImport")
    public ResponseResult<?> javaImport(
            @RequestParam("collectTaskId") Integer collectTaskId
    ) {
        return importService.javaImport(collectTaskId);
    }

    @ApiOperation("Java库连续任务导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startCollectTaskId", value = "起始采集任务ID", required = true),
            @ApiImplicitParam(name = "endCollectTaskId", value = "结束采集任务ID", required = true)
    })
    @GetMapping("/continuousJavaImport")
    public ResponseResult<?> continuousJavaImport(
            @RequestParam("startCollectTaskId") Integer startCollectTaskId,
            @RequestParam("endCollectTaskId") Integer endCollectTaskId
    ) {
        return importService.continuousJavaImport(startCollectTaskId,endCollectTaskId);
    }

}
