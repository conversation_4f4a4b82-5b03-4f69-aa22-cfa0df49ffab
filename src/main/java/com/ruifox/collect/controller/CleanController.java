package com.ruifox.collect.controller;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.service.CleanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "数据清洗模块")
@RequestMapping("/clean")
public class CleanController {
    @Resource
    private CleanService cleanService;

    @ApiOperation("清洗新闻数据")
    @GetMapping("/cleanNews/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanNews(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanNews(collectTaskId);
    }

    @ApiOperation("清洗医生数据")
    @GetMapping("/cleanDoctor/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanDoctor(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanDoctor(collectTaskId);
    }

    @ApiOperation("清洗图片数据")
    @GetMapping("/cleanImage/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanImage(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanImage(collectTaskId);
    }

    @ApiOperation("清洗视频数据")
    @GetMapping("/cleanVideo/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanVideo(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanVideo(collectTaskId);
    }

    @ApiOperation("清洗领导数据")
    @GetMapping("/cleanLeader/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanLeader(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanLeader(collectTaskId);
    }

    @ApiOperation("清洗院报数据")
    @GetMapping("/cleanNewspaper/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanNewspaper(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanNewspaper(collectTaskId);
    }

    @ApiOperation("清洗Java库新闻数据")
    @GetMapping("/cleanNewsJava/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanNewsJava(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanNewsJava(collectTaskId);
    }

    @ApiOperation("清洗Java库医生数据")
    @GetMapping("/cleanDoctorJava/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanDoctorJava(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanDoctorJava(collectTaskId);
    }

    @ApiOperation("清洗Java库图片数据")
    @GetMapping("/cleanImageJava/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanImageJava(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanImageJava(collectTaskId);
    }

    @ApiOperation("清洗Java库视频数据")
    @GetMapping("/cleanVideoJava/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanVideoJava(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanVideoJava(collectTaskId);
    }

    @ApiOperation("清洗Java库领导数据")
    @GetMapping("/cleanLeaderJava/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanLeaderJava(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanLeaderJava(collectTaskId);
    }

    @ApiOperation("清洗Java库院报数据")
    @GetMapping("/cleanNewspaperJava/{collectTaskId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> cleanNewspaperJava(@PathVariable("collectTaskId") Integer collectTaskId){
        return cleanService.cleanNewspaperJava(collectTaskId);
    }
}
