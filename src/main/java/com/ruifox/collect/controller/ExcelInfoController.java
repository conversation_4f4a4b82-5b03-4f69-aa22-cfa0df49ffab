package com.ruifox.collect.controller;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.service.CleanService;
import com.ruifox.collect.service.ExcelInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "数据导出Excel模块")
@RequestMapping("/excel")
public class ExcelInfoController {
    @Resource
    private ExcelInfoService excelInfoService;

    @ApiOperation("导出采集数据")
    @GetMapping("/importExcel")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    public ResponseResult<?> importExcelInfo(){
        return excelInfoService.importExcelInfo();
    }
}
