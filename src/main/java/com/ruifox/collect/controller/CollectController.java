package com.ruifox.collect.controller;

import com.ruifox.collect.module.dto.TemplateDTO;
import com.ruifox.collect.service.CollectTaskService;
import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.module.dto.CollectTaskDTO;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "数据采集模块")
@RequestMapping("/collect")
public class CollectController {

    @Resource
    private CollectTaskService collectTaskService;

    @ApiOperation("创建任务模板")
    @PostMapping("/createTaskTemplate")
    public ResponseResult<?> createTaskTemplate(@RequestBody TemplateDTO templateDTO) {
        return collectTaskService.createTaskTemplate(templateDTO);
    }

    @ApiOperation("创建采集任务")
    @PostMapping("/createCollectTask")
    public ResponseResult<?> createCollectTask(@RequestBody CollectTaskDTO collectTaskDTO) {
        return collectTaskService.createCollectTask(collectTaskDTO);
    }

    @ApiOperation("启动采集任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    @GetMapping("/startCollectTask/{collectTaskId}")
    public ResponseResult<?> startCollectTask(@PathVariable("collectTaskId") Integer collectTaskId) {
        return collectTaskService.startCollectTask(collectTaskId);
    }

    @ApiOperation("启动连续采集任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginCollectTaskId", value = "开始采集任务ID", example = "999", required = true),
            @ApiImplicitParam(name = "endCollectTaskId", value = "结束采集任务ID", example = "999", required = true),
    })
    @GetMapping("/startContinuousCollectTask/{beginCollectTaskId}/{endCollectTaskId}")
    public ResponseResult<?> startContinuousCollectTask(@PathVariable("beginCollectTaskId") Integer beginCollectTaskId, @PathVariable("endCollectTaskId") Integer endCollectTaskId) {
        return collectTaskService.startContinuousCollectTask(beginCollectTaskId, endCollectTaskId);
    }

    @ApiOperation("手动落库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectTaskId", value = "采集任务ID", example = "999", required = true)
    })
    @GetMapping("/manualPersistence/{collectTaskId}")
    public ResponseResult<?> manualPersistence(@PathVariable("collectTaskId") Integer collectTaskId) {
        return collectTaskService.manualPersistence(collectTaskId);
    }
}
