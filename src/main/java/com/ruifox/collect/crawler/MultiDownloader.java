package com.ruifox.collect.crawler;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Page;
import us.codecraft.webmagic.Request;
import us.codecraft.webmagic.Task;
import us.codecraft.webmagic.downloader.HttpClientDownloader;

import java.util.List;

@Component
@Slf4j
@Getter
@Setter
public class MultiDownloader extends HttpClientDownloader {

    private List<MyDownloader> downloaderList;
    private int currentIndex = 0;

    /**
     * 请求分发: webmagic中的多线程会源源不断的从请求队列中拿到请求
     *      此处download方法会同时被多个线程执行,通过轮询策略将任务分发至下级请求处理器
     */
    @Override
    public Page download(Request request, Task task) {
        MyDownloader downloader;
        synchronized(this){
            int size = downloaderList.size();
            if (currentIndex >= size - 1) {
                currentIndex = 0;
            }
            downloader = downloaderList.get(currentIndex);
            currentIndex++;
        }
        return downloader.download(request, task);
    }
}
