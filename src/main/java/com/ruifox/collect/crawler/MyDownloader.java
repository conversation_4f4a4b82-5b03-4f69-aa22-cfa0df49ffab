package com.ruifox.collect.crawler;

import com.ruifox.collect.common.constants.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.remote.RemoteWebDriver;
import us.codecraft.webmagic.Page;
import us.codecraft.webmagic.Request;
import us.codecraft.webmagic.Task;
import us.codecraft.webmagic.downloader.Downloader;
import us.codecraft.webmagic.selector.PlainText;

import java.util.concurrent.TimeUnit;

@Data
@Slf4j
public class MyDownloader implements Downloader {

    private final RemoteWebDriver driver;

    @Override
    public Page download(Request request, Task task) {
        switch ((String) request.getExtra(RequestConstant.SITUATION)) {

            // 1.快速翻页（获取所有列表页URL
            case RequestConstant.SITUATION_INIT:
                return init(request);

            // 2.列表页解析（下载源码
            case RequestConstant.SITUATION_PAGE:
                return page(request);

            // 3.详情页解析（内链页和微信页复用列表方法
            case RequestConstant.SITUATION_DETAIL:
                return detail(request);

            // 4.针对异步请求的处理（翻页的同时解析列表页
            case RequestConstant.SITUATION_ASYNCHRONOUS:
                return init(request);

            default:
                return null;
        }
    }


    /**
     * 初始翻页（根据类型判断快速翻页还是异步请求翻页
     *
     * @param request 初始请求，地址为首页
     * @return page，携带所有列表页的URL入队或者异步请求携带所有详情URL入队
     */
    private Page init(Request request) {
        synchronized (driver) {
            // 标记当前request已被处理
            Page page = new Page();
            page.setRequest(request);
            try {
                // 快速翻页
                CrawlerManager.multiPageDispose(driver, page, request.getExtra(RequestConstant.SITUATION));
            } catch (Exception e) {
                CrawlerManager.taskFailParseRecord(request.getExtra(RequestConstant.COLLECT_TASK_ID), request.getUrl(), "初始翻页失败", e.getMessage());
                page.getRequest().putExtra(RequestConstant.SITUATION, RequestConstant.SITUATION_ERROR);
            }
            return page;
        }
    }


    /**
     * 正常采集--》页面源码下载解析
     *
     * @param request 请求
     * @return 该请求的page，携带源码
     */
    private Page page(Request request) {
        synchronized (driver) {
            Sleeper.sleep(6, TimeUnit.SECONDS);
            Page page = new Page();
            page.setRequest(request);
            CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
            try {
                // 1.获取源码
                // NOTE 目前对源码的下载有两种方案，1：采用driver打开页面获取；2：直接发送http请求获取。
                // NOTE 目前是首先用driver下载，获取失败的话再用http请求的方式下载
                // NOTE 采用方案1是为了使用JS操作driver直接将源码中的所有相对路径变为绝对路径。（遇到过获取源码失败的情况，莫名其妙报错
                // NOTE 如果采用方案2则需要在后续操作时手动将相对转为绝对路径，并且还需要注意字符编码的问题（测试时遇到过一次返回的中文字符乱码
                CrawlerManager.PageSourceResult pageSourceANDurl = CrawlerManager.getPageSourceFromDriver(driver, request.getUrl(), collectTask);

                String pageSource = pageSourceANDurl.getPageSource();
                String finalUrl = pageSourceANDurl.getFinalUrl();
                // 2.放入page
                page.setRawText(pageSource);
                page.setUrl(new PlainText(finalUrl));
                page.isDownloadSuccess();

                //如果是翻页不变url，且页数少时，可以通过直接下载对应页的内容，直接列表页解析
                if(request.getExtra("RawText")!=null)
                    page.setRawText(request.getExtra("RawText"));

                request.setUrl(finalUrl);
                request.putExtra("driver",driver);
                if(!finalUrl.contains("scmy120"))
                    request.putExtra(RequestConstant.LINK_TYPE,RequestConstant.LINK_TYPE_OUTER_LINK);


                // 3.url与其源码进行对应,存入redis（兜底操作)
                RedisUtil.getInstance().hPut(RedisConstant.PAGE_SOURCE + collectTask.getId(), request.getUrl(), pageSource);

            } catch (Exception e) {
                CrawlerManager.taskFailParseRecord(collectTask.getId(), request.getUrl(), "源码下载失败", e.getMessage());
                page.getRequest().putExtra(RequestConstant.SITUATION, RequestConstant.SITUATION_ERROR);
            }
            return page;
        }
    }


    /**
     * 详情页解析（内链页下载源码，存入redis
     *
     * @param request 该详情页请求
     * @return 该详情页请求的page
     */
    private Page detail(Request request) {
        // NOTE 目前对详情页的处理是内链需要解析正文，微信外链解析标题、时间、来源、摘要、封面，其余外链则不处理
        if (request.getExtra(RequestConstant.LINK_TYPE) == RequestConstant.LINK_TYPE_INNER_LINK
                || (request.getExtra(RequestConstant.LINK_TYPE) == RequestConstant.LINK_TYPE_OUTER_LINK && request.getUrl().startsWith("https://mp.weixin.qq.com/"))) {
            //如果是点击医生图片展示详细信息且url不变的，单页特殊处理
            if (request.getExtra(RequestConstant.SPECIAL_PAGE) != null && request.getExtra(RequestConstant.SPECIAL_PAGE).equals(RequestConstant.SPECIAL_DETAIL)) {
                synchronized (driver) {
                    try {
                        return CrawlerManager.detailDockerSpecial(driver, request);
                    } catch (Exception e) {
                        CrawlerManager.taskFailParseRecord(request.getExtra(RequestConstant.COLLECT_TASK_ID), request.getUrl(), "数据处理失败", e.getMessage());
                    }
                }
            }
            //列表项点击元素是js变化，即无详情页url,且点击下一页按钮url也不发生变化，就进行特殊的列表页单页处理
            else if (request.getExtra(RequestConstant.SPECIAL_PAGE) != null && request.getExtra(RequestConstant.SPECIAL_PAGE).equals(RequestConstant.SPECIAL_LIST)) {
                synchronized (driver) {
                    try {
                        return CrawlerManager.listNewsSpecial(driver, request);
                    } catch (Exception e) {
                        CrawlerManager.taskFailParseRecord(request.getExtra(RequestConstant.COLLECT_TASK_ID), request.getUrl(), "数据处理失败", e.getMessage());
                    }
                }
            }
            else if (request.getExtra(RequestConstant.SPECIAL_PAGE) != null && request.getExtra(RequestConstant.SPECIAL_PAGE).equals(RequestConstant.SPECIAL_LIST_AND_IFRAME)) {
                synchronized (driver) {
                    try {
                        return CrawlerManager.listNewsSpecialAndIframe(driver, request);
                    } catch (Exception e) {
                        CrawlerManager.taskFailParseRecord(request.getExtra(RequestConstant.COLLECT_TASK_ID), request.getUrl(), "数据处理失败", e.getMessage());
                    }
                }
            }
            else if (request.getExtra(RequestConstant.SPECIAL_PAGE) != null && request.getExtra(RequestConstant.SPECIAL_PAGE).equals(RequestConstant.SPECIAL_LIST_DETAIL)) {
                synchronized (driver) {
                    try {
                        return CrawlerManager.listDoctorsSpecial(driver, request);
                    } catch (Exception e) {
                        CrawlerManager.taskFailParseRecord(request.getExtra(RequestConstant.COLLECT_TASK_ID), request.getUrl(), "数据处理失败", e.getMessage());
                    }
                }
            }
            else if (request.getExtra(RequestConstant.SPECIAL_PAGE) != null && request.getExtra(RequestConstant.SPECIAL_PAGE).equals(RequestConstant.SPECIAL_DETAIL_DOCUMENT)) {
                synchronized (driver) {
                    try {
                        return CrawlerManager.helpDocumentSpecial(driver, request);
                    } catch (Exception e) {
                        CrawlerManager.taskFailParseRecord(request.getExtra(RequestConstant.COLLECT_TASK_ID), request.getUrl(), "数据处理失败", e.getMessage());
                    }
                }
            }
            return page(request);
        } else {
            Page page = new Page();
            page.setRequest(request);
            return page;
        }
    }


    @Override
    public void setThread(int threadNum) {

    }
}
