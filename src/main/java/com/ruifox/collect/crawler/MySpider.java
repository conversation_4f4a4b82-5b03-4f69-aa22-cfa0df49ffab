package com.ruifox.collect.crawler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.common.constants.BusinessConstant;
import com.ruifox.collect.common.constants.ConfigConstant;
import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.common.enums.TaskStatus;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskDataMapper;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.designmode.factory.WebDriverFactory;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskData;
import com.ruifox.collect.module.entity.TaskType;
import com.ruifox.collect.service.impl.CollectTaskServiceImpl;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.util.*;
import com.ruifox.collect.designmode.singeton.CollectTaskQueue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;
import us.codecraft.webmagic.Request;
import us.codecraft.webmagic.Spider;
import us.codecraft.webmagic.proxy.Proxy;
import us.codecraft.webmagic.proxy.SimpleProxyProvider;
import us.codecraft.webmagic.scheduler.RedisScheduler;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Getter
@Slf4j
public class MySpider {

    private Spider spider;
    // TODO 用于判断是否已经创建了线程，保证爬虫监控只创建一次
    private static final AtomicBoolean isThreadCreated = new AtomicBoolean(false);

    /**
     * webmagic爬虫启动
     */
    public void doCrawler(CollectTask collectTask) {
        try {
            // 1.初始化driver、初始化任务分发器
            // FIXME 分配driver常驻于downloaderList，实际上每个downloader仅仅用得上一个浏览器，还是可以用完就能关那种
            // FIXME 可以考虑改为需要时再从driver池中获取，使用后就返回，后续修改
            int browserCount = Integer.parseInt(ConfigUtil.getProperties(ConfigConstant.BROWSER_COUNT));
            String residentPageUrl = ConfigUtil.getProperties(ConfigConstant.RESIDENT_URL);
            residentPageUrl = WebDriverFactory.driverInit(browserCount, residentPageUrl, collectTask);
            List<MyDownloader> downloaderList = new ArrayList<>(browserCount);
            for (int i = 0; i <browserCount; i++) {
                RemoteWebDriver driver = WebDriverFactory.getChromeDriverFromPool();
                downloaderList.add(new MyDownloader(driver));
            }
            ApplicationContextProvider.getBeanByType(MultiDownloader.class).setDownloaderList(downloaderList);

            // 2.组件获取
            MyProcessor processor = ApplicationContextProvider.getBeanByType(MyProcessor.class);
            MultiDownloader downloader = ApplicationContextProvider.getBeanByType(MultiDownloader.class);
            MyPipeline pipeline = ApplicationContextProvider.getBeanByType(MyPipeline.class);
            //redis的队列
            JedisPool jedisPool = ApplicationContextProvider.getBeanByType(JedisPool.class);
            RedisScheduler scheduler = new RedisScheduler(jedisPool);
            int threadCount = Integer.parseInt(ConfigUtil.getProperties(ConfigConstant.THREAD_COUNT));
            //代理服务器
            downloader.setProxyProvider(
                    SimpleProxyProvider.from(new Proxy(
                            ConfigUtil.getProperties(ConfigConstant.PROXY_IP),
                            Integer.parseInt(ConfigUtil.getProperties(ConfigConstant.PROXY_PORT))
                    ))
            );

            // 3.起步请求
            Request request = new Request(residentPageUrl);
            request.putExtra(RequestConstant.COLLECT_TASK_ID, collectTask.getId());
            request.putExtra(RequestConstant.SORT, collectTask.getSort());

            TaskType taskType = RedisGetManager.getTaskType(collectTask);
            switch (taskType.getCode()) {
                // 3.1 正常采集
                case BusinessConstant.NORMAL:
                    request.putExtra(RequestConstant.SITUATION, RequestConstant.SITUATION_INIT);
                    //是否为特殊的列表页处理
                    //request.putExtra(RequestConstant.SPECIAL_PAGE,RequestConstant.SPECIAL_LIST);
                    break;
                // 3.2 异步请求页采集
                case BusinessConstant.ASYNCHRONOUS:
                    request.putExtra(RequestConstant.SITUATION, RequestConstant.SITUATION_ASYNCHRONOUS);
                    break;
                // 3.3 单详情页采集
                case BusinessConstant.SINGLE:
                    request.putExtra(RequestConstant.SITUATION, RequestConstant.SITUATION_DETAIL);
                    request.putExtra(RequestConstant.LINK_TYPE, RequestConstant.LINK_TYPE_INNER_LINK);
                    //是否为特殊的页面处理
                    request.putExtra(RequestConstant.SPECIAL_PAGE, collectTask.getSpecialPage());
                    request.setUrl(collectTask.getTargetUrl());
                    break;
            }

            // 4.组件设置
            this.spider = Spider.create(processor)
                    .setUUID(String.valueOf(collectTask.getId()))
                    .addPipeline(pipeline)
                    .setDownloader(downloader)
                    .setScheduler(scheduler)
                    .addRequest(request)
                    .thread(threadCount);
            // 队列为空时的等待时间
           spider.setEmptySleepTime(1000 * 60);

            // 5.爬虫监控
            if (isThreadCreated.compareAndSet(false, true)) {
                new Thread(() -> {
                    while (true) {
                        Sleeper.sleep(6, TimeUnit.SECONDS);

                        System.err.println("==============================");
                        System.out.println("当前爬虫状态: " + spider.getStatus().name());
                        System.out.println("当前队列状态: " + scheduler.getLeftRequestsCount(spider));
                        System.out.println("当前基本数据: " + RedisUtil.getInstance().lLen(RedisConstant.DATA_ITEMS+CollectTaskQueue.getInstance().getTaskId()));
                        System.out.println("当前文件数据: " + RedisUtil.getInstance().lLen(RedisConstant.FILE_ITEMS+CollectTaskQueue.getInstance().getTaskId()));

                        ThreadPoolExecutor threadPool = FileDataManager.getThreadPool();
                        System.out.println("当前文件线程池状态 --> executor.getActiveCount() ：" + threadPool.getActiveCount());
                        System.out.println("当前文件线程池状态 --> executor.getQueue().size() ：" + threadPool.getQueue().size());
                        System.out.println("当前文件线程池状态 --> executor.getPoolSize() ：" + threadPool.getPoolSize());

                        System.out.println("当前任务队列 --> " + CollectTaskQueue.getInstance().getAll());
                        System.out.println("当前任务状态 --> " + CollectTaskQueue.getInstance().getTaskId() + " - " + CollectTaskQueue.getInstance().getTaskState());
                        System.err.println("==============================");
                    }
                },"spiderMonitor").start();
            }

            // 6.爬虫运行（同步启动）
           try {
               spider.run();
           } catch (Exception e){
               // NOTE 目前爬虫运行过程中出现异常处理是等待10分钟，然后继续执行剩余采集任务
               CollectTaskQueue.getInstance().setTaskState("爬虫运行异常...");
               CrawlerManager.taskFailParseRecord(collectTask.getId(), collectTask.getTargetUrl(), "爬虫运行异常", e.getMessage());
               // 关闭浏览器
               closeDriver();
               Sleeper.sleep(10, TimeUnit.MINUTES);
               // 启动下一个采集任务
               startNext();
               return;
           }

            System.out.println("end！！！采集任务执行完成，ID："+collectTask.getId());
            // 7.数据落库
            log.info("正在同步数据至数据库...");
            try {
                TransactionUtil.executeInTransaction(()->{
                    CrawlerManager.persistenceDatas(collectTask);
                    return null;
                });
                // 8.修改任务状态
                collectTask.setTaskStatus(TaskStatus.SUCCESS.getCode());
                ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).updateById(collectTask);
                log.info("同步成功,任务状态已更新!");
                CrawlerManager.taskFailParseRecord(collectTask.getId(), collectTask.getTargetUrl(), "数据采集完成！！！",
                        "数据量："+RedisUtil.getInstance().lLen(RedisConstant.DATA_ITEMS+collectTask.getId())
                                +"，文件量："+RedisUtil.getInstance().lLen(RedisConstant.FILE_ITEMS+collectTask.getId()));

                // 9.该任务最后获取数据量以及下载成功文件量以及失败文件量存入数据表中
                TaskData taskData = new TaskData();
                taskData.setCollectTaskId(collectTask.getId());
                Long dataSize = RedisUtil.getInstance().lLen(RedisConstant.DATA_ITEMS + collectTask.getId());
                taskData.setDataSize(dataSize);
                Long fileSuccessSize = RedisUtil.getInstance().lLen(RedisConstant.FILE_ITEMS + collectTask.getId());
                taskData.setFileSuccessSize(fileSuccessSize);
                Long fileFailSize = RedisUtil.getInstance().lLen(RedisConstant.FILE_FAIL + collectTask.getId());
                taskData.setFileFailedSize(fileFailSize);
                // 若之前这个任务采集过一次有存在数据，将其删除后再次添加最新的,得是全采才行,不然就新增一条
                if (collectTask.getPageSum()==0) {
                    LambdaQueryWrapper<TaskData> taskDataWrapper = new LambdaQueryWrapper();
                    taskDataWrapper.eq(TaskData::getCollectTaskId, collectTask.getId());
                    TaskDataMapper taskDataMapper = ApplicationContextProvider.getBeanByType(TaskDataMapper.class);
                    taskDataMapper.delete(taskDataWrapper);
                }
                ApplicationContextProvider.getBeanByType(TaskDataMapper.class).insert(taskData);


                RedisGetManager.deleteRedisById(collectTask.getId());
                CollectTaskQueue.getInstance().setTaskState("落库成功，即将执行下一个任务...");

                //TODO 添加对数据落库失败的处理
                //NOTE 目前的处理思路：保留该采集任务的数据在redis，错误记录，继续执行下一个任务
            } catch (Exception e) {
                log.info("数据落库失败!");
                collectTask.setTaskStatus(TaskStatus.FAIL.getCode());
                ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).updateById(collectTask);
                CrawlerManager.taskFailParseRecord(collectTask.getId(), collectTask.getTargetUrl(), "数据落库失败", e.getMessage());
                CollectTaskQueue.getInstance().setTaskState("数据落库失败...");

            } finally {
                closeDriver();
                startNext();
            }
        } catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTask.getId(), collectTask.getTargetUrl(), "爬虫运行出现异常", e.getMessage());
        }
    }

    private static void closeDriver() {
        log.info("正在关闭所有资源...");

        WebDriverFactory.driverPool.getPool().forEach(managedFirefoxDriver -> managedFirefoxDriver.getDriver().quit());

        WebDriverFactory.resetInit();

        log.info("所有资源关闭成功!");
    }

    private static void startNext() {
        Integer collectTaskId = CollectTaskQueue.getInstance().pollTask();
        if (collectTaskId != null) {
            log.info("当前有任务需要执行,ID: " + collectTaskId);
            CollectTaskQueue.getInstance().setTaskId(-1);
            CollectTaskQueue.getInstance().setTaskState("即将执行下一个任务");
            ApplicationContextProvider.getBeanByType(CollectTaskServiceImpl.class).startCollectTask(collectTaskId);

        } else {
            log.info("当前没有任务需要执行");
            CollectTaskQueue.getInstance().setTaskId(-1);
            CollectTaskQueue.getInstance().setTaskState("当前没有任务在执行");
        }
    }
}
