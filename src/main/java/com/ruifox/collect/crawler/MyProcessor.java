package com.ruifox.collect.crawler;

import com.ruifox.collect.common.constants.BusinessConstant;
import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.util.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import us.codecraft.webmagic.Page;
import us.codecraft.webmagic.Request;
import us.codecraft.webmagic.Site;
import us.codecraft.webmagic.processor.PageProcessor;

import java.net.URL;
import java.util.*;

@Component
@Slf4j
@Getter
public class MyProcessor implements PageProcessor {

    @Override
    public void process(Page page) {
        String situation = (String) page.getRequest().getExtra(RequestConstant.SITUATION);
        log.info("MyProcessor处理请求: URL={}, situation={}", page.getUrl(), situation);
        
        switch (situation) {
            case RequestConstant.SITUATION_PAGE:
                pageProcessor(page);
                break;
            case RequestConstant.SITUATION_DETAIL:
                detailProcessor(page);
                break;
            default:
                log.warn("未知的situation类型: {}", situation);
        }
    }


    /**
     * 列表页解析；通过源码获取所有详情URL，并进行列表项预处理
     *
     * @param page 该列表页请求的page，携带源码
     */
    private void pageProcessor(Page page) {
        log.info("正在处理的列表页：{}", page.getUrl());
        Request request = page.getRequest();
        CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
        try {
            // 列表项处理（预处理数据
            CrawlerManager.pageListDispose(page, request, collectTask);
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), request.getUrl(), "列表页解析失败", e.getMessage());
        }
    }


    /**
     * 详情页解析；通过源码解析正文，合并预处理数据保存到redis
     *
     * @param page 该详情页请求的page，携带源码
     */
    private void detailProcessor(Page page) {
        Request request = page.getRequest();
        CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
        try {
            // 1.获取预处理数据
            Map<String, String> dataItemMap = page.getRequest().getExtra(RequestConstant.PRE_DATE);

            if(request.getExtra(RequestConstant.LINK_TYPE) == RequestConstant.LINK_TYPE_OUTER_LINK){
                dataItemMap.put(NewsDataConstant.TARGET_URL, page.getRequest().getUrl());
                dataItemMap.put(NewsDataConstant.FLAG, "1");
            }

            // 2.内外链处理
            Map<String, String> independentDataMap = new HashMap<>();
            if (request.getExtra(RequestConstant.LINK_TYPE) == RequestConstant.LINK_TYPE_INNER_LINK) {
                // NOTE 内链
                independentDataMap = CrawlerManager.detailProcessorDispose(page, request, collectTask);
                // TODO 在这里更新一下内链页的源码，之前预处理列表项数据时存储的源码是列表项源码
                dataItemMap.put(NewsDataConstant.ORIGIN_CODE, page.getRawText());

            } else if (request.getExtra(RequestConstant.LINK_TYPE) == RequestConstant.LINK_TYPE_OUTER_LINK && request.getUrl().startsWith("https://mp.weixin.qq.com/")) {
                // NOTE 外链，目前这里只对微信外链进行处理（已经下载了源码），解析出标题、时间、来源、摘要、封面
                independentDataMap = CrawlerManager.getWechatBaseInfo(page, request, collectTask);
                //太大了，可以不用放进去

            }

            // 3.合并列表项预处理数据和详情数据
            if(independentDataMap!=null) {
                dataItemMap.putAll(independentDataMap);
            }


            // 4.存入缓存
            RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(dataItemMap));

        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), request.getUrl(), "详情页解析失败", e.getMessage());
        }
    }




    @Override
    public Site getSite() {
        return Site.me().setRetryTimes(1)
                .setCharset("UTF-8");
    }

}
