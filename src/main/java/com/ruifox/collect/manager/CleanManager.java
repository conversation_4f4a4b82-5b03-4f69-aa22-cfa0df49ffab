package com.ruifox.collect.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class CleanManager {
    public static String cleanNews(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), hmsCategory.getId(), hmsCategory.getName());
            }

            HmsCNewsMapper hmsCNewsMapper = ApplicationContextProvider.getBeanByType(HmsCNewsMapper.class);
            HmsCNewsDataMapper hmsCNewsDataMapper = ApplicationContextProvider.getBeanByType(HmsCNewsDataMapper.class);

            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<HmsCNews> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCNews::getCatid, hmsCategory.getId());
            List<HmsCNews> hmsCNews = hmsCNewsMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(HmsCNews hmsCNew : hmsCNews){
                dataIds.add(Math.toIntExact(hmsCNew.getDataid()));
                hmsCNewsMapper.deleteById(hmsCNew.getId());
            }
            LambdaQueryWrapper<HmsCNewsData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(HmsCNewsData::getDid, dataIds);
            hmsCNewsDataMapper.delete(dataWrapper);
            result = hmsCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanDoctor(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), hmsCategory.getId(), hmsCategory.getName());
            }

            HmsCMansMapper hmsCMansMapper = ApplicationContextProvider.getBeanByType(HmsCMansMapper.class);
            HmsCMansDataMapper hmsCMansDataMapper = ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class);


            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<HmsCMans> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCMans::getCatid, hmsCategory.getId());
            List<HmsCMans> hmsCMans = hmsCMansMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(HmsCMans hmsCMan : hmsCMans){
                dataIds.add(Math.toIntExact(hmsCMan.getDataid()));
                hmsCMansMapper.deleteById(hmsCMan.getId());
            }
            LambdaQueryWrapper<HmsCMansData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(HmsCMansData::getDid, dataIds);
            hmsCMansDataMapper.delete(dataWrapper);
            result = hmsCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanImage(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), hmsCategory.getId(), hmsCategory.getName());
            }

            HmsCImageMapper hmsCImageMapper = ApplicationContextProvider.getBeanByType(HmsCImageMapper.class);
            HmsCImageDataMapper hmsCImageDataMapper = ApplicationContextProvider.getBeanByType(HmsCImageDataMapper.class);


            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<HmsCImage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCImage::getCatid, hmsCategory.getId());
            List<HmsCImage> hmsCImages = hmsCImageMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(HmsCImage hmsCImage : hmsCImages){
                dataIds.add(Math.toIntExact(hmsCImage.getDataid()));
                hmsCImageMapper.deleteById(hmsCImage.getId());
            }
            LambdaQueryWrapper<HmsCImageData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(HmsCImageData::getDid, dataIds);
            hmsCImageDataMapper.delete(dataWrapper);
            result = hmsCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanVideo(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), hmsCategory.getId(), hmsCategory.getName());
            }

            HmsCVideoMapper hmsCVideoMapper = ApplicationContextProvider.getBeanByType(HmsCVideoMapper.class);
            HmsCVideoDataMapper hmsCVideoDataMapper = ApplicationContextProvider.getBeanByType(HmsCVideoDataMapper.class);


            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<HmsCVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCVideo::getCatid, hmsCategory.getId());
            List<HmsCVideo> hmsCVideos = hmsCVideoMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(HmsCVideo hmsCVideo : hmsCVideos){
                dataIds.add(Math.toIntExact(hmsCVideo.getDataid()));
                hmsCVideoMapper.deleteById(hmsCVideo.getId());
            }
            LambdaQueryWrapper<HmsCVideoData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(HmsCVideoData::getDid, dataIds);
            hmsCVideoDataMapper.delete(dataWrapper);
            result = hmsCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanLeader(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), hmsCategory.getId(), hmsCategory.getName());
            }

            HmsCLeaderMapper hmsCLeaderMapper = ApplicationContextProvider.getBeanByType(HmsCLeaderMapper.class);
            HmsCLeaderDataMapper hmsCLeaderDataMapper = ApplicationContextProvider.getBeanByType(HmsCLeaderDataMapper.class);


            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<HmsCLeader> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCLeader::getCatid, hmsCategory.getId());
            List<HmsCLeader> hmsCLeaders = hmsCLeaderMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(HmsCLeader hmsCLeader : hmsCLeaders){
                dataIds.add(Math.toIntExact(hmsCLeader.getDataid()));
                hmsCLeaderMapper.deleteById(hmsCLeader.getId());
            }
            LambdaQueryWrapper<HmsCLeaderData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(HmsCLeaderData::getDid, dataIds);
            hmsCLeaderDataMapper.delete(dataWrapper);
            result = hmsCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanNewspaper(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), hmsCategory.getId(), hmsCategory.getName());
            }

            HmsCNewspaperMapper hmsCNewspaperMapper = ApplicationContextProvider.getBeanByType(HmsCNewspaperMapper.class);
            HmsCNewspaperDataMapper hmsCNewspaperDataMapper = ApplicationContextProvider.getBeanByType(HmsCNewspaperDataMapper.class);


            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<HmsCNewspaper> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCNewspaper::getCatid, hmsCategory.getId());
            List<HmsCNewspaper> hmsCNewspapers = hmsCNewspaperMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(HmsCNewspaper hmsCNewspaper : hmsCNewspapers){
                dataIds.add(Math.toIntExact(hmsCNewspaper.getDataid()));
                hmsCNewspaperMapper.deleteById(hmsCNewspaper.getId());
            }
            LambdaQueryWrapper<HmsCNewspaperData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(HmsCNewspaperData::getDid, dataIds);
            hmsCNewspaperDataMapper.delete(dataWrapper);
            result = hmsCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanNewsJava(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            StationMArticleMapper stationMArticleMapper = ApplicationContextProvider.getBeanByType(StationMArticleMapper.class);
            StationMArticleDataMapper stationMArticleDataMapper = ApplicationContextProvider.getBeanByType(StationMArticleDataMapper.class);

            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<StationMArticle> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationMArticle::getCatId, stationCategory.getId());
            List<StationMArticle> stationMArticles = stationMArticleMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(StationMArticle stationMArticle : stationMArticles){
                dataIds.add(Math.toIntExact(stationMArticle.getDataId()));
                stationMArticleMapper.deleteById(stationMArticle.getId());
            }
            LambdaQueryWrapper<StationMArticleData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(StationMArticleData::getDataId, dataIds);
            stationMArticleDataMapper.delete(dataWrapper);
            result = stationCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
            return "栏目数据清洗失败,原因是"+e.getMessage();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanDoctorJava(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            StationMDoctorMapper stationMDoctorMapper = ApplicationContextProvider.getBeanByType(StationMDoctorMapper.class);
            StationMDoctorDataMapper stationMDoctorDataMapper = ApplicationContextProvider.getBeanByType(StationMDoctorDataMapper.class);

            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<StationMDoctor> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationMDoctor::getCatId, stationCategory.getId());
            List<StationMDoctor> stationMDoctors = stationMDoctorMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(StationMDoctor stationMDoctor : stationMDoctors){
                dataIds.add(Math.toIntExact(stationMDoctor.getDataId()));
                stationMDoctorMapper.deleteById(stationMDoctor.getId());
            }
            LambdaQueryWrapper<StationMDoctorData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(StationMDoctorData::getDataId, dataIds);
            stationMDoctorDataMapper.delete(dataWrapper);
            result = stationCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
            return "栏目数据清洗失败,原因是"+e.getMessage();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanImageJava(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            StationMImageMapper stationMImageMapper = ApplicationContextProvider.getBeanByType(StationMImageMapper.class);
            StationMImageDataMapper stationMImageDataMapper = ApplicationContextProvider.getBeanByType(StationMImageDataMapper.class);

            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<StationMImage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationMImage::getCatId, stationCategory.getId());
            List<StationMImage> stationMImages = stationMImageMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(StationMImage stationMImage : stationMImages){
                dataIds.add(Math.toIntExact(stationMImage.getDataId()));
                stationMImageMapper.deleteById(stationMImage.getId());
            }
            LambdaQueryWrapper<StationMImageData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(StationMImageData::getDataId, dataIds);
            stationMImageDataMapper.delete(dataWrapper);
            result = stationCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
            return "栏目数据清洗失败,原因是"+e.getMessage();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanVideoJava(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            StationMVideoMapper stationMVideoMapper = ApplicationContextProvider.getBeanByType(StationMVideoMapper.class);
            StationMVideoDataMapper stationMVideoDataMapper = ApplicationContextProvider.getBeanByType(StationMVideoDataMapper.class);

            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<StationMVideo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationMVideo::getCatId, stationCategory.getId());
            List<StationMVideo> stationMVideos = stationMVideoMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(StationMVideo stationMVideo : stationMVideos){
                dataIds.add(Math.toIntExact(stationMVideo.getDataId()));
                stationMVideoMapper.deleteById(stationMVideo.getId());
            }
            LambdaQueryWrapper<StationMVideoData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(StationMVideoData::getDataId, dataIds);
            stationMVideoDataMapper.delete(dataWrapper);
            result = stationCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
            return "栏目数据清洗失败,原因是"+e.getMessage();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanLeaderJava(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            StationMLeaderMapper stationMLeaderMapper = ApplicationContextProvider.getBeanByType(StationMLeaderMapper.class);
            StationMLeaderDataMapper stationMLeaderDataMapper = ApplicationContextProvider.getBeanByType(StationMLeaderDataMapper.class);

            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<StationMLeader> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationMLeader::getCatId, stationCategory.getId());
            List<StationMLeader> stationMLeaders = stationMLeaderMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(StationMLeader stationMLeader : stationMLeaders){
                dataIds.add(Math.toIntExact(stationMLeader.getDataId()));
                stationMLeaderMapper.deleteById(stationMLeader.getId());
            }
            LambdaQueryWrapper<StationMLeaderData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(StationMLeaderData::getDataId, dataIds);
            stationMLeaderDataMapper.delete(dataWrapper);
            result = stationCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
            return "栏目数据清洗失败,原因是"+e.getMessage();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }

    public static String cleanNewspaperJava(Integer collectTaskId){
        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
        String result = "";
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeDynamicDataSource();
            // 按栏目ID查询类别
            StationCategory stationCategory = ApplicationContextProvider.getBeanByType(StationCategoryMapper.class).selectById(collectTask.getImportCategoryId());
            if (stationCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return "栏目ID出错";
            } else {
                log.info("当前导入采集任务ID:{},当前导入栏目ID:{},当前导入栏目名称:{}", collectTask.getId(), stationCategory.getId(), stationCategory.getName());
            }

            StationMNewspaperMapper stationMNewspaperMapper = ApplicationContextProvider.getBeanByType(StationMNewspaperMapper.class);
            StationMNewspaperDataMapper stationMNewspaperDataMapper = ApplicationContextProvider.getBeanByType(StationMNewspaperDataMapper.class);

            // 删除所有该栏目的测试数据
            LambdaQueryWrapper<StationMNewspaper> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationMNewspaper::getCatId, stationCategory.getId());
            List<StationMNewspaper> stationMNewspapers = stationMNewspaperMapper.selectList(wrapper);
            List<Integer> dataIds = new ArrayList<>();
            for(StationMNewspaper stationMNewspaper : stationMNewspapers){
                dataIds.add(Math.toIntExact(stationMNewspaper.getDataId()));
                stationMNewspaperMapper.deleteById(stationMNewspaper.getId());
            }
            LambdaQueryWrapper<StationMNewspaperData> dataWrapper = new LambdaQueryWrapper<>();
            dataWrapper.in(StationMNewspaperData::getDataId, dataIds);
            stationMNewspaperDataMapper.delete(dataWrapper);
            result = stationCategory.getName() + "栏目数据清洗成功";
        }catch (Exception e){
            e.printStackTrace();
            return "栏目数据清洗失败,原因是"+e.getMessage();
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
        return result;
    }
}
