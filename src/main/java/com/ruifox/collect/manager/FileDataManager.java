package com.ruifox.collect.manager;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.common.constants.RequestConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.util.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import us.codecraft.webmagic.Request;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Data
public class FileDataManager {

    private static final ExecutorService threadPool = Executors.newFixedThreadPool(4); // 用于下载图片,附件,视频等的线程池

    public static ThreadPoolExecutor getThreadPool() {
        return (ThreadPoolExecutor) threadPool;
    }

    private static final List<String> suffixs = List.of("jpg", "jpeg", "png", "gif", "svg", "webp", "tiff", "mp3", "mp4",
            "docx", "doc", "xlsx", "xls", "pptx", "ppt", "pdf", "zip", "rar", "jar", "gz", "unknown");

    /**
     * 文件下载的路径（无类型后缀）
     * eg：C:/www.hospital-cqmu.com/20241096/151726365
     */
    public static String getDirPathFileName(
            CollectTask collectTask
    ) {
        return FileDataManager.getDirPath(collectTask) + FileDataManager.getFileName();
    }

    /**
     * 文件夹路径
     * eg：C:/www.hospital-cqmu.com/20241096/
     */
    public static String getDirPath(
            CollectTask collectTask
    ) {
        String fileSavePathPrefix = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/";
        String fileFolderName = collectTask.getFileFolderName();
        if (StringUtils.isNotBlank(fileFolderName)) {
            if (FileUtil.createFolder(fileSavePathPrefix + fileFolderName)) {
                return fileSavePathPrefix + fileFolderName + "/";
            } else {
                throw new RuntimeException("创建文件夹失败-->" + fileSavePathPrefix + fileFolderName);
            }
        }
        LocalDate localDate = LocalDate.now();
        String year = String.format("%02d", localDate.getYear());
        String month = String.format("%02d", localDate.getMonthValue());
        String day = String.format("%02d", localDate.getDayOfMonth());
        fileFolderName = Integer.parseInt(year + month + day) + collectTask.getId() + "";
        // 创建文件夹
        if (FileUtil.createFolder(fileSavePathPrefix + fileFolderName)) {
            collectTask.setFileFolderName(fileFolderName);
            LambdaUpdateWrapper<CollectTask> luw = new LambdaUpdateWrapper<>();
            luw.eq(CollectTask::getId, collectTask.getId()).
                    set(CollectTask::getFileFolderName, fileFolderName);
            ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).update(luw);
            RedisUtil.getInstance().set(RedisConstant.COLLECT_TASK + collectTask.getId(), JsonUtil.obj2String(collectTask));
        } else {
            throw new RuntimeException("创建文件夹失败-->" + fileSavePathPrefix + fileFolderName);
        }
        return fileSavePathPrefix + fileFolderName + "/";
    }

    /**
     * 文件名（无类型后缀）
     * eg：151726365
     */
    public static String getFileName(
    ) {
        Random random = new Random();
        int i = random.nextInt(90000) + 10000;
        LocalDateTime localDateTime = LocalDateTime.now();
        String hour = String.format("%02d", localDateTime.getHour());
        String minute = String.format("%02d", localDateTime.getMinute());

        return hour + minute + i;

        // NOTE 改为多线程异步下载文件后出现过命名相同重复的情况，换成了毫秒时间戳+随机值
//        Random random = new Random();
//        int i = random.nextInt(900) + 100;
//        long timeStamp  = Instant.now().toEpochMilli();
//        return String.valueOf(timeStamp) + i;
    }

    /**
     * 文件扩展后缀
     * 尝试从URL中获取，默认返回unknown
     */
    public static String getExtensionFromUrl(
            String url
    ) {
        if (url.contains("wx_fmt")) {
            String substring = url.substring(url.lastIndexOf("wx_fmt") + "wx_fmt".length());
            if (substring.contains("jpeg") || substring.contains("jpg") || substring.contains("other")) {
                return "jpg";
            }
            if (substring.contains("png")) {
                return "png";
            }
        }

        //特殊情况下
        /*if(url.contains("owner")){
            String substring = url.substring(url.lastIndexOf(".")+1);
            return substring;
        }*/

        String suffix = "unknown";
        if (url != null) {
            int index = url.lastIndexOf(".");
            if (index != -1) {
                suffix = url.substring(index);
                if (suffix.length() > 6)
                    suffix = "unknown";
            }
        }

        suffix = suffix.toLowerCase();
        for (String s : suffixs) {
            if (suffix.contains(s)) {
                suffix = s;
                break;
            }
        }
        return suffix;
    }

    /**
     * 文件下载记录
     */
    public static void fileDataRecord(
            String targetUrl,
            String pageId,
            String localUrl,
            String originUrl,
            int resCode,
            CollectTask collectTask
    ) {
        // FIXME 这里与taskObject中的file_table_fields对应，不应该写死，后续修改
        Map<Object, Object> map = new ConcurrentHashMap<>();
        map.put("target_url", targetUrl);
        map.put("page_id", pageId);
        map.put("local_url", localUrl);
        map.put("origin_url", originUrl);
        map.put("res_code", resCode);
        RedisUtil.getInstance().lLeftPush(RedisConstant.FILE_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
    }


    /**
     * 从HttpURL下载文件，多文件多线程异步下载
     *
     * @param urlList     网络URL集合
     * @param baseUrl     页面来源
     * @param pageId      页码ID
     * @param collectTask 页码ID
     * @return map集合，对每个下载URL对应本地路径
     */
    public static Map<String, String> downloadFromHttpUrl(
            List<String> urlList,
            String baseUrl,
            String pageId,
            CollectTask collectTask
    ) {
        Map<String, String> map = new HashMap<>();

        // 遍历下载URL集合，定义任务列表
        List<Callable<String>> taskList = new ArrayList<>();
        for (String url : urlList) {
            // 预下载到本地的路径（无类型后缀
            String filename = FileDataManager.getDirPathFileName(collectTask);
            // 尝试从URL中获取本文件后缀，默认失败返回"unknown"
            String extension = "." + FileDataManager.getExtensionFromUrl(url);

            //重庆附一的特殊处理
            /*if(url.contains("owner")){
                String substring = url.substring(url.lastIndexOf("."));
                url = url.replace(substring,"");
            }*/

            //若是下载url中有中文字符，会将其编码后进行下载
            url = urlReplaceChinese(url);

            taskList.add(new FileDownloader.DownloadTask(url, baseUrl, filename, extension, collectTask));
        }

        try {
            List<Future<String>> futureList = threadPool.invokeAll(taskList);
            for (int i = 0; i < futureList.size(); ++i) {
                String url = urlList.get(i);

                //下载url中&都是转义字符&amp;,所以替换回去时要将&替换为&amp;保证能匹配原文中的url
                url = url.replaceAll("&", "&amp;");

                Future<String> future = futureList.get(i);
                try {
                    String localPath = future.get();
                    map.put(url, localPath);
                    FileDataManager.fileDataRecord(baseUrl, pageId, localPath, url, 200, collectTask);
                } catch (Exception e) {
                    CrawlerManager.taskFailParseRecord(collectTask.getId(), baseUrl, "文件下载失败-->" + url, e.getMessage());
                    //将下载失败的文件放入redis中保存
                    Map<String, String> failedMap = new HashMap<>();
                    failedMap.put("collect_task_id", collectTask.getId().toString());
                    failedMap.put("page_id", pageId);
                    failedMap.put("url", baseUrl);
                    failedMap.put("file_path", url);
                    RedisUtil.getInstance().lLeftPush(RedisConstant.FILE_FAIL + collectTask.getId(), JsonUtil.obj2String(failedMap));
                }
            }
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), baseUrl, "文件下载失败", e.getMessage());
        }

        return map;
    }

    /**
     * 从HttpURL下载文件，单文件异步下载（其实就是同步
     *
     * @param url         网络URL
     * @param baseUrl     页面来源
     * @param pageId      页码ID
     * @param collectTask 采集任务
     * @return 本地下载路径
     */
    public static String downloadFromHttpUrl(
            String url,
            String baseUrl,
            String pageId,
            CollectTask collectTask
    ) {
        String localPath = "";
        String filename = FileDataManager.getDirPathFileName(collectTask);
        String extension = "." + FileDataManager.getExtensionFromUrl(url);
        String URL = url;

        try {
            Future<String> future = null;
            url = urlReplaceChinese(url);
            future = threadPool.submit(new FileDownloader.DownloadTask(url, baseUrl, filename, extension, collectTask));
            // NOTE 这里紧接着就获取异步线程的返回结果，等待时会阻塞主线程，总感觉其实就是一个同步
            localPath = future.get();
            FileDataManager.fileDataRecord(baseUrl, pageId, localPath, url, 200, collectTask);
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), baseUrl, "文件下载失败-->" + url, e.getMessage());
            //将下载失败的文件放入redis中保存
            Map<String, String> failedMap = new HashMap<>();
            failedMap.put("collect_task_id", collectTask.getId().toString());
            failedMap.put("page_id", pageId);
            failedMap.put("url", baseUrl);
            failedMap.put("file_path", URL);
            RedisUtil.getInstance().lLeftPush(RedisConstant.FILE_FAIL + collectTask.getId(), JsonUtil.obj2String(failedMap));
//            log.error("文件下载失败：" + url + ";微信公众号地址：" + baseUrl);
//            throw new RuntimeException(e);
        }

        return localPath;
    }

    /**
     * 将下载文件地址中的中文字符全部解码
     *
     * @param url 下载的文件的地址
     * @return
     */
    public static String urlReplaceChinese(
            String url
    ) {
        StringBuilder result = new StringBuilder();
        try {
            Pattern pattern = Pattern.compile("[^a-zA-Z0-9:/?#\\\\[\\\\]@!$&'()*+,;=._%]");
            Matcher matcher = pattern.matcher(url);

            int lastMatchEnd = 0;

            while (matcher.find()) {
                // 添加未匹配部分
                result.append(url, lastMatchEnd, matcher.start());
                // 编码匹配到的中文部分
                String chinesePart = matcher.group();
                String encodedPart = URLEncoder.encode(chinesePart, "UTF-8");
                if ("+".equals(encodedPart)) {
                    encodedPart = "%20";
                }
                result.append(encodedPart);
                lastMatchEnd = matcher.end();
            }

            // 添加最后未匹配部分
            result.append(url, lastMatchEnd, url.length());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        return result.toString();
    }

}
