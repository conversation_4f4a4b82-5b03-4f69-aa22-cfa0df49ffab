package com.ruifox.collect.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruifox.collect.common.constants.*;
import com.ruifox.collect.designmode.factory.WebDriverFactory;
import com.ruifox.collect.module.entity.news.NewsTable;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;
import com.ruifox.collect.dao.mapper.*;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.module.entity.*;
import com.ruifox.collect.util.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.WebDriverWait;
import us.codecraft.webmagic.Page;
import us.codecraft.webmagic.Request;
import us.codecraft.webmagic.selector.PlainText;

import javax.imageio.ImageIO;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruifox.collect.common.constants.RedisConstant.*;

@Slf4j
@Data
public class CrawlerManager {

    private static final Map<String, String> CONTENT_TYPE_MAP = new HashMap<>();
    private static final Map<String, String> REVERSE_CONTENT_TYPE_MAP = new HashMap<>();

    static {
        CONTENT_TYPE_MAP.put("pdf", "application/pdf");
        CONTENT_TYPE_MAP.put("doc", "application/msword");
        CONTENT_TYPE_MAP.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        CONTENT_TYPE_MAP.put("xls", "application/vnd.ms-excel");
        CONTENT_TYPE_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        CONTENT_TYPE_MAP.put("ppt", "application/vnd.ms-powerpoint");
        CONTENT_TYPE_MAP.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        CONTENT_TYPE_MAP.put("txt", "text/plain");
        CONTENT_TYPE_MAP.put("html", "text/html");
        CONTENT_TYPE_MAP.put("xml", "text/xml");
        CONTENT_TYPE_MAP.put("json", "application/json");
        CONTENT_TYPE_MAP.put("jpg", "image/jpeg");
        CONTENT_TYPE_MAP.put("jpeg", "image/jpeg");
        CONTENT_TYPE_MAP.put("png", "image/png");
        CONTENT_TYPE_MAP.put("gif", "image/gif");
        CONTENT_TYPE_MAP.put("bmp", "image/bmp");
        CONTENT_TYPE_MAP.put("svg", "image/svg+xml");
        CONTENT_TYPE_MAP.put("mp3", "audio/mpeg");
        CONTENT_TYPE_MAP.put("wav", "audio/wav");
        CONTENT_TYPE_MAP.put("mp4", "video/mp4");
        CONTENT_TYPE_MAP.put("avi", "video/x-msvideo");
        CONTENT_TYPE_MAP.put("mpeg", "video/mpeg");
        CONTENT_TYPE_MAP.put("ts", "video/mpeg");
        CONTENT_TYPE_MAP.put("m3u8", "application/x-mpegURL");
        CONTENT_TYPE_MAP.put("zip", "application/zip");
        CONTENT_TYPE_MAP.put("rar", "application/x-rar-compressed");
        CONTENT_TYPE_MAP.put("tar", "application/x-tar");
        CONTENT_TYPE_MAP.put("gz", "application/gzip");
        CONTENT_TYPE_MAP.put("7z", "application/x-7z-compressed");
        CONTENT_TYPE_MAP.put("css", "text/css");
        CONTENT_TYPE_MAP.put("js", "application/javascript");
        CONTENT_TYPE_MAP.put("ftl", "text/html"); // FreeMarker 模板类型

        CONTENT_TYPE_MAP.put("webp", "image/webp"); // WebP 图像格式
        CONTENT_TYPE_MAP.put("ico", "image/x-icon"); // 图标文件
        CONTENT_TYPE_MAP.put("tiff", "image/tiff"); // TIFF 图像格式
        CONTENT_TYPE_MAP.put("psd", "image/vnd.adobe.photoshop"); // Photoshop 文件
        CONTENT_TYPE_MAP.put("ai", "application/postscript"); // Adobe Illustrator 文件
        CONTENT_TYPE_MAP.put("eps", "application/postscript"); // Encapsulated PostScript 文件
        CONTENT_TYPE_MAP.put("flv", "video/x-flv"); // Flash 视频
        CONTENT_TYPE_MAP.put("mkv", "video/x-matroska"); // Matroska 视频
        CONTENT_TYPE_MAP.put("mov", "video/quicktime"); // QuickTime 视频
        CONTENT_TYPE_MAP.put("wmv", "video/x-ms-wmv"); // Windows Media Video
        CONTENT_TYPE_MAP.put("mpga", "audio/mpeg"); // MPEG 音频
        CONTENT_TYPE_MAP.put("aac", "audio/aac"); // AAC 音频
        CONTENT_TYPE_MAP.put("flac", "audio/flac"); // FLAC 音频
        CONTENT_TYPE_MAP.put("ogg", "audio/ogg"); // OGG 音频
        CONTENT_TYPE_MAP.put("webm", "video/webm"); // WebM 视频
        CONTENT_TYPE_MAP.put("woff", "font/woff"); // WOFF 字体
        CONTENT_TYPE_MAP.put("woff2", "font/woff2"); // WOFF2 字体
        CONTENT_TYPE_MAP.put("ttf", "font/ttf"); // TrueType 字体
        CONTENT_TYPE_MAP.put("otf", "font/otf"); // OpenType 字体
        CONTENT_TYPE_MAP.put("eot", "application/vnd.ms-fontobject"); // EOT 字体
        CONTENT_TYPE_MAP.put("csv", "text/csv"); // CSV 文件
        CONTENT_TYPE_MAP.put("tsv", "text/tsv"); // TSV 文件
        CONTENT_TYPE_MAP.put("rtf", "application/rtf"); // RTF 文件
        CONTENT_TYPE_MAP.put("md", "text/markdown"); // Markdown 文件
        CONTENT_TYPE_MAP.put("log", "text/plain"); // 日志文件

        for (String key : CONTENT_TYPE_MAP.keySet()) {
            String value = CONTENT_TYPE_MAP.get(key);
            REVERSE_CONTENT_TYPE_MAP.put(value, key);
        }
    }

    /**
     * 通过文件名称获取对应的contentType
     *
     * @param fileName
     * @return
     */
    public static ContentType getContentTypeFromFileName(String fileName) {
        String extension = getFileExtension(fileName);
        String type = CONTENT_TYPE_MAP.getOrDefault(extension.toLowerCase(Locale.CHINA), "application/octet-stream");
        ContentType contentType = ContentType.create(type);
        return contentType;
    }

    /**
     * 通过文件名下载
     *
     * @param fileName
     * @return
     */
    private static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return ""; // No extension found or the dot is at the end of the file name
        }
        return fileName.substring(dotIndex + 1);
    }

    //    private static String account = "gxhzsrmyy13096378734";
//    private static String password = "qr3Feb2HMdNnCbXO1ywarWFKr5SD5AvGO2Nrkbrn6GQ=";
    //成都第5人民医院
//    private static String token = "eyJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************.XsLSsu1B8968W2jbCqXkOupMtb_ekvI6smsNlmkdHf0";


    //重庆附一
//    private static String account = "cqmu13096378734";
//    private static String password = "NkBRhKZCcWb+9jFaCRC8IMNuZjL8QrE1v7R3DIZowps=";
      private static String token = "eyJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************.bUdGyqeL30iTw9qKvPqDnd_dIVrRLZbUsavZwhlsvGM";


    //凉山州
//    private static String token = "eyJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************.psxvSRAbWnK0J4Ie38ragM6Yu-c2eI7V25HJdePeo0A";
    private static String tokenUrl = "http://***********:9100/auth/auth/login";

    private static CloseableHttpClient client = HttpClients.custom()
            .setDefaultRequestConfig(
                    RequestConfig.custom()
                            .setCookieSpec(CookieSpecs.STANDARD) // 或 CookieSpecs.LAX
                            .build()
            )
            .build();

    /**
     * 失败记录，打印并保存
     */
    public static void taskFailParseRecord(
            int collectTaskId,
            String url,
            String cause,
            String record
    ) {
        log.info("{}：{}", cause, url);
        ApplicationContextProvider.getBeanByType(TaskParseRecordMapper.class)
                .insert(new TaskParseRecord(collectTaskId, url, cause, record));
    }

    /**
     * 新标签打开目标URL
     */
    public static String openAndSwitchTo(
            RemoteWebDriver driver,
            String url
    ) {
        Set<String> preWindowHandles = driver.getWindowHandles();

        ((JavascriptExecutor) driver).executeScript("window.open(arguments[0], '_blank');", url);

        Set<String> afterWindowHandles = driver.getWindowHandles();

        for (String currentWindowHandle : CollUtil.subtract(afterWindowHandles, preWindowHandles)) {
            driver.switchTo().window(currentWindowHandle);
        }
        return driver.getWindowHandle();
    }

    public static class PageSourceResult {
        private String pageSource;
        private String finalUrl;

        public PageSourceResult(String pageSource, String finalUrl) {
            this.pageSource = pageSource;
            this.finalUrl = finalUrl;
        }

        public String getPageSource() {
            return pageSource;
        }

        public String getFinalUrl() {
            return finalUrl;
        }
    }

    /**
     * driver新标签打开页面获取源码
     */
    public static PageSourceResult getPageSourceFromDriver(
            RemoteWebDriver driver,
            String url,
            CollectTask collectTask
    ) {
        try {
            String preWindowHandle = driver.getWindowHandle();
            CrawlerManager.openAndSwitchTo(driver, url);
            Sleeper.sleep(collectTask.getTimeOut(), TimeUnit.SECONDS);
            String pageSource = driver.getPageSource();
            // 获取跳转后的真实URL
            String finalUrl = driver.getCurrentUrl();

            //转换为绝对路径
            Document doc = Jsoup.parse(pageSource, finalUrl);
            String baseUrl = getBaseUrlFromRequest(finalUrl);
            // 处理img元素src属性中的相对路径
            Elements imgs = doc.select("img[src]");
            for (Element img : imgs) {
                String relativeSrc = img.attr("src");
                if (isRelativePath(relativeSrc)) {
                    String absoluteSrc = baseUrl + "/" + relativeSrc;
                    img.attr("src", absoluteSrc);
                }
            }
            // 处理a元素href属性中的相对路径
            Elements links = doc.select("a[href]");
            for (Element link : links) {
                String relativeHref = link.attr("href");
                if (isRelativePath(relativeHref)) {
                    String absoluteHref = baseUrl + "/" + relativeHref;
                    link.attr("href", absoluteHref);
                }
            }

            // 处理video元素src属性中的相对路径
            Elements videos = doc.select("video[src]");
            for (Element video : videos) {
                String relativeSrc = video.attr("src");
                if (isRelativePath(relativeSrc)) {
                    String absoluteSrc = baseUrl + "/" + relativeSrc;
                    video.attr("src", absoluteSrc);
                }
            }
            pageSource = doc.toString();
            driver.close();
            driver.switchTo().window(preWindowHandle);
            if (StringUtils.isBlank(pageSource) || BusinessConstant.EmptyPage.equals(pageSource)) {
                pageSource = HttpClientUtils.getPageSource(finalUrl);
            }

            return new PageSourceResult(pageSource, finalUrl);
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), url, "driverJS下载源码失败", e.getMessage());
            return new PageSourceResult("", url);
        }
    }

    private static String getBaseUrlFromRequest(String fullUrl) {
        try {
            URL url = new URL(fullUrl);
            String protocol = url.getProtocol(); // 获取协议，比如 "https"
            String host = url.getHost(); // 获取主机名，比如 "example.com"
            int port = url.getPort();
            if (port == -1) {
                // 如果没有显式指定端口，根据协议使用默认端口
                if ("https".equals(protocol)) {
                    port = 443;
                } else if ("http".equals(protocol)) {
                    port = 80;
                }
            }
            return protocol + "://" + host + ":" + port;
        } catch (MalformedURLException e) {
            e.printStackTrace();
            return fullUrl; // 如果解析出错，返回原始的完整URL（可能不太理想，但避免程序崩溃）
        }
    }

    private static boolean isRelativePath(String path) {
        // 检查路径是否以"./"、"../"开头或者不包含协议（如http、https）部分
        return path.startsWith("./") || path.startsWith("../") ||
                (!path.startsWith("http://") && !path.startsWith("https://"));
    }


    /**
     * 初始化步骤中的获取下一页按钮
     *
     * @param driver       当前浏览器
     * @param taskTemplate 采集模板
     * @return 下一页按钮的元素
     */
    public static WebElement getNextButton(
            RemoteWebDriver driver,
            TaskTemplate taskTemplate
    ) {
        // 1.下一页按钮的名称
        String nextButtonName = taskTemplate.getNextButtonName();
        // 2.按照模版查找下一页按钮
        List<WebElement> elements = driver.findElements(By.cssSelector(taskTemplate.getNextButtonLoc()));
        WebElement nextButton = null;
        for (WebElement element : elements) {
            String text = element.getText();
            text = text.trim();
            if (StringUtils.isNotBlank(nextButtonName)) {
                if (nextButtonName.equals(text)) {
                    nextButton = element;
                    break;
                }
            } else {
                if (text.isEmpty()) {
                    nextButton = element;
                    break;
                }
            }
        }
        return nextButton;
    }


    /**
     * 初始翻页，根据翻页类型，正常：获取所有列表页的URL，异步：获取所有详情页的URL，添加到请求队列中
     *
     * @param driver    selenium浏览器驱动
     * @param page      初始请求page，会往其中添加所有URL请求
     * @param situation 标记是正常翻页还是异步请求翻页
     */
    public static void multiPageDispose(
            RemoteWebDriver driver,
            Page page,
            String situation
    ) {
        // 1.获取总页数: 传0 -> 采集当前栏目所有列表页; 传1 -> 采集当前页; 其他 -> 采集指定页数
        CollectTask collectTask = RedisGetManager.getCollectTask(page.getRequest().getExtra(RequestConstant.COLLECT_TASK_ID));
        int pageSum = collectTask.getPageSum() == 0 ? 9999 : collectTask.getPageSum();
        //TODO 排序，记录当前页数
        String sort = collectTask.getSort();
        int pageNum = 1;
        if (StringUtils.isBlank(sort)) {
            sort = "10";
        } else {
            // NOTE 约定在创建采集任务时，如果需要指定开始的pageNum值，则与sort初始值用" "分隔
            String[] split = collectTask.getSort().split(" ");
            sort = split[0];
            if (split.length > 1) {
                pageNum = Integer.parseInt(split[1]);
            }
        }

        log.info("===== 开始multiPageDispose处理 =====");
        log.info("任务ID: {}, 目标URL: {}, 总页数: {}, situation: {}",
                collectTask.getId(), collectTask.getTargetUrl(), pageSum, situation);

        // 2.打开首页
        // FIXME 这里对 非异步请求并且是只有一页的情况 是可以不打开首页的（拿到的URL肯定是collect.targetUrl）
        // FIXME 为了和其余情况通用也打开一下吧，反正耗时也不会特别多，后续看如何改进这里
        String preWindowHandle = driver.getWindowHandle();
        CrawlerManager.openAndSwitchTo(driver, collectTask.getTargetUrl());
        log.info("已打开目标URL: {}", driver.getCurrentUrl());

        Sleeper.sleep(collectTask.getTimeOut(), TimeUnit.SECONDS);
        //获取其对应的特殊处理类
        WebSpecialHanding specialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
        specialHanding.loginInit(driver, collectTask);

        // 3.开始翻页
        String preUrl = driver.getCurrentUrl();
        TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);
        log.info("模板翻页按钮配置: nextButtonLoc={}, nextButtonName={}",
                taskTemplate.getNextButtonLoc(), taskTemplate.getNextButtonName());

        WebElement nextButton = null;

        for (int i = 1; i <= pageSum; ++i) {
            log.info("处理第 {} 页，当前URL: {}", i, preUrl);

            Request request = new Request(preUrl);
            request.putExtra(RequestConstant.COLLECT_TASK_ID, collectTask.getId());
            request.putExtra(RequestConstant.SITUATION, RequestConstant.SITUATION_PAGE);
            request.putExtra(RequestConstant.SORT, sort + " " + pageNum);

            // 3.1.正常采集（快速翻页
            if (RequestConstant.SITUATION_INIT.equals(situation)) {
                page.addTargetRequest(request);
                log.info("添加列表页请求到队列: {}", request.getUrl());

                //翻页不改变url，但详情页是新的url，且页数少时可以这样，使解析列表页的时候直接根据此时我翻到的页数进行解析
                //request.putExtra("RawText", driver.getPageSource());

                // 3.2.异步请求采集（一边翻页，一边解析)
                // NOTE 用于翻页不改变url但详情页是新的url，此时把前后页url相同退出翻页注释掉
            } else if (RequestConstant.SITUATION_ASYNCHRONOUS.equals(situation)) {
                page.setRawText(driver.getPageSource());
                page.setUrl(new PlainText(preUrl));
                page.isDownloadSuccess();
                // 复用处理列表页的方法
                CrawlerManager.pageListDispose(page, request, collectTask);
            }

            // 3.3.翻页
            // NOTE 正常翻页可以不指定总页数，但异步请求翻页必需指定页数或者可以明确知道最后一页不会存在翻页按钮
            // NOTE 未指定总页数时，目前对最后一页的判定是找不到翻页按钮或者翻页前后的URL相同
            if (i == pageSum) {
                log.info("已到最后一页，翻页结束，添加列表页：" + i);
                break;
            }

            try {
                Sleeper.sleep(3, TimeUnit.SECONDS);

                nextButton = CrawlerManager.getNextButton(driver, taskTemplate);
                if (nextButton == null) {
                    log.info("无下一页按钮，翻页结束，添加列表页：" + i);
                    break;
                } else {
                    log.info("找到下一页按钮，准备点击");
                }
            } catch (Exception e) {
                log.error("查找下一页按钮时出错: {}", e.getMessage());
                break;
            }

            nextButton.click();
            pageNum++;
            String currentUrl = driver.getCurrentUrl();
            log.info("点击下一页后，新URL: {}", currentUrl);

            if (RequestConstant.SITUATION_INIT.equals(situation) && preUrl.equals(currentUrl)) {
                log.info("翻页前后URL相同，翻页结束，添加列表页：" + (--pageNum));
                break;
            }
            preUrl = currentUrl;
            // 休眠一段时间
            Sleeper.sleep(collectTask.getTimeOut(), TimeUnit.SECONDS);
        }

        log.info("===== multiPageDispose处理完成，共处理了 {} 页 =====", pageNum);

        // 关闭当前标签页
        driver.close();
        // 回退到指定标签页
        driver.switchTo().window(preWindowHandle);
    }


    /**
     * 元数据填充
     *
     * @param dataItemMap 存储数据集合
     * @param collectTask 采集任务
     * @param request     页码请求
     * @param pageSource  页面源码
     */
    public static void fillMetaData(
            Map<String, String> dataItemMap,
            CollectTask collectTask,
            Request request,
            String pageSource
    ) {
        // TODO 合并了新闻和医生的元数据填充
        dataItemMap.put(BusinessConstant.COLLECT_TASK_ID, String.valueOf(collectTask.getId()));
        dataItemMap.put(BusinessConstant.CLASSIFICATION, collectTask.getImportCategoryName());
        dataItemMap.put(BusinessConstant.TARGET_URL, request.getUrl());
        dataItemMap.put(BusinessConstant.PAGE_ID, UUID.randomUUID().toString());

        String sort = request.getExtra(RequestConstant.SORT).toString();
        String[] split = sort.split(" ");
        if (split.length == 3) {
            String s1 = String.format("%4s", split[0]).replace(" ", "0");
            String s2 = String.format("%4s", split[1]).replace(" ", "0");
            String s3 = String.format("%4s", split[2]).replace(" ", "0");
            sort = s1 + s2 + s3;
        }
        dataItemMap.put(BusinessConstant.SORT, sort);

        if (request.getExtra(RequestConstant.LINK_TYPE) == RequestConstant.LINK_TYPE_OUTER_LINK) {
            dataItemMap.put(BusinessConstant.FLAG, "1");
        } else {
            dataItemMap.put(BusinessConstant.FLAG, "0");
        }
        dataItemMap.put(BusinessConstant.ORIGIN_CODE, pageSource);

        TaskObject taskObject = RedisGetManager.getTaskObject(collectTask);
        Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
        //将其他字段放入dataItemMap中
        dataTableFieldMap.forEach((key, value) -> {
            if (!dataItemMap.containsKey(key)) {
                dataItemMap.put(key, "");
            }
        });
    }


    /**
     * JSON形式的字段映射 --> Map(字段,字段内容)
     *
     * @param fieldLocStr 字段映射JSON字符串
     * @param element     列表项
     * @return Map(字段, 字段内容)
     */
    public static HashMap<String, String> fieldLocElementParse2Map(
            String fieldLocStr,
            Element element
    ) {
        // 结果集
        HashMap<String, String> result = new HashMap<>();
        // 字段映射集
        Map<String, String> listItemFieldLocMap = JSONUtil.toBean(fieldLocStr, Map.class);

        // 遍历每一个键值对
        for (Map.Entry<String, String> entry : listItemFieldLocMap.entrySet()) {
            try {
                // 字段无需解析
                if (entry.getValue().equals(BusinessConstant.EXCLUDE_PARSE_FLAG)) {
                    continue;
                }

                StringBuilder resStr = new StringBuilder();
                // 字段中可能包含多个元素，约定用 ; 分隔
                for (String outerStr : entry.getValue().split(";")) {
                    // NOTE 用##确定元素类型(默认为text)，用子标签 > 和 :nth-of-type() 来确定元素
                    String[] split = outerStr.split("##");
                    outerStr = split[0];
                    String type = split.length > 1 ? split[1] : "text";

                    // 获取元素
                    // 约定字段值为 0 表示需求元素为整体父元素
                    Element AElement = null;
                    if (outerStr.equals(BusinessConstant.SELF_ELEMENT)) {
                        AElement = element;
                    } else {
                        AElement = element.select(outerStr).first();
                    }

                    // 约定：当解析的元素不为正文，则获取文本内容，为正文，则获取源码
                    if ("content".equals(entry.getKey()) || "biographic_sketch".equals(entry.getKey())) {
                        resStr.append(AElement.outerHtml());
                    } else if ("text".equals(type)) {
                        resStr.append(AElement.text());
                    } else {
                        resStr.append(AElement.attr(type));
                    }
                }

                // 结果存储
                result.put(entry.getKey(), resStr.toString());
            } catch (Exception e) {
                log.warn("列表项或详情页预处理字段解析出错！无该元素：" + entry.getKey() + "==>" + e.getMessage());
                result.put(entry.getKey(), "");
            }
        }
        // 返回结果
        return result;
    }


    /**
     * 列表页解析
     *
     * @param page        该列表页的请求page，携带源码
     * @param request     该列表页的请求request
     * @param collectTask 采集任务
     */
    public static void pageListDispose(
            Page page,
            Request request,
            CollectTask collectTask
    ) {
        try {
            // 1.获取所有列表项的集合
            Document document = page.getHtml().getDocument();
            TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);

            Elements itemList = document.select(taskTemplate.getListItemTag());

            if (itemList == null || itemList.isEmpty()) {
                throw new RuntimeException("获取所有列表项为空");
            }

            // 2.遍历列表项，记录排序
            String sort = request.getExtra(RequestConstant.SORT);
            int queue = 0;

            for (Element item : itemList) {
                queue++;

                Request req = new Request();
                req.putExtra(RequestConstant.COLLECT_TASK_ID, collectTask.getId());
                req.putExtra(RequestConstant.SORT, sort + " " + queue);
                req.putExtra(RequestConstant.SITUATION, RequestConstant.SITUATION_DETAIL);

                // 内嵌、内链、外链判断
                if (StringUtils.isBlank(taskTemplate.getListItemClickLoc())) {
                    req.putExtra(RequestConstant.LINK_TYPE, RequestConstant.LINK_TYPE_EMBEDDED); // 内嵌
                    req.setUrl(request.getUrl());
                } else {
                    // TODO 定位可点击项（一般为a标签），获取详情URL
                    // NOTE 约定为 0 标记可点击项即为当前元素
                    String href;
                    if (item.select(taskTemplate.getListItemClickLoc()).first() != null) {
                        href = BusinessConstant.SELF_ELEMENT.equals(taskTemplate.getListItemClickLoc()) ?
                                item.attr("href") : item.select(taskTemplate.getListItemClickLoc()).first().attr("href");
                    } else {
                        queue--;
                        continue;
                    }

                    req.setUrl(href);
                    req.putExtra(RequestConstant.LINK_TYPE,
                            !href.equals(request.getUrl()) && new URL(href).getHost().equals(collectTask.getHost()) ?
                                    RequestConstant.LINK_TYPE_INNER_LINK : RequestConstant.LINK_TYPE_OUTER_LINK);

                    if (request.getUrl().contains("scmy120.cn"))
                        req.putExtra(RequestConstant.LINK_TYPE, RequestConstant.LINK_TYPE_INNER_LINK);
                }

                // 元信息填充
                Map<String, String> dataItemMap = new ConcurrentHashMap<>();
                CrawlerManager.fillMetaData(dataItemMap, collectTask, req, item.outerHtml());
                req.putExtra(RequestConstant.PAGE_ID, dataItemMap.get(NewsDataConstant.PAGE_ID));

                // 列表项预处理信息
                Map<String, String> filed2dataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getListItemFieldLoc(), item);
                // 特殊处理列表项数据
                WebSpecialHanding specialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                specialHanding.specialHandingForListItem(filed2dataMap, collectTask, req);
                // 整合数据
                dataItemMap.putAll(filed2dataMap);

                req.putExtra(RequestConstant.PRE_DATE, dataItemMap);

                // 内嵌数据不放入队列，因为是同一个url会被拦截
                if (req.getExtra(RequestConstant.LINK_TYPE) == RequestConstant.LINK_TYPE_EMBEDDED) {
                    RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(dataItemMap));
                } else {
                    // 添加请求入队
                    page.addTargetRequest(req);
                }
            }
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), request.getUrl(), "列表页解析失败", e.getMessage());
        }
    }

    /**
     * 内链详情页解析
     *
     * @param page        内链详情页面，携带源码
     * @param request     该详情页的请求request，携带基本元素
     * @param collectTask 采集任务
     * @return Map集合，包含解析的信息
     */
    public static Map<String, String> detailProcessorDispose(
            Page page,
            Request request,
            CollectTask collectTask
    ) {
        Map<String, String> independentDataMap = new HashMap<>();
        Element detailElement = null;
        try {
            // 1.获取正文元素
            Document document = page.getHtml().getDocument();
            TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);
            detailElement = document.select(taskTemplate.getIndependentDetailTag()).first();
            if (detailElement == null) {
                throw new RuntimeException("获取正文详情失败");
            }

            // 2.按非内嵌模版进行初始解析
            independentDataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getIndependentDetailFieldLoc(), detailElement);

            // 3.特殊处理
            WebSpecialHanding webSpecialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
            webSpecialHanding.specialHandingForNotEmbedded(independentDataMap, collectTask, request);

            // 4.下载正文中的文件、图片、视频等，并更新正文
            // NOTE 本来对正文中文件的处理是在特殊处理中的，因为那里可能已经把正文转化为document了一次
            // NOTE 现在把文件处理拿出来了，方便理解代码，多了一次转化为document的过程，反正耗时也不会太多
            // FIXME 后续如果有更好的方法再修
            String content = independentDataMap.get(NewsDataConstant.CONTENT);

            if (StringUtils.isNotBlank(content)) {
                // NOTE 加了一个保险，这里使用Jsoup.parse(html, baseURI)来转化为document，多了一个参数当前页面URL
                // NOTE 目的是为了配合attr(abs:xxx)使用，将可能的相对路径转化为绝对路径，因为获取源码时有可能没有转化相对路径
                Document contextDom = Jsoup.parse(content, request.getUrl());
                // TODO 获取需要下载的链接集合
                List<String> linkList = Stream.of(taskTemplate.getContentALoc(), taskTemplate.getContentImgLoc(), taskTemplate.getContentVideoLoc())
                        .flatMap(loc -> contextDom.select(loc).stream().map(element -> {
                            String attr = "";
                            if (loc.equals(taskTemplate.getContentALoc())) {
                                attr = element.attr("abs:href");
                            } else if (loc.equals(taskTemplate.getContentImgLoc()) || loc.equals(taskTemplate.getContentVideoLoc())) {
                                // 如果是base64格式的图片，无法获取获取绝对地址
                                String src = element.attr("src");
                                if (src.contains("base64")) {
                                    attr = src;
                                } else {
                                    attr = element.attr("abs:src");
                                }
                            } else {
                                attr = "";
                            }
                            // TODO 加了一个保险，重新编码下载链接中的参数部分（这里可能需要处理URL参数中带中文造成乱码的情况
                            attr = HttpClientUtils.ReEncodeHttpUrl(attr);
                            String substring = attr.substring(attr.indexOf("./W0") + "./W0".length(), attr.indexOf("./W0") + "./W0".length() + 6);
                            if (substring.equals("202506"))
                                substring = "202505";
                            attr = attr.replace("/./", "/dzdg/gd/" + substring + "/");
                            return attr;
                        }))
                        .filter(url -> webSpecialHanding.specialHandingForJudgeHttpUrl(url, collectTask)) //过滤无用链接
                        .distinct() //去重
                        .collect(Collectors.toList());

                // TODO 多线程异步下载文件
                Map<String, String> map = FileDataManager.downloadFromHttpUrl(linkList, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);

                // TODO 更新正文
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    if (FileUtil.localUrlChick(entry.getValue())) {
                        String origin = entry.getKey();
                        String substring = origin.substring(origin.indexOf("/dzdg/gd/"), origin.indexOf("/dzdg/gd/") + "/dzdg/gd/".length() + 7);
                        origin = origin.replace(substring, "/./");
                        content = content.replace(origin, entry.getValue());
                    }
                }
                independentDataMap.put("content", content);
            }

            return independentDataMap;
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), request.getUrl(), "内链页解析失败", e.getMessage());
            log.error("获取到的正文：{},获取的结果：{}", detailElement, independentDataMap);
            return null;
        }
    }


    /**
     * 微信外链基本信息获取（标题、来源、时间，摘要，封面）
     *
     * @param page        微信详情页面
     * @param request     该详情页的请求request，携带基本元素
     * @param collectTask 采集任务
     * @return Map集合，包含基本信息
     */
    public static Map<String, String> getWechatBaseInfo(
            Page page,
            Request request,
            CollectTask collectTask
    ) {
        Map<String, String> map = new HashMap<>();
        try {
            Document document = page.getHtml().getDocument();
            // 标题
            String title = "";
            Element titleElement = document.select("h1#activity-name").first();
            if (titleElement != null) {
                title = titleElement.text();
            }
            // 来源
            String come_from = "";
            Element comefromElement = document.select("a#js_name").first();
            if (comefromElement != null) {
                come_from = comefromElement.text();
            }
            // 发布时间
            String publish_time = "";
            Element publishElement = document.select("em#publish_time").first();
            if (publishElement != null) {
                publish_time = publishElement.text();
                if (StringUtils.isNotBlank(publish_time)) {
                    Date date = DateUtils.parseDate(publish_time, "yyyy年MM月dd日 HH:mm");
                    publish_time = String.valueOf(date.getTime() / 1000);
                }
            }

            // 正文元素
            Element content = document.select("div#js_content").first();

            // 摘要
            String description = content.text();
            if (StringUtils.isBlank(description)) {
                description = "";
            } else {
                description = StringUtils.trim(description.substring(0, Math.min(description.length(), 150))) + "...";
            }
            // 封面
            // NOTE 目前选择封面的逻辑是如果有视频的话就选择视频封面，否则选择第二张图片作为封面
            String thumb = "";
            Elements videoElements = content.select("video");
            if (!videoElements.isEmpty()) {
                thumb = videoElements.first().attr("poster");
            } else {
                Elements imgElements = content.select("img");
                for (Element imgElement : imgElements) {
                    String src = imgElement.attr("data-src");
                    String index = imgElement.attr("data-index");
                    if (StringUtils.isBlank(src) || StringUtils.isBlank(index) || src.contains("gif")) {
                        continue;
                    }
                    thumb = src;
                    if (Integer.parseInt(index) >= 2) {
                        break;
                    }
                }
            }
            if (StringUtils.isNotBlank(thumb)) {
                thumb = FileDataManager.downloadFromHttpUrl(thumb, "https://mp.weixin.qq.com/", request.getExtra(NewsDataConstant.PAGE_ID), collectTask);
            } else {
                thumb = "";
            }

            map.put(NewsDataConstant.TITLE, title);
            map.put(NewsDataConstant.COME_FROM, come_from);
            map.put(NewsDataConstant.PUBLISH_TIME, publish_time);
            map.put(NewsDataConstant.DESCRIPTION, description);
            map.put(NewsDataConstant.THUMB, thumb);
        } catch (Exception e) {
            throw new RuntimeException("解析微信文章失败，原因：" + e.getMessage());
        }
        return map;
    }


    /**
     * 动态建表，根据采集对象的指定字段
     *
     * @param collectTask 采集任务
     * @return true:成功，false:失败
     */
    public static boolean DynamicalTable(
            CollectTask collectTask
    ) {
        try {
            // 动态建表
            TaskObject taskType = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());
            ApplicationContextProvider.getBeanByType(CommonMapper.class).createDynamicTable(collectTask.getDataTableName(), JsonUtil.Json2Obj(taskType.getDataTableFields(), Map.class));
            ApplicationContextProvider.getBeanByType(CommonMapper.class).createDynamicTable(collectTask.getFileTableName(), JsonUtil.Json2Obj(taskType.getFileTableFields(), Map.class));
            return true;
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTask.getId(), collectTask.getTargetUrl(), "动态建表失败", e.getMessage());
            return false;
        }
    }


    /**
     * 持久化进数据库，批量落库
     *
     * @param collectTask 采集任务
     */
    public static void persistenceDatas(
            CollectTask collectTask
    ) {

        // 动态建表
        if (!CrawlerManager.DynamicalTable(collectTask)) {
            return;
        }

        // 正常落库
        CommonMapper commonMapper = ApplicationContextProvider.getBeanByType(CommonMapper.class);
        RedisUtil redisUtil = RedisUtil.getInstance();

        // 基本信息和文件信息条目数获取
        Long dataItemsSize = redisUtil.lLen(DATA_ITEMS + collectTask.getId());
        Long fileItemsSize = redisUtil.lLen(FILE_ITEMS + collectTask.getId());

        // 下载失败文件信息条目数获取
        Long fileFailedSize = redisUtil.lLen(FILE_FAIL + collectTask.getId());

        // 基本信息批量落库
        int batchNum = Integer.parseInt(ConfigUtil.getProperties("common.database.batchNum"));
        int insertLength = Integer.parseInt(dataItemsSize.toString());
        int i = 0;
        while (insertLength > batchNum) {
            commonMapper.insertBatch(collectTask.getDataTableName(), redisUtil.lRange(DATA_ITEMS + collectTask.getId(), i, i + batchNum)
                    .stream()
                    .map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class))
                    .collect(Collectors.toList()));
            i += batchNum;
            insertLength -= batchNum;
        }
        if (insertLength > 0) {
            commonMapper.insertBatch(collectTask.getDataTableName(), redisUtil.lRange(DATA_ITEMS + collectTask.getId(), i, i + insertLength)
                    .stream()
                    .map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class))
                    .collect(Collectors.toList()));
        }

        // 文件信息批量落库
        batchNum = Integer.parseInt(ConfigUtil.getProperties("common.database.batchNum"));
        insertLength = Integer.parseInt(fileItemsSize.toString());
        i = 0;
        while (insertLength > batchNum) {
            commonMapper.insertBatch(collectTask.getFileTableName(), redisUtil.lRange(FILE_ITEMS + collectTask.getId(), i, i + batchNum)
                    .stream()
                    .map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class))
                    .collect(Collectors.toList()));
            i += batchNum;
            insertLength -= batchNum;
        }
        if (insertLength > 0) {
            commonMapper.insertBatch(collectTask.getFileTableName(), redisUtil.lRange(FILE_ITEMS + collectTask.getId(), i, i + insertLength)
                    .stream()
                    .map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class))
                    .collect(Collectors.toList()));
        }

        // 下载失败信息批量落库
        batchNum = Integer.parseInt(ConfigUtil.getProperties("common.database.batchNum"));
        insertLength = Integer.parseInt(fileFailedSize.toString());
        i = 0;
        while (insertLength > batchNum) {
            commonMapper.insertBatch(FILE_FAIL_TABLE, redisUtil.lRange(FILE_FAIL + collectTask.getId(), i, i + batchNum)
                    .stream()
                    .map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class))
                    .collect(Collectors.toList()));
            i += batchNum;
            insertLength -= batchNum;
        }
        if (insertLength > 0) {
            commonMapper.insertBatch(FILE_FAIL_TABLE, redisUtil.lRange(FILE_FAIL + collectTask.getId(), i, i + insertLength)
                    .stream()
                    .map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class))
                    .collect(Collectors.toList()));
        }
    }

    /**
     * 医生数据只在单页，且无内链，点击图片对应生成详细信息的采集
     *
     * @param driver  selenium浏览器驱动
     * @param request 初始页面的请求request，携带基本元素
     * @return 一个不需要再被处理的page
     */
    public static Page detailDockerSpecial(
            RemoteWebDriver driver,
            Request request
    ) {
        CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
        TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);
        //打开到需要采集的页面
        CrawlerManager.openAndSwitchTo(driver, collectTask.getTargetUrl());

        // 等待页面加载，确保页面内容完整
        WebDriverWait wait = new WebDriverWait(driver, 10);
        //ExpectedConditions.presenceOfElementLocated这是一个预期条件，用于检查某个元素是否存在于 DOM（文档对象模型）中，但不一定可见
        wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getListItemTag())));

        //下载最开始的page
        Page page = new Page();
        String pageSource = driver.getPageSource();

        page.setRawText(pageSource);
        page.setUrl(new PlainText(request.getUrl()));
        page.isDownloadSuccess();

        //搜寻医生外面的信息,通过列表项标签
        List<WebElement> outElements = driver.findElements(By.cssSelector(taskTemplate.getListItemTag()));

        //通过sort表示查询的医生的顺序,实例10 1 1->第一科室第一个医生
        String sort = collectTask.getSort();
        if (StringUtils.isBlank(sort)) {
            sort = "10";
        }
        int i = 0;

        for (WebElement outElement : outElements) {
            if (sort.split(" ").length <= 1) {
                sort += " " + ++i;
            } else {
                sort = sort.substring(0, sort.indexOf(" ") + 1);
                sort += ++i;
            }
            try {
                Document document = Jsoup.parse(page.getRawText(), request.getUrl());
                Element select = document.select(taskTemplate.getListItemTag()).get(i - 1);
                // 列表项预处理信息
                Map<String, String> filed2dataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getListItemFieldLoc(), select);

                // 特殊处理列表项数据
                WebSpecialHanding specialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                specialHanding.specialHandingForListItem(filed2dataMap, collectTask, request);

                //将列表项中可点击的元素的位置当作每一个医生的点击
                List<WebElement> elements = outElement.findElements(By.cssSelector(taskTemplate.getListItemClickLoc()));
                int j = 0;
                for (WebElement element : elements) {
                    if (sort.split(" ").length <= 2) {
                        sort += " " + ++j;
                    } else {
                        sort = sort.substring(0, sort.lastIndexOf(" ") + 1);
                        sort += ++j;
                    }

                    //最后的查询结果的map
                    Map<String, String> dataItemMap = new ConcurrentHashMap<>();

                    // 整合列表项的数据
                    dataItemMap.putAll(filed2dataMap);

                    //打开每个医生的详情
                    element.click();

                    // 等待详情页加载
                    wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getIndependentDetailTag())));

                    //下载此时的page
                    String pageSource1 = driver.getPageSource();
                    page.setRawText(pageSource1);
                    page.setUrl(new PlainText(request.getUrl()));
                    page.isDownloadSuccess();

                    // 元信息填充
                    request.putExtra(RequestConstant.SORT, sort);
                    CrawlerManager.fillMetaData(dataItemMap, collectTask, request, driver.getPageSource());
                    request.putExtra(RequestConstant.PAGE_ID, dataItemMap.get(NewsDataConstant.PAGE_ID));

                    // 获取点击后的数据
                    Map<String, String> independentDataMap = new HashMap<>();
                    Document parse = Jsoup.parse(page.getRawText(), request.getUrl());
                    //找到点击后打开的元素
                    Element ContentElement = parse.select(taskTemplate.getIndependentDetailTag()).first();
                    //获取其中的数据
                    independentDataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getIndependentDetailFieldLoc(), ContentElement);

                    //特殊获取每个医生的职称
                    String position = parse.select("div.details > div:last-child > div:nth-of-type(" + i + ") > ul > li:nth-of-type(" + j + ") > dl > dd").text();
                    independentDataMap.put(DoctorDataConstant.DOC_POSITION, position);

                    // 特殊处理点击后的数据
                    WebSpecialHanding webSpecialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                    webSpecialHanding.specialHandingForNotEmbedded(independentDataMap, collectTask, request);

                    //下载正文中需要的资源
                    String content = independentDataMap.get(DoctorDataConstant.CONTENT);
                    // NOTE 加了一个保险，这里使用Jsoup.parse(html, baseURI)来转化为document，多了一个参数当前页面URL
                    // NOTE 目的是为了配合attr(abs:xxx)使用，将可能的相对路径转化为绝对路径，因为获取源码时有可能没有转化相对路径
                    Document contextDom = Jsoup.parse(content, request.getUrl());
                    // TODO 获取需要下载的链接集合
                    List<String> linkList = Stream.of(taskTemplate.getContentALoc(), taskTemplate.getContentImgLoc(), taskTemplate.getContentVideoLoc())
                            .flatMap(loc -> contextDom.select(loc).stream().map(element1 -> {
                                String attr = "";
                                if (loc.equals(taskTemplate.getContentALoc())) {
                                    attr = element1.attr("abs:href");
                                } else if (loc.equals(taskTemplate.getContentImgLoc()) || loc.equals(taskTemplate.getContentVideoLoc())) {
                                    attr = element1.attr("abs:src");
                                } else {
                                    attr = "";
                                }
                                // TODO 加了一个保险，重新编码下载链接中的参数部分（这里可能需要处理URL参数中带中文造成乱码的情况
                                attr = HttpClientUtils.ReEncodeHttpUrl(attr);
                                return attr;
                            }))
                            .filter(url -> webSpecialHanding.specialHandingForJudgeHttpUrl(url, collectTask)) //过滤无用链接
                            .distinct() //去重
                            .collect(Collectors.toList());
                    // TODO 多线程异步下载文件
                    Map<String, String> map = FileDataManager.downloadFromHttpUrl(linkList, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);

                    // TODO 更新正文
                    for (Map.Entry<String, String> entry : map.entrySet()) {
                        if (FileUtil.localUrlChick(entry.getValue())) {
                            content = content.replace(entry.getKey(), entry.getValue());
                        }
                    }
                    independentDataMap.put("content", content);

                    //整合数据
                    if (independentDataMap != null) {
                        independentDataMap.forEach((key, value) -> {
                            String val = dataItemMap.get(key);
                            if (StringUtils.isBlank(val)) {
                                dataItemMap.put(key, value);
                            }
                        });
                    }

                    //将每一个医生搜集到的信息放入redis中
                    RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(dataItemMap));

                    //查询完医生的详情后进行关闭
                    WebElement closeElement = driver.findElement(By.cssSelector(taskTemplate.getNextButtonLoc()));
                    closeElement.click();
                }
            } catch (Exception e) {
                CrawlerManager.taskFailParseRecord(collectTask.getId(), request.getUrl(), "查询失败", e.getMessage());
                log.info(e.getMessage());
            }
        }
        return new Page();
    }

    /**
     * 列表项点击元素是js变化，即无详情页url,且点击下一页按钮url不发生变化,就进行特殊的列表页处理
     *
     * @param driver  selenium浏览器驱动
     * @param request 初始页面的请求request，携带基本元素
     * @return 一个不需要再被处理的page
     */
    public static Page listNewsSpecial(
            RemoteWebDriver driver,
            Request request
    ) {
        CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
        TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);
        CrawlerManager.openAndSwitchTo(driver, request.getUrl());

        //进入招标信息
        String common = taskTemplate.getCommon();
        driver.findElement(By.cssSelector(common)).click();


        //攀研院特殊处理---------
        Sleeper.sleep(6, TimeUnit.SECONDS);

        // 等待页面加载，确保页面内容完整
        WebDriverWait wait = new WebDriverWait(driver, 1000);
        wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getListItemTag())));
        /*for (int i = 0; i < 26; i++) {
            Sleeper.sleep(2,TimeUnit.SECONDS);
            driver.findElement(By.cssSelector(taskTemplate.getNextButtonLoc())).click();
        }*/
        Sleeper.sleep(2, TimeUnit.SECONDS);

        String sort = request.getExtra(RequestConstant.SORT);
        //点击返回回到第一页的特殊情况，直接进详情页一直点击下一篇------------
        //driver.findElements(By.cssSelector(taskTemplate.getListItemTag())).get(0).click();


        //手动获取要翻页的页数
        for (int i = 0; i < 1; i++) {

            //中途由于网络问题导致失败，从失败页处重新开始
            /*if(i<9){
                Sleeper.sleep(2, TimeUnit.SECONDS);
                //下一页按钮
                WebElement nextElement = driver.findElement(By.cssSelector(taskTemplate.getNextButtonLoc()));
                nextElement.click();
                continue;
            }*/

            //ExpectedConditions.presenceOfElementLocated这是一个预期条件，用于检查某个元素是否存在于 DOM（文档对象模型）中，但不一定可见
            wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getListItemTag())));

            //获取此时页面源码
            String pageSource = driver.getPageSource();
            Document document = Jsoup.parse(pageSource);

            //1.获取所有列表项的集合,要是webElement的，因为要对其进行点击获取详情
            List<WebElement> outElements = driver.findElements(By.cssSelector(taskTemplate.getListItemTag()));

            //不需要点击下一篇的处理------------------------
            // 2.遍历列表项，记录排序
            if (sort.split(" ").length <= 1) {
                sort += " " + (i + 1);
            } else {
                sort = sort.substring(0, sort.indexOf(" ") + 1);
                sort += (i + 1);
            }
            int queue = 0;
            for (WebElement outElement : outElements) {
                queue++;
                if (sort.split(" ").length <= 2) {
                    sort += " " + queue;
                } else {
                    sort = sort.substring(0, sort.lastIndexOf(" ") + 1);
                    sort += queue;
                }
                request.putExtra(RequestConstant.SORT, sort);

                //每一次重新查找要点击的列表项
                Sleeper.sleep(2, TimeUnit.SECONDS);
                WebElement webElement = driver.findElements(By.cssSelector(taskTemplate.getListItemTag())).get(queue - 1);

                //存储最终信息的map
                Map<String, String> dataItemMap = new HashMap<>();

                // 元信息填充
                request.putExtra(RequestConstant.SORT, sort);
                CrawlerManager.fillMetaData(dataItemMap, collectTask, request, driver.getPageSource());
                request.putExtra(RequestConstant.PAGE_ID, dataItemMap.get(NewsDataConstant.PAGE_ID));

                //获取列表项的element元素
                Element elementList = document.select(taskTemplate.getListItemTag()).get(queue - 1);

                // 列表项预处理信息
                Map<String, String> filed2dataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getListItemFieldLoc(), elementList);

                // 特殊处理列表项数据
                WebSpecialHanding specialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                specialHanding.specialHandingForListItem(filed2dataMap, collectTask, request);

                // 整合与处理信息
                dataItemMap.putAll(filed2dataMap);

                //点击列表项元素，打开详情页面
                String listItemClickLoc = taskTemplate.getListItemClickLoc();
                WebElement itemClick;
                if (listItemClickLoc.equals("0")) {
                    itemClick = webElement;
                } else {
                    itemClick = driver.findElement(By.cssSelector(listItemClickLoc));
                }
                itemClick.click();

                // 等待详情页加载
                Sleeper.sleep(2, TimeUnit.SECONDS);

                //下载此时的page
                String pageSource1 = driver.getPageSource();
                Document document1 = Jsoup.parse(pageSource1);

                // 详情页处理信息的map
                Map<String, String> independentDataMap = new HashMap<>();

                //找到要处理的详情页信息
                Element ContentElement = document1.select(taskTemplate.getIndependentDetailTag()).first();

                //获取详情页的数据
                independentDataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getIndependentDetailFieldLoc(), ContentElement);

                // 特殊处理点击后的数据
                WebSpecialHanding webSpecialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                webSpecialHanding.specialHandingForNotEmbedded(independentDataMap, collectTask, request);

                //下载正文中需要的资源
                String content = independentDataMap.get(NewsDataConstant.CONTENT);
                // NOTE 加了一个保险，这里使用Jsoup.parse(html, baseURI)来转化为document，多了一个参数当前页面URL
                // NOTE 目的是为了配合attr(abs:xxx)使用，将可能的相对路径转化为绝对路径，因为获取源码时有可能没有转化相对路径
                Document contextDom = Jsoup.parse(content, request.getUrl());
                // TODO 获取需要下载的链接集合
                List<String> linkList = Stream.of(taskTemplate.getContentALoc(), taskTemplate.getContentImgLoc(), taskTemplate.getContentVideoLoc())
                        .flatMap(loc -> contextDom.select(loc).stream().map(element1 -> {
                            String attr = "";
                            if (loc.equals(taskTemplate.getContentALoc())) {
                                attr = element1.attr("abs:href");
                            } else if (loc.equals(taskTemplate.getContentImgLoc()) || loc.equals(taskTemplate.getContentVideoLoc())) {
                                attr = element1.attr("abs:src");
                            } else {
                                attr = "";
                            }
                            // TODO 加了一个保险，重新编码下载链接中的参数部分（这里可能需要处理URL参数中带中文造成乱码的情况
                            attr = HttpClientUtils.ReEncodeHttpUrl(attr);
                            return attr;
                        }))
                        .filter(url -> webSpecialHanding.specialHandingForJudgeHttpUrl(url, collectTask)) //过滤无用链接
                        .distinct() //去重
                        .collect(Collectors.toList());
                // TODO 多线程异步下载文件
                Map<String, String> map = FileDataManager.downloadFromHttpUrl(linkList, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);

                // TODO 更新正文
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    if (FileUtil.localUrlChick(entry.getValue())) {
                        content = content.replace(entry.getKey(), entry.getValue());
                    }
                }
                independentDataMap.put("content", content);

                //整合数据
                if (independentDataMap != null) {
                    independentDataMap.forEach((key, value) -> {
                        String val = dataItemMap.get(key);
                        if (StringUtils.isBlank(val)) {
                            dataItemMap.put(key, value);
                        }
                    });
                }

                //将信息放入redis中
                RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(dataItemMap));

                //关闭详情页面
                WebElement closeClick = driver.findElement(By.cssSelector(taskTemplate.getCommon()));
                closeClick.click();
            }

            /*Sleeper.sleep(2, TimeUnit.SECONDS);
            //下一页按钮
            WebElement nextElement = driver.findElement(By.cssSelector(taskTemplate.getNextButtonLoc()));
            nextElement.click();*/
        }


        //要点击下一篇的代码--------------------
            /*int pagination = (i + 1) / 9 + 1;
            if((i+1)%9==0)
                pagination -= 1;

            int number = (i + 1) % 9;
            if(number == 0)
                number = 9;

            sort = "10";
            sort = sort + " " + pagination + " " + number;

            //存储最终信息的map
            Map<String, String> dataItemMap = new HashMap<>();

            // 元信息填充
            request.putExtra(RequestConstant.SORT, sort);
            CrawlerManager.fillMetaData(dataItemMap, collectTask, request, driver.getPageSource());
            request.putExtra(RequestConstant.PAGE_ID, dataItemMap.get(NewsDataConstant.PAGE_ID));

            // 等待详情页加载
            Sleeper.sleep(2,TimeUnit.SECONDS);

            //下载此时的page
            String pageSource1 = driver.getPageSource();
            Document document1 = Jsoup.parse(pageSource1);

            // 详情页处理信息的map
            Map<String, String> independentDataMap = new HashMap<>();

            //找到要处理的详情页信息
            Element ContentElement = document1.select(taskTemplate.getIndependentDetailTag()).first();

            //获取详情页的数据
            independentDataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getIndependentDetailFieldLoc(), ContentElement);

            // 特殊处理点击后的数据
            WebSpecialHanding webSpecialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
            webSpecialHanding.specialHandingForNotEmbedded(independentDataMap, collectTask, request);

            //下载正文中需要的资源
            String content = independentDataMap.get(NewsDataConstant.CONTENT);
            // NOTE 加了一个保险，这里使用Jsoup.parse(html, baseURI)来转化为document，多了一个参数当前页面URL
            // NOTE 目的是为了配合attr(abs:xxx)使用，将可能的相对路径转化为绝对路径，因为获取源码时有可能没有转化相对路径
            Document contextDom = Jsoup.parse(content, request.getUrl());
            // TODO 获取需要下载的链接集合
            List<String> linkList = Stream.of(taskTemplate.getContentALoc(), taskTemplate.getContentImgLoc(), taskTemplate.getContentVideoLoc())
                    .flatMap(loc -> contextDom.select(loc).stream().map(element1 -> {
                        String attr = "";
                        if (loc.equals(taskTemplate.getContentALoc())) {
                            attr = element1.attr("abs:href");
                        } else if (loc.equals(taskTemplate.getContentImgLoc()) || loc.equals(taskTemplate.getContentVideoLoc())) {
                            attr = element1.attr("abs:src");
                        } else {
                            attr = "";
                        }
                        // TODO 加了一个保险，重新编码下载链接中的参数部分（这里可能需要处理URL参数中带中文造成乱码的情况
                        attr = HttpClientUtils.ReEncodeHttpUrl(attr);
                        return attr;
                    }))
                    .filter(url -> webSpecialHanding.specialHandingForJudgeHttpUrl(url, collectTask)) //过滤无用链接
                    .distinct() //去重
                    .collect(Collectors.toList());
            // TODO 多线程异步下载文件
            Map<String, String> map = FileDataManager.downloadFromHttpUrl(linkList, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);

            // TODO 更新正文
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (FileUtil.localUrlChick(entry.getValue())) {
                    content = content.replace(entry.getKey(), entry.getValue());
                }
            }
            independentDataMap.put("content", content);

            //整合数据
            if (independentDataMap != null) {
                independentDataMap.forEach((key, value) -> {
                    String val = dataItemMap.get(key);
                    if (StringUtils.isBlank(val)) {
                        dataItemMap.put(key, value);
                    }
                });
            }

            //将信息放入redis中
            RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(dataItemMap));

            //进入下一篇
            List<WebElement> elements = driver.findElements(By.cssSelector(taskTemplate.getCommon()));
            elements.get(elements.size() - 1).click();
        }*/
        return new Page();
    }

    /**
     * 列表页特殊处理，列表页中存在iframe
     *
     * @param driver  selenium浏览器驱动
     * @param request 初始页面的请求request，携带基本元素
     * @return 一个不需要再被处理的page
     */
    public static Page listNewsSpecialAndIframe(
            RemoteWebDriver driver,
            Request request
    ) {
        CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
        TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);
        CrawlerManager.openAndSwitchTo(driver, request.getUrl());

        WebElement notificationMenu = driver.findElement(By.xpath("//li/a[text()='公司要闻']"));
        notificationMenu.click();
        Sleeper.sleep(2, TimeUnit.SECONDS);


        List<String> hrefs = new ArrayList<>();
        // 切换到iframe中
        driver.switchTo().frame("gscontentiframe");
        Sleeper.sleep(1, TimeUnit.SECONDS);
        for (int i = 0; i < 1; i++) {
            //找到所有的列表项
            Document document = Jsoup.parse(driver.getPageSource());
            Elements lists = document.select(taskTemplate.getListItemTag());
            for (Element list : lists) {
                Element a = list.select(taskTemplate.getListItemClickLoc()).first();
                hrefs.add("https://www.ceadrc.cn" + a.attr("href"));
            }

            // 使用 JavaScript 直接点击按钮
            /*if (i != 23) {
                WebElement nextButton = driver.findElement(By.cssSelector(taskTemplate.getNextButtonLoc()));
                JavascriptExecutor js = (JavascriptExecutor) driver;
                js.executeScript("arguments[0].click();", nextButton);
                Sleeper.sleep(1, TimeUnit.SECONDS);
            }*/
        }

        int index = 1;
        int page = 1;
        //对每一个详情页的处理
        for (String href : hrefs) {
            if (index % 21 == 0) {
                index = 1;
                page++;
            }
            String sort = collectTask.getSort() + " " + page + " " + index;
            index++;

            //存储最终信息的map
            Map<String, String> dataItemMap = new HashMap<>();

            // 元信息填充
            request.putExtra(RequestConstant.SORT, sort);
            CrawlerManager.fillMetaData(dataItemMap, collectTask, request, driver.getPageSource());
            request.putExtra(RequestConstant.PAGE_ID, dataItemMap.get(NewsDataConstant.PAGE_ID));

            //打开详情页
            driver.get(href);
            Sleeper.sleep(2, TimeUnit.SECONDS);
            Document document = Jsoup.parse(driver.getPageSource());

            // 详情页处理信息的map
            Map<String, String> independentDataMap = new HashMap<>();

            //找到要处理的详情页信息
            Element ContentElement = document.select(taskTemplate.getIndependentDetailTag()).first();

            //获取详情页的数据
            independentDataMap = CrawlerManager.fieldLocElementParse2Map(taskTemplate.getIndependentDetailFieldLoc(), ContentElement);

            // 特殊处理点击后的数据
            WebSpecialHanding webSpecialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
            webSpecialHanding.specialHandingForNotEmbedded(independentDataMap, collectTask, request);

            //下载正文中需要的资源
            String content = independentDataMap.get(NewsDataConstant.CONTENT);
            // NOTE 加了一个保险，这里使用Jsoup.parse(html, baseURI)来转化为document，多了一个参数当前页面URL
            // NOTE 目的是为了配合attr(abs:xxx)使用，将可能的相对路径转化为绝对路径，因为获取源码时有可能没有转化相对路径
            Document contextDom = Jsoup.parse(content, request.getUrl());
            // TODO 获取需要下载的链接集合
            List<String> linkList = Stream.of(taskTemplate.getContentALoc(), taskTemplate.getContentImgLoc(), taskTemplate.getContentVideoLoc())
                    .flatMap(loc -> contextDom.select(loc).stream().map(element1 -> {
                        String attr = "";
                        if (loc.equals(taskTemplate.getContentALoc())) {
                            attr = element1.attr("abs:href");
                        } else if (loc.equals(taskTemplate.getContentImgLoc()) || loc.equals(taskTemplate.getContentVideoLoc())) {
                            attr = element1.attr("abs:src");
                        } else {
                            attr = "";
                        }
                        // TODO 加了一个保险，重新编码下载链接中的参数部分（这里可能需要处理URL参数中带中文造成乱码的情况
                        attr = HttpClientUtils.ReEncodeHttpUrl(attr);
                        return attr;
                    }))
                    .filter(url -> webSpecialHanding.specialHandingForJudgeHttpUrl(url, collectTask)) //过滤无用链接
                    .distinct() //去重
                    .collect(Collectors.toList());
            // TODO 多线程异步下载文件
            Map<String, String> map = FileDataManager.downloadFromHttpUrl(linkList, request.getUrl(), request.getExtra(RequestConstant.PAGE_ID), collectTask);

            // TODO 更新正文
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (FileUtil.localUrlChick(entry.getValue())) {
                    String origin = entry.getKey();
                    origin = origin.replace("https://www.panyan.cn/", "");
                    content = content.replace(origin, entry.getValue());
                }
            }
            independentDataMap.put("content", content);

            //整合数据
            if (independentDataMap != null) {
                independentDataMap.forEach((key, value) -> {
                    String val = dataItemMap.get(key);
                    if (StringUtils.isBlank(val)) {
                        dataItemMap.put(key, value);
                    }
                });
            }

            //将信息放入redis中
            RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(dataItemMap));

        }

        return new Page();
    }

    /**
     * 二次对该任务id的下载失败的文件进行重新下载
     *
     * @param collectTaskId 任务id
     * @return 下载成功或失败的提示
     */
    public static String DownloadAgain(Integer collectTaskId) {
        // 清除该任务id的redis中的数据
        RedisGetManager.deleteRedisById(collectTaskId);

        CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);

        LambdaQueryWrapper<TaskFileFailed> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskFileFailed::getCollectTaskId, collectTaskId);
        TaskFileFailedMapper taskFileFailedMapper = ApplicationContextProvider.getBeanByType(TaskFileFailedMapper.class);
        NewsTableMapper newsTableMapper = ApplicationContextProvider.getBeanByType(NewsTableMapper.class);
        TaskDataMapper taskDataMapper = ApplicationContextProvider.getBeanByType(TaskDataMapper.class);
        List<TaskFileFailed> taskFileFaileds = taskFileFailedMapper.selectList(wrapper);

        int failFileNum = taskFileFaileds.size();

        for (TaskFileFailed taskFileFailed : taskFileFaileds) {
            String pageId = taskFileFailed.getPageId();
            String baseUrl = taskFileFailed.getUrl();
            String path = taskFileFailed.getFilePath();
            String localPath = FileDataManager.downloadFromHttpUrl(path, baseUrl, pageId, collectTask);

            // 找到之前由于网络原因没有下载成功的文件
            if (StringUtils.isNotBlank(localPath)) {
                // 找到对应原数据
                List<NewsTable> newsTables = newsTableMapper.selectByPageId(collectTask.getDataTableName(), pageId);
                // 将其中该文件路径进行修改并更新
                for (NewsTable newsTable : newsTables) {
                    String thumb = newsTable.getThumb();
                    String content = newsTable.getContent();
                    if (thumb.contains(path)) {
                        thumb = thumb.replace(path, localPath);
                        newsTable.setThumb(thumb);
                    }
                    if (content.contains(path)) {
                        content = content.replace(path, localPath);
                        newsTable.setContent(content);
                    }
                    newsTableMapper.updateById(collectTask.getDataTableName(), newsTable);
                }
            }
        }
        // 更新taskData表中下载成功文件数量和失败文件数量
        LambdaQueryWrapper<TaskData> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TaskData::getCollectTaskId, collectTaskId);
        List<TaskData> taskDatas = taskDataMapper.selectList(wrapper1);

        Long fileFailedSize = RedisUtil.getInstance().lLen(FILE_FAIL + collectTask.getId());
        for (TaskData taskData : taskDatas) {
            Long failedSize = taskData.getFileFailedSize();
            Long successSize = taskData.getFileSuccessSize();
            successSize += failedSize - fileFailedSize;
            failedSize = fileFailedSize;
            taskData.setFileFailedSize(failedSize);
            taskData.setFileSuccessSize(successSize);
            taskDataMapper.updateById(taskData);
        }

        // 删除之前存放在其中的下载失败的文件信息
        taskFileFailedMapper.delete(wrapper);

        // 重新放入现在新的下载失败的文件信息
        CrawlerManager.persistenceDatas(collectTask);

        RedisGetManager.deleteRedisById(collectTaskId);

        return "原下载失败文件数为:" + failFileNum + "，二次下载后下载失败文件数为:" + fileFailedSize + "，成功下载文件数为:" + (failFileNum - fileFailedSize);
    }

    /**
     * 将文件上传到服务器，获取返回的地址
     *
     * @param file
     * @return 文件上传后的地址
     */
    public static String changeFileUrl(File file) {
//        String token = getToken(account, password);
        String urlValue = "";
        int retryCount = 3;
        for (int i = 0; i < retryCount; i++) {
            try {
                HttpClient client = HttpClientBuilder.create().build();
                HttpPost request = new HttpPost("http://***********:9100/file/sysFile/upload");
                request.setHeader("X-token", token);

                // 使用MultipartEntityBuilder构建form-data格式的请求体
                MultipartEntityBuilder builder = MultipartEntityBuilder.create();
                // 添加文件部分到请求体
                builder.addBinaryBody("file", file, getContentTypeFromFileName(file.getName()), file.getName());
                HttpEntity entity = builder.build();
                request.setEntity(entity);

                HttpResponse response = client.execute(request);
                int statusCode = response.getStatusLine().getStatusCode();
                System.out.println("响应码: " + statusCode);
                if (statusCode == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity());

                    // 使用Jackson解析JSON获取url
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode rootNode = objectMapper.readTree(responseBody);
                    JsonNode dataNode = rootNode.get("data");
                    if (dataNode != null) {
                        JsonNode urlNode = dataNode.get("url");
                        if (urlNode != null) {
                            urlValue = urlNode.asText();
                        }
                    }
                    break;
                } else {
                    System.out.println("请求失败，响应码: " + statusCode + "，正在进行第 " + (i + 1) + " 次重试");
                    if (i < retryCount - 1) {
                        TimeUnit.SECONDS.sleep(1);
                    }
                }
            } catch (java.net.SocketException e) {
                // 针对Connection reset by peer: socket write error异常进行更详细的日志记录
                System.err.println("Socket连接出现异常：Connection reset by peer: socket write error，正在进行第 " + (i + 1) + " 次重试");
                System.err.println("异常详细信息: " + e.getMessage());
                if (i < retryCount - 1) {
                    try {
                        TimeUnit.SECONDS.sleep(1);
                    } catch (InterruptedException ex) {
                        System.err.println("线程睡眠被中断，重试可能受影响");
                        ex.printStackTrace();
                    }
                }
            } catch (IOException ex) {
                // 处理其他IO相关异常，比如网络连接、读取响应等出现的问题
                System.err.println("发生IO异常，请求可能失败");
                System.err.println("异常详细信息: " + ex.getMessage());
                ex.printStackTrace();
                // 这里可以根据具体需求调用相关方法记录任务失败等情况，如原代码中的
                CrawlerManager.taskFailParseRecord(1, "", "下载失败", ex.getMessage());
            } catch (Exception e) {
                // 捕获其他未预期到的异常
                System.err.println("发生未知异常");
                e.printStackTrace();
                CrawlerManager.taskFailParseRecord(1, "", "下载失败", e.getMessage());
            }
        }
        return urlValue;
    }

    public static void main(String[] args) {
        File file = new File("C:\\Collect_Data\\www.xyfy.com.cn\\********\\*********.png");
        String s = changeFileUrl(file);
        System.out.println(s);
    }

    /**
     * 获取token
     *
     * @param account
     * @param password
     * @return
     */
    public static String getToken(String account, String password) {
        String token = "";

        HttpPost request = new HttpPost(tokenUrl);

        try {
            // 设置请求头
            request.setHeader("Content-Type", "application/json");
            // 使用 ObjectMapper 构建 JSON 请求体
            ObjectMapper objectMapper = new ObjectMapper();
            java.util.Map<String, String> requestBody = new java.util.HashMap<>();
            requestBody.put("account", account);
            requestBody.put("password", password);
            String jsonBody = objectMapper.writeValueAsString(requestBody);


            // 设置请求体
            StringEntity entity = new StringEntity(jsonBody);
            request.setEntity(entity);

            // 执行请求
            HttpResponse response = client.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());

                // 使用 Jackson 解析 JSON 获取 token
                JsonNode rootNode = objectMapper.readTree(responseBody);
                JsonNode dataNode = rootNode.get("data");
                if (dataNode != null) {
                    JsonNode tokenNode = dataNode.get("refreshToken");
                    if (tokenNode != null) {
                        token = tokenNode.asText();
                    }
                }
            }
        } catch (Exception e) {
            java.util.logging.Logger.getLogger("Exception").log(Level.SEVERE, "发生异常");
            CrawlerManager.taskFailParseRecord(1, "", "获取token失败", e.getMessage());
        }
        return token;
    }

    /**
     * 随机生成8位随机字符
     *
     * @return 8位随机字符
     */
    public static String randomCharacterGenerator() {
        StringBuilder randomChars = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 8; i++) {
            // 生成一个范围在65（A的ASCII码值）到90（Z的ASCII码值）或者97（a的ASCII码值）到122（z的ASCII码值）的随机整数
            int ascii = random.nextInt(26) + (random.nextBoolean() ? 65 : 97);
            randomChars.append((char) ascii);
        }
        return randomChars.toString();
    }

    /**
     * 随机生成13位随机数字
     *
     * @return 13位随机数字
     */
    public static String randomNumber() {
        StringBuilder randomNumbers = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 13; i++) {
            int ascii = random.nextInt(9) + 1;
            randomNumbers.append(ascii);
        }
        return randomNumbers.toString();
    }

    private static final Integer CHUNK_SIZE = 5242880;//5MB

    public static String getChunkVideoUrl(File file) {
//        String token = getToken(account, password);
        long fileSize = file.length();
        int chunkCount = (int) Math.ceil((double) fileSize / CHUNK_SIZE);
        String urlValue = "";

        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[8];  // 16 位十六进制字符串对应 8 个字节
        random.nextBytes(bytes);
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        String uuid = hexString.toString();

        try (InputStream fileInputStream = new FileInputStream(file)) {
            for (int index = 0; index < chunkCount; index++) {
                HttpClient client = HttpClientBuilder.create().build();
                HttpPost request = new HttpPost("http://***********:9100/file/sysFile/uploadChunk");
                request.setHeader("X-token", token);

                // 使用MultipartEntityBuilder构建form-data格式的请求体
                MultipartEntityBuilder builder = MultipartEntityBuilder.create();

                // 计算当前块的大小
                int currentChunkSize = (int) Math.min(CHUNK_SIZE, fileSize - index * CHUNK_SIZE);
                byte[] chunk = new byte[currentChunkSize];
                fileInputStream.read(chunk);

                // 添加文件部分到请求体
                builder.addBinaryBody("file", chunk, getContentTypeFromFileName(file.getName()), file.getName());
                // 添加其他参数
                builder.addTextBody("uuid", uuid);
                builder.addTextBody("fileName", file.getName());
                builder.addTextBody("index", String.valueOf(index));
                builder.addTextBody("total", String.valueOf(chunkCount));

                HttpEntity entity = builder.build();
                request.setEntity(entity);

                HttpResponse response = client.execute(request);
                int statusCode = response.getStatusLine().getStatusCode();
                System.out.println("第 " + index + " 块响应码: " + statusCode);

                if (statusCode == 200 && index == chunkCount - 1) {
                    String responseBody = EntityUtils.toString(response.getEntity());

                    // 使用Jackson解析JSON获取url
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode rootNode = objectMapper.readTree(responseBody);
                    JsonNode dataNode = rootNode.get("data");
                    if (dataNode != null) {
                        JsonNode urlNode = dataNode.get("url");
                        if (urlNode != null) {
                            urlValue = urlNode.asText();
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return urlValue;
    }

    public static String DownLoad(String url, String fileName, String referer) {
        // 修正URL格式
        if (url != null) {
            url = url.replace("\\", "/");
        }
        try {
            Path filePath = null;
            String fileNameWithoutExtension = getDirPath(fileName);
            String extension = url.substring(url.lastIndexOf("."));
            try {
                SSLContext sc = SSLContext.getInstance("SSL");
                sc.init(null, new TrustManager[]{new X509TrustManager() {
                    public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    }

                    public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    }

                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                }}, new java.security.SecureRandom());
                HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            } catch (Exception e) {
                log.error(e.getMessage());
            }

            // 增加连接超时时间或调整缓冲大小，来优化网络连接。
            System.setProperty("sun.net.client.defaultConnectTimeout", "60000");
            System.setProperty("sun.net.client.defaultReadTimeout", "60000");

            // 检查是否为base64格式的图片，base64格式图片单独下载
            if (url.contains("data:image")) {
                ByteArrayInputStream bais = null;
                try {
                    String urlTmp = url.split(",")[1];
                    String extra = url.split(",")[0];
                    String style = extra.substring(extra.lastIndexOf("/") + 1, extra.indexOf(";"));
                    extension = "." + style;
                    byte[] bytes = Base64.getDecoder().decode(urlTmp);
                    bais = new ByteArrayInputStream(bytes);
                    BufferedImage bufferedImage = ImageIO.read(bais);
                    fileName = fileNameWithoutExtension + extension;
                    File imageFile = new File(fileName);
                    ImageIO.write(bufferedImage, style, imageFile);

                    return fileName.replace("\\", "/");
                } catch (Exception e) {
                    throw new IOException(e.getMessage());
                } finally {
                    if (bais != null) {
                        bais.close();
                    }
                }
            }

            // 处理HTTP下载
            URL urlObj = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
            connection.setRequestMethod("GET");
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestProperty("Referer", referer);
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0");
            connection.setInstanceFollowRedirects(true);

            int responseCode = connection.getResponseCode();
            // TODO 处理http重定向（这里只处理了一次重定向的情况，后续有需要的话进行多次判断）
            while (responseCode >= 300 && responseCode <= 399) {
                String redirectUrl = connection.getHeaderField("Location");
                if (redirectUrl != null) {
                    urlObj = new URL(redirectUrl);
                    connection = (HttpURLConnection) urlObj.openConnection();
                    connection.setInstanceFollowRedirects(true);
                    connection.setRequestMethod("GET");
                    connection.setDoInput(true);
                    connection.setDoOutput(true);
                    connection.setRequestProperty("Referer", referer);
                    connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0");
                    responseCode = connection.getResponseCode();
                }
            }
            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream in = connection.getInputStream();

                filePath = Paths.get(fileNameWithoutExtension + extension);


                while (Files.exists(filePath)) {
                    log.info("目标文件已存在，你可以选择手动删除或者重新命名目标文件。");

                    String dirPathFileName = getDirPath(fileName);
                    filePath = Paths.get(dirPathFileName + extension);
                }

                Files.copy(in, filePath, StandardCopyOption.REPLACE_EXISTING);
                in.close();
            } else {
                throw new IOException("Failed to download file, server returned HTTP code: " + responseCode);
            }
            return filePath.toString().replace("\\", "/");
        } catch (Exception e) {
            log.error("下载失败{}，url为{}", e, url);
            return "";
        }
    }

    public static String getDirPath(
            String fileName
    ) {
        int year = LocalDate.now().getYear();
        int month = LocalDate.now().getMonthValue();
        int day = LocalDate.now().getDayOfMonth();
        String date = String.format("%04d%02d%02d", year, month, day);
        fileName = fileName + "\\" + date + "\\";
        FileUtil.createFolder(fileName);
        String fileSavePathPrefix = fileName + "\\";
        SecureRandom random = new SecureRandom();
        String fileFolderName = String.format("%08d", random.nextInt(10000000));
        return fileSavePathPrefix + fileFolderName;
    }


    /**
     * 绵阳市第三人民医院医生列表特殊处理
     * 先翻页收集所有页面，再逐个解析列表项和详情页数据
     *
     * @param driver  selenium浏览器驱动
     * @param request 初始页面的请求request，携带基本元素
     * @return 一个不需要再被处理的page
     */
    public static Page listDoctorsSpecial(
            RemoteWebDriver driver,
            Request request
    ) {
        CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
        TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);
        CrawlerManager.openAndSwitchTo(driver, request.getUrl());

        // 等待页面加载
        WebDriverWait wait = new WebDriverWait(driver, 30);
        wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getListItemTag())));
        Sleeper.sleep(3, TimeUnit.SECONDS);

        String sort = request.getExtra(RequestConstant.SORT);

        // 第一步：收集所有分页URL
        List<String> pageUrls = new ArrayList<>();
        pageUrls.add(driver.getCurrentUrl()); // 添加当前页

        // 硬编码总页数为76页（从HTML中可以看到最后一页是76）
        String baseUrl = request.getUrl();
        String urlPattern = baseUrl.substring(0, baseUrl.lastIndexOf("-") + 1);

        for (int pageNum = 2; pageNum <= 76; pageNum++) {
            pageUrls.add(urlPattern + pageNum + ".html");
        }

        // 第二步：遍历每个页面处理数据
        for (int pageIndex = 0; pageIndex < pageUrls.size(); pageIndex++) {
            String pageUrl = pageUrls.get(pageIndex);

            try {
                // 导航到目标页面
                if (!driver.getCurrentUrl().equals(pageUrl)) {
                    driver.get(pageUrl);
                    wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getListItemTag())));
                    Sleeper.sleep(2, TimeUnit.SECONDS);
                }

                // 滚动页面确保所有内容加载
                JavascriptExecutor js = (JavascriptExecutor) driver;
                js.executeScript("window.scrollTo(0, document.body.scrollHeight);");
                Sleeper.sleep(1, TimeUnit.SECONDS);
                js.executeScript("window.scrollTo(0, 0);");
                Sleeper.sleep(1, TimeUnit.SECONDS);

                // 获取页面源码并解析
                String pageSource = driver.getPageSource();
                Document document = Jsoup.parse(pageSource);

                // 获取所有列表项链接
                Elements listElements = document.select(taskTemplate.getListItemTag());

                // 处理每个列表项
                for (int itemIndex = 0; itemIndex < listElements.size(); itemIndex++) {
                    Element listElement = listElements.get(itemIndex);

                    // 更新排序信息
                    if (sort.split(" ").length <= 1) {
                        sort += " " + (pageIndex + 1);
                    } else {
                        sort = sort.substring(0, sort.indexOf(" ") + 1) + (pageIndex + 1);
                    }
                    if (sort.split(" ").length <= 2) {
                        sort += " " + (itemIndex + 1);
                    } else {
                        sort = sort.substring(0, sort.lastIndexOf(" ") + 1) + (itemIndex + 1);
                    }
                    request.putExtra(RequestConstant.SORT, sort);

                    // 存储最终信息的map
                    Map<String, String> dataItemMap = new HashMap<>();

                    // 元信息填充
                    CrawlerManager.fillMetaData(dataItemMap, collectTask, request, pageSource);
                    request.putExtra(RequestConstant.PAGE_ID, dataItemMap.get(NewsDataConstant.PAGE_ID));

                    // 解析列表项基本信息
                    Map<String, String> listItemData = CrawlerManager.fieldLocElementParse2Map(
                            taskTemplate.getListItemFieldLoc(), listElement);

                    // 特殊处理列表项数据
                    WebSpecialHanding specialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                    specialHanding.specialHandingForListItem(listItemData, collectTask, request);

                    // 获取详情页链接
                    String detailUrl = listElement.attr("abs:href");
                    if (detailUrl.isEmpty()) {
                        detailUrl = listElement.attr("href");
                        if (!detailUrl.startsWith("http")) {
                            detailUrl = "http://www.scmy120.com" + detailUrl;
                        }
                    }

                    // 访问详情页
                    String currentUrl = driver.getCurrentUrl();
                    driver.get(detailUrl);
                    Sleeper.sleep(3, TimeUnit.SECONDS);

                    try {
                        // 等待页面加载并尝试点击"更多"按钮
                        WebElement gengduoButton = null;
                        try {
                            gengduoButton = wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector(".gengduo")));
                            gengduoButton.click();
                            Sleeper.sleep(2, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            // 如果没有"更多"按钮，继续处理
                        }

                        // 获取详情页内容
                        String detailPageSource = driver.getPageSource();
                        Document detailDocument = Jsoup.parse(detailPageSource);

                        Map<String, String> detailData = new HashMap<>();

                        // 尝试从弹窗中获取完整简介
                        Element contentElement = detailDocument.select("#content").first();
                        String fullContent = "";

                        if (contentElement != null) {
                            fullContent = contentElement.text();
                        } else {
                            // 如果没有弹窗内容，尝试从页面中获取基本简介
                            Elements spanElements = detailDocument.select(".dcinfortext span");
                            if (!spanElements.isEmpty()) {
                                fullContent = spanElements.first().text();
                            }
                        }

                        // 提取专业技能
                        String goodat = extractAndStitchSkills(fullContent);

                        detailData.put("content", fullContent);
                        detailData.put("goodat", goodat);

                        // 特殊处理详情页数据
                        specialHanding.specialHandingForNotEmbedded(detailData, collectTask, request);

                        // 处理内容中的资源链接
                        if (!fullContent.isEmpty()) {
                            Document contextDom = Jsoup.parse(fullContent, detailUrl);
                            List<String> linkList = Stream.of(
                                            taskTemplate.getContentALoc(),
                                            taskTemplate.getContentImgLoc(),
                                            taskTemplate.getContentVideoLoc())
                                    .flatMap(loc -> contextDom.select(loc).stream().map(element1 -> {
                                        String attr = "";
                                        if (loc.equals(taskTemplate.getContentALoc())) {
                                            attr = element1.attr("abs:href");
                                        } else if (loc.equals(taskTemplate.getContentImgLoc()) ||
                                                loc.equals(taskTemplate.getContentVideoLoc())) {
                                            attr = element1.attr("abs:src");
                                        }
                                        attr = HttpClientUtils.ReEncodeHttpUrl(attr);
                                        return attr;
                                    }))
                                    .filter(url -> specialHanding.specialHandingForJudgeHttpUrl(url, collectTask))
                                    .distinct()
                                    .collect(Collectors.toList());

                            // 下载资源文件
                            Map<String, String> downloadMap = FileDataManager.downloadFromHttpUrl(
                                    linkList, detailUrl, request.getExtra(RequestConstant.PAGE_ID), collectTask);

                            // 更新内容中的链接
                            String updatedContent = fullContent;
                            for (Map.Entry<String, String> entry : downloadMap.entrySet()) {
                                if (FileUtil.localUrlChick(entry.getValue())) {
                                    updatedContent = updatedContent.replace(entry.getKey(), entry.getValue());
                                }
                            }
                            detailData.put("content", updatedContent);
                        }

                        // 整合所有数据
                        dataItemMap.putAll(listItemData);
                        if (detailData != null) {
                            detailData.forEach((key, value) -> {
                                String val = dataItemMap.get(key);
                                if (StringUtils.isBlank(val)) {
                                    dataItemMap.put(key, value);
                                }
                            });
                        }

                        // 将信息放入redis中
                        RedisUtil.getInstance().lLeftPush(
                                RedisConstant.DATA_ITEMS + collectTask.getId(),
                                JsonUtil.obj2String(dataItemMap));

                    } catch (Exception e) {
                        System.err.println("处理详情页失败: " + detailUrl + ", 错误: " + e.getMessage());
                    } finally {
                        // 返回列表页
                        try {
                            driver.get(currentUrl);
                            wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getListItemTag())));
                            Sleeper.sleep(1, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            // 如果返回失败，重新加载页面
                            driver.get(pageUrl);
                            wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(taskTemplate.getListItemTag())));
                            Sleeper.sleep(2, TimeUnit.SECONDS);
                        }
                    }
                }
            } catch (Exception e) {
                System.err.println("处理页面失败: " + pageUrl + ", 错误: " + e.getMessage());
                continue;
            }
        }

        return new Page();
    }

    /**
     * 提取专业技能的正则表达式（静态常量，提高性能）
     */
    private static final Pattern CAPTURE_PATTERN = Pattern.compile(
            "(\\b对[\\s\\S]+?(?:经验|研究|见解))|\\b(专业擅长|擅长|主治(?!医师|医生)|尤其对|尤其在|特别是)(?:于|[:：\\s])*([\\s\\S]+?)(?=\\s*。|,?\\s*(?:并|发表|主持|参与|承担|获得|主编|翻译|荣获|担任|著书立说)|\\b(?:专业擅长|擅长|主治(?!医师|医生)|尤其对|尤其在|特别是)|(?:\\s*\\n{2,})|$)"
    );

    private static final String CLEANUP_REGEX = ",?\\s*(?:对.+)?有(?:丰富|较好|扎实|一定|深入)[的]?(?:临床)?(?:经验|研究|见解)$";

    public static String extractAndStitchSkills(String singleProfileText) {
        if (singleProfileText == null || singleProfileText.trim().isEmpty()) {
            return "";
        }

        List<String> foundSkills = new ArrayList<>();
        Matcher matcher = CAPTURE_PATTERN.matcher(singleProfileText);

        while (matcher.find()) {
            String rawSkill = "";
            if (matcher.group(1) != null) {
                rawSkill = matcher.group(1);
            } else if (matcher.group(3) != null) {
                rawSkill = matcher.group(3);
            }

            if (!rawSkill.isEmpty()) {
                String cleanedSkill = rawSkill.replaceAll(CLEANUP_REGEX, "").trim();
                cleanedSkill = cleanedSkill.replaceAll("(?:尤其|特别是)$", "").trim();

                // --- 这是本次最关键的修复 ---
                // 在要清理的标点字符串中，加入了中文全角逗号 "，"
                String punctuationToRemove = ",；、， "; // 同时包含英文逗号, 和中文逗号，
                while (cleanedSkill.length() > 0 && punctuationToRemove.indexOf(cleanedSkill.charAt(cleanedSkill.length() - 1)) != -1) {
                    cleanedSkill = cleanedSkill.substring(0, cleanedSkill.length() - 1);
                }
                // --------------------------

                if (!cleanedSkill.isEmpty()) {
                    foundSkills.add(cleanedSkill);
                }
            }
        }
        return foundSkills.stream().collect(Collectors.joining("；"));
    }


    /**
     * 医疗安全事件报告系统帮助文档爬取特殊处理
     * 基于frameset结构的帮助文档，先解析左侧目录树，再逐个抓取内容页
     *
     * @param driver  selenium浏览器驱动
     * @param request 初始页面的请求request，携带基本元素
     * @return 一个不需要再被处理的page
     */
    public static Page helpDocumentSpecial(
            RemoteWebDriver driver,
            Request request
    ) {
        CollectTask collectTask = RedisGetManager.getCollectTask(request.getExtra(RequestConstant.COLLECT_TASK_ID));
        TaskTemplate taskTemplate = RedisGetManager.getTaskTemplate(collectTask);

        try {
            // 1. 打开主页面
            CrawlerManager.openAndSwitchTo(driver, request.getUrl());

            WebDriverWait wait = new WebDriverWait(driver, 30);
            Sleeper.sleep(3, TimeUnit.SECONDS);

            // 4. 获取基础URL用于拼接详情页链接
            String currentUrl = driver.getCurrentUrl();
            String baseUrl = currentUrl.replace("help.html", "help/");

            // 2. 切换到左侧frame解析目录结构
            driver.switchTo().frame("left");
            Sleeper.sleep(2, TimeUnit.SECONDS);

            String leftFrameSource = driver.getPageSource();


            // 3. 解析所有菜单项
            List<HelpMenuItem> menuItems = parseMenuItems(leftFrameSource);
            Map<Integer, HelpMenuItem> menuMap = buildMenuMap(menuItems);


            // 5. 切换回主frame
            driver.switchTo().defaultContent();

            String sort = request.getExtra(RequestConstant.SORT);
            int itemIndex = 0;

            // 6. 遍历所有有内容的菜单项
            for (HelpMenuItem item : menuItems) {
                // 跳过没有URL的目录项
                if (item.getUrl() == null || item.getUrl().isEmpty()) {
                    continue;
                }

                itemIndex++;

                try {
                    // 更新排序信息
                    String newSort = sort + " " + itemIndex;
                    request.putExtra(RequestConstant.SORT, newSort);

                    // 存储最终信息的map
                    Map<String, String> dataItemMap = new HashMap<>();

                    // 7. 构建层级标题
                    String hierarchicalTitle = buildHierarchicalTitle(item, menuMap);

                    // 8. 构建详情页URL
                    String detailUrl = baseUrl + item.getUrl();

                    // 9. 使用Jsoup直接获取详情页内容（避免frame问题）
                    try {
//                        Document detailDoc = Jsoup.connect(detailUrl)
//                                .timeout(30000) // 30秒超时
//                                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
//                                .get();
                        Connection.Response response = Jsoup.connect(detailUrl)
                                .timeout(30000)
                                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                                .ignoreContentType(true)  // 必须加，防止类型检测失败
                                .execute();

                        Document detailDoc = Jsoup.parse(
                                new ByteArrayInputStream(response.bodyAsBytes()),
                                "UTF-8",
                                detailUrl
                        );

                        // 10. 提取正文内容
                        Element bodyElement = detailDoc.body();
                        String content = "";
                        if (bodyElement != null) {
                            content = bodyElement.html();
                        }

                        // 11. 元信息填充
                        String detailPageSource = detailDoc.outerHtml();
                        CrawlerManager.fillMetaData(dataItemMap, collectTask, request, detailPageSource);
                        request.putExtra(RequestConstant.PAGE_ID, dataItemMap.get(NewsDataConstant.PAGE_ID));

                        // 12. 处理图片和其他资源
                        if (!content.isEmpty()) {
                            content = processContentResources(content, detailUrl, request, collectTask, taskTemplate);
                        }

                        // 13. 填充数据
                        dataItemMap.put("title", hierarchicalTitle);
                        dataItemMap.put("content", content);

                        // 14. 特殊处理
                        WebSpecialHanding specialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                        if (specialHanding != null) {
                            specialHanding.specialHandingForNotEmbedded(dataItemMap, collectTask, request);
                        }

                        // 15. 将信息放入redis中
                        RedisUtil.getInstance().lLeftPush(
                                RedisConstant.DATA_ITEMS + collectTask.getId(),
                                JsonUtil.obj2String(dataItemMap));

                        System.out.println("成功抓取: " + hierarchicalTitle);

                    } catch (IOException e) {
                        System.err.println("处理详情页失败: " + detailUrl + ", 错误: " + e.getMessage());
                    }
                // 注意：删除了所有frame切换操作，不再需要switchTo().defaultContent()
                } catch (Exception e) {
                    System.err.println("处理菜单项失败: " + item.getTitle() + ", 错误: " + e.getMessage());
                    // 确保切换回主frame
                    try {
                        driver.switchTo().defaultContent();
                    } catch (Exception ex) {
                        // 忽略切换异常
                    }
                    continue;
                }
            }

        } catch (Exception e) {
            System.err.println("爬取帮助文档失败: " + e.getMessage());
        }

        return new Page();
    }

    /**
     * 解析左侧frame源码中的菜单项
     */
    private static List<HelpMenuItem> parseMenuItems(String leftFrameSource) {
        List<HelpMenuItem> menuItems = new ArrayList<>();

        // 正则表达式匹配 d.add(id, parentId, "title", "url");
        Pattern pattern = Pattern.compile("d\\.add\\((\\d+),(-?\\d+),\"([^\"]+?)\",\"([^\"]*?)\"\\);");
        Matcher matcher = pattern.matcher(leftFrameSource);

        while (matcher.find()) {
            int id = Integer.parseInt(matcher.group(1));
            int parentId = Integer.parseInt(matcher.group(2));
            String title = matcher.group(3);
            String url = matcher.group(4);

            HelpMenuItem item = new HelpMenuItem(id, parentId, title, url);
            menuItems.add(item);
        }

        return menuItems;
    }

    /**
     * 构建菜单项的映射表
     */
    private static Map<Integer, HelpMenuItem> buildMenuMap(List<HelpMenuItem> menuItems) {
        Map<Integer, HelpMenuItem> menuMap = new HashMap<>();
        for (HelpMenuItem item : menuItems) {
            menuMap.put(item.getId(), item);
        }
        return menuMap;
    }

    /**
     * 构建层级标题
     */
    private static String buildHierarchicalTitle(HelpMenuItem item, Map<Integer, HelpMenuItem> menuMap) {
        List<String> titleParts = new ArrayList<>();
        HelpMenuItem current = item;

        // 从当前项向上追溯到根节点
        while (current != null && current.getParentId() != -1) {
            titleParts.add(0, current.getTitle()); // 插入到开头保持正确顺序
            current = menuMap.get(current.getParentId());
        }

        // 如果有根节点也添加进去
        if (current != null && current.getParentId() == -1) {
            titleParts.add(0, current.getTitle());
        }

        return String.join(" > ", titleParts);
    }

    /**
     * 处理内容中的资源文件（图片等）- 修复版本
     */
    private static String processContentResources(String content, String baseUrl, Request request,
                                                  CollectTask collectTask, TaskTemplate taskTemplate) {
        try {
            Document contentDoc = Jsoup.parse(content, baseUrl);

            // 获取所有需要下载的资源链接，同时保存原始路径映射
            List<String> linkList = new ArrayList<>();
            Map<String, String> originalToAbsoluteMap = new HashMap<>(); // 原始路径 -> 绝对路径

            // 处理图片
            Elements imgElements = contentDoc.select("img");
            for (Element img : imgElements) {
                String originalSrc = img.attr("src");           // 原始路径：./img01/1.3.jpg
                String absoluteSrc = img.attr("abs:src");       // 绝对路径：https://...

                if (!absoluteSrc.isEmpty()) {
                    absoluteSrc = HttpClientUtils.ReEncodeHttpUrl(absoluteSrc);
                    linkList.add(absoluteSrc);
                    originalToAbsoluteMap.put(originalSrc, absoluteSrc);
                }
            }

            // 处理其他资源（如果taskTemplate中有配置）
            if (taskTemplate.getContentALoc() != null) {
                Elements aElements = contentDoc.select(taskTemplate.getContentALoc());
                for (Element a : aElements) {
                    String originalHref = a.attr("href");
                    String absoluteHref = a.attr("abs:href");
                    if (!absoluteHref.isEmpty()) {
                        absoluteHref = HttpClientUtils.ReEncodeHttpUrl(absoluteHref);
                        linkList.add(absoluteHref);
                        originalToAbsoluteMap.put(originalHref, absoluteHref);
                    }
                }
            }

            if (taskTemplate.getContentVideoLoc() != null) {
                Elements videoElements = contentDoc.select(taskTemplate.getContentVideoLoc());
                for (Element video : videoElements) {
                    String originalSrc = video.attr("src");
                    String absoluteSrc = video.attr("abs:src");
                    if (!absoluteSrc.isEmpty()) {
                        absoluteSrc = HttpClientUtils.ReEncodeHttpUrl(absoluteSrc);
                        linkList.add(absoluteSrc);
                        originalToAbsoluteMap.put(originalSrc, absoluteSrc);
                    }
                }
            }

            // 过滤重复链接
            linkList = linkList.stream()
                    .distinct()
                    .filter(url -> {
                        WebSpecialHanding specialHanding = RedisGetManager.getWebSpecialHanding(collectTask);
                        return specialHanding == null || specialHanding.specialHandingForJudgeHttpUrl(url, collectTask);
                    })
                    .collect(Collectors.toList());

            if (!linkList.isEmpty()) {
                // 下载资源文件 (绝对路径 -> 本地路径)
                Map<String, String> downloadMap = FileDataManager.downloadFromHttpUrl(
                        linkList, baseUrl, request.getExtra(RequestConstant.PAGE_ID), collectTask);

                // 替换内容中的链接为本地路径 - 修复版本
                String updatedContent = content;
                for (Map.Entry<String, String> originalToAbsolute : originalToAbsoluteMap.entrySet()) {
                    String originalPath = originalToAbsolute.getKey();    // ./img01/1.3.jpg
                    String absolutePath = originalToAbsolute.getValue();  // https://...
                    String localPath = downloadMap.get(absolutePath);     // C:/Collect_Data/...

                    if (localPath != null && FileUtil.localUrlChick(localPath)) {
                        // 用原始路径替换为本地路径
                        updatedContent = updatedContent.replace(originalPath, localPath);
                    }
                }
                return updatedContent;
            }

        } catch (Exception e) {
            System.err.println("处理资源文件失败: " + e.getMessage());
        }

        return content;
    }

    /**
     * 帮助文档菜单项数据类
     */
    static class HelpMenuItem {
        private int id;
        private int parentId;
        private String title;
        private String url;

        public HelpMenuItem(int id, int parentId, String title, String url) {
            this.id = id;
            this.parentId = parentId;
            this.title = title;
            this.url = url;
        }

        // Getters and Setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }

        public int getParentId() { return parentId; }
        public void setParentId(int parentId) { this.parentId = parentId; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
    }
}



