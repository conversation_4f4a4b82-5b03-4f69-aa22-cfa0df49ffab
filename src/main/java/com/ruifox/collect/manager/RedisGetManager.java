package com.ruifox.collect.manager;

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.*;
import com.ruifox.collect.designmode.strategy.WebSpecialHanding;
import com.ruifox.collect.module.entity.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import com.ruifox.collect.designmode.factory.WebSpecialHandingFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

@Slf4j
@Data
public class RedisGetManager {

    /**
     * 清除redis缓存
     */
    public static void deleteRedisById(
            int collectTaskId
    ) {
        RedisUtil redisUtil = RedisUtil.getInstance();
        Set<String> keys = redisUtil.keys("*_" + collectTaskId);
        redisUtil.delete(keys);
        log.info("已清除redis缓存，ID："+collectTaskId);
    }

    /**
     * 采集任务检查，相关信息存入redis
     */
    public static CollectTask checkCollectTask(
            int collectTaskId
    ) {
        // 采集任务
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        if (collectTask == null) {
            throw new RuntimeException("采集任务为null，ID：" + collectTaskId);
        }
        // 任务类型
        TaskType taskType = ApplicationContextProvider.getBeanByType(TaskTypeMapper.class).selectById(collectTask.getTypeId());
        if(taskType == null) {
            throw new RuntimeException("任务类型为null，ID：" + collectTask.getTypeId());
        }
        // 任务对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());
        if (taskObject == null) {
            throw new RuntimeException("任务对象为null，ID：" + collectTask.getObjectId());
        }
        // 任务模板
        TaskTemplate taskTemplate = ApplicationContextProvider.getBeanByType(TaskTemplateMapper.class).selectById(collectTask.getTemplateId());
        if (taskTemplate == null) {
            throw new RuntimeException("任务模板为null，ID：" + collectTask.getTemplateId());
        }
        // cookie
        TaskCookie taskCookie = null;
        if (collectTask.getCookieId() != 0) {
            taskCookie = ApplicationContextProvider.getBeanByType(TaskCookieMapper.class).selectById(collectTask.getCookieId());
            if (taskCookie == null) {
                throw new RuntimeException("cookie为null，ID：" + collectTask.getCookieId());
            }
        }
        // 特殊处理类
        WebSpecialHanding specialHanding = WebSpecialHandingFactory.getSpecialHanding(collectTask);
        if(specialHanding == null) {
            throw new RuntimeException("特殊处理类为null，beanFlag：" + collectTask.getHost()+"-"+collectTask.getObjectId());
        }

        // 清除redis相关缓存，重新存入缓存
        RedisGetManager.deleteRedisById(collectTaskId);

        RedisUtil.getInstance().set(RedisConstant.COLLECT_TASK+collectTaskId, JsonUtil.obj2String(collectTask));
        RedisUtil.getInstance().set(RedisConstant.TASK_TYPE+collectTaskId, JsonUtil.obj2String(taskType));
        RedisUtil.getInstance().set(RedisConstant.TASK_OBJECT+collectTaskId, JsonUtil.obj2String(taskObject));
        RedisUtil.getInstance().set(RedisConstant.TASK_TEMPLATE+collectTaskId, JsonUtil.obj2String(taskTemplate));
        RedisUtil.getInstance().set(RedisConstant.TASK_COOKIE+collectTaskId, JsonUtil.obj2String(taskCookie));
        log.info("已经加入采集相关信息至缓存，ID：{}", collectTask.getId());

        return collectTask;
    }

    /**
     * 获取采集任务
     */
    public static CollectTask getCollectTask(
            int collectTaskID
    ) {
        CollectTask collectTask = null;
        String collectTaskStr = RedisUtil.getInstance().get(RedisConstant.COLLECT_TASK+collectTaskID);
        if (StringUtils.isBlank(collectTaskStr)) {
            collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskID);
            if (collectTask == null) {
                throw new RuntimeException("获取采集任务失败，ID：" + collectTaskID);
            }
            RedisUtil.getInstance().set(RedisConstant.COLLECT_TASK+collectTaskID, JsonUtil.obj2String(collectTask));
        } else {
            collectTask = JsonUtil.Json2Obj(collectTaskStr, CollectTask.class);
        }
        return collectTask;
    }

    /**
     * 获取任务类型
     */
    public static TaskType getTaskType(
            CollectTask collectTask
    ) {
        int collectTaskID = collectTask.getId();
        TaskType taskType = null;
        String taskTypeStr = RedisUtil.getInstance().get(RedisConstant.TASK_TYPE+collectTaskID);
        if (StringUtils.isBlank(taskTypeStr)) {
            taskType = ApplicationContextProvider.getBeanByType(TaskTypeMapper.class).selectById(collectTask.getTypeId());
            if (taskType == null) {
                throw new RuntimeException("获取任务类型失败，类型ID：" + collectTask.getTypeId());
            }
            RedisUtil.getInstance().set(RedisConstant.TASK_TYPE+collectTaskID, JsonUtil.obj2String(taskType));
        } else {
            taskType = JsonUtil.Json2Obj(taskTypeStr, TaskType.class);
        }
        return taskType;
    }

    /**
     * 获取任务对象
     */
    public static TaskObject getTaskObject(
            CollectTask collectTask
    ) {
        int collectTaskID = collectTask.getId();
        TaskObject taskObject = null;
        String taskObjectStr = RedisUtil.getInstance().get(RedisConstant.TASK_OBJECT+collectTaskID);
        if (StringUtils.isBlank(taskObjectStr)) {
            taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());
            if (taskObject == null) {
                throw new RuntimeException("获取任务对象失败，对象ID：" + collectTask.getObjectId());
            }
            RedisUtil.getInstance().set(RedisConstant.TASK_OBJECT+collectTaskID, JsonUtil.obj2String(taskObject));
        } else {
            taskObject = JsonUtil.Json2Obj(taskObjectStr, TaskObject.class);
        }
        return taskObject;
    }

    /**
     * 获取任务模板
     */
    public static TaskTemplate getTaskTemplate(
            CollectTask collectTask
    ) {
        int collectTaskID = collectTask.getId();
        TaskTemplate taskTemplate = null;
        String taskTemplateStr = RedisUtil.getInstance().get(RedisConstant.TASK_TEMPLATE+collectTaskID);
        if (StringUtils.isBlank(taskTemplateStr)) {
            taskTemplate = ApplicationContextProvider.getBeanByType(TaskTemplateMapper.class).selectById(collectTask.getTemplateId());
            if (taskTemplate == null) {
                throw new RuntimeException("获取任务模板失败，模板ID：" + collectTask.getTemplateId());
            }
            RedisUtil.getInstance().set(RedisConstant.TASK_TEMPLATE+collectTaskID, JsonUtil.obj2String(taskTemplate));
        } else {
            taskTemplate = JsonUtil.Json2Obj(taskTemplateStr, TaskTemplate.class);
        }
        return taskTemplate;
    }

    /**
     * 获取cookie
     */
    public static TaskCookie getTaskCookie(
            CollectTask collectTask
    ) {
        if (collectTask.getCookieId() == null || collectTask.getCookieId() == 0) {
            return null;
        }
        int collectTaskID = collectTask.getId();
        TaskCookie taskCookie = null;
        String taskCookieStr = RedisUtil.getInstance().get(RedisConstant.TASK_COOKIE+collectTaskID);
        if (StringUtils.isBlank(taskCookieStr)) {
            taskCookie = ApplicationContextProvider.getBeanByType(TaskCookieMapper.class).selectById(collectTask.getCookieId());
            if (taskCookie == null) {
                throw new RuntimeException("获取cookie失败，cookieID：" + collectTask.getCookieId());
            }
            RedisUtil.getInstance().set(RedisConstant.TASK_COOKIE+collectTaskID, JsonUtil.obj2String(taskCookie));
        } else {
            taskCookie = JsonUtil.Json2Obj(taskCookieStr, TaskCookie.class);
        }
        return taskCookie;
    }

    /**
     * 获取特殊处理类
     */
    public static WebSpecialHanding getWebSpecialHanding(
            CollectTask collectTask
    ) {
        WebSpecialHanding specialHanding = WebSpecialHandingFactory.getSpecialHanding(collectTask);
        if (specialHanding == null) {
            throw new RuntimeException("获取特殊处理类失败，beanFlag：" + collectTask.getHost()+"-"+collectTask.getObjectId());
        }
        return specialHanding;
    }
}
