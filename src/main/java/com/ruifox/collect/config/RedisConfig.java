package com.ruifox.collect.config;

import com.ruifox.collect.system.ConfigUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

@Configuration
public class RedisConfig {

    /**
     * 注入JedisPool至IOC容器中
     * @return JedisPool
     */
    @Bean
    public JedisPool redisPoolFactory() {

        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();

        jedisPoolConfig.setMaxIdle(Integer.parseInt(ConfigUtil.getProperties("spring.redis.jedis.pool.max-idle")));
        jedisPoolConfig.setMaxWaitMillis(Long.parseLong(ConfigUtil.getProperties("spring.redis.jedis.pool.max-wait")));

        return new JedisPool(
                jedisPoolConfig,
                ConfigUtil.getProperties("spring.redis.host"),
                Integer.parseInt(ConfigUtil.getProperties("spring.redis.port")),
                Integer.parseInt(ConfigUtil.getProperties("spring.redis.timeout")),
                ConfigUtil.getProperties("spring.redis.password"));
    }

}