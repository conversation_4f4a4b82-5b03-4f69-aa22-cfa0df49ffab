package com.ruifox.collect.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruifox.collect.module.entity.news.NewsTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NewsTableMapper extends BaseMapper<NewsTable> {
    List<NewsTable> selectByPageId(@Param("tableName") String tableName, @Param("pageId") String pageId);

    int updateById(@Param("tableName") String tableName, @Param("newsTable") NewsTable newsTable);
}
