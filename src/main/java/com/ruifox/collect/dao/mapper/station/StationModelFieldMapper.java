package com.ruifox.collect.dao.mapper.station;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruifox.collect.module.entity.station.StationModelField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface StationModelFieldMapper extends BaseMapper<StationModelField> {
    Map<Object, Object> selectByModelIdWithDocPosition(@Param("modelId") Integer modelId);

    /**
     * 根据model_id和hos_position查询字段表中的某条记录,院内职务(院长、科主任等)
     * @param modelId 模型id (新闻/医生/图集等)
     * @return 字段记录
     */
    Map<Object, Object> selectByModelIdWithHosPosition(@Param("modelId") Integer modelId);

    /**
     * 根据model_id和edu_position查询字段表中的某条记录,教务职称(教授、副教授等)
     * @param modelId 模型id (新闻/医生/图集等)
     * @return 字段记录
     */
    Map<Object, Object> selectByModelIdWithEduPosition(@Param("modelId") Integer modelId);

    /**
     * 根据model_id和edy_post查询字段表中的某条记录,教学岗位(博士生导师、硕士研究生导师)
     * @param modelId 模型id (新闻/医生/图集等)
     * @return 字段记录
     */
    Map<Object, Object> selectByModelIdWithEduPost(@Param("modelId") Integer modelId);
    /**
     * 根据model_id和edy_post查询字段表中的某条记录,教学岗位(博士生导师、硕士研究生导师)
     * @param modelId 模型id (新闻/医生/图集等)
     * @return 字段记录
     */
    Map<Object, Object> selectByModelIdWithPartyPosition(@Param("modelId") Integer modelId);
    /**
     * 根据model_id和edy_post查询字段表中的某条记录,教学岗位(博士生导师、硕士研究生导师)
     * @param modelId 模型id (新闻/医生/图集等)
     * @return 字段记录
     */
    Map<Object, Object> selectByModelIdWithLeaderPosition(@Param("modelId") Integer modelId);
}
