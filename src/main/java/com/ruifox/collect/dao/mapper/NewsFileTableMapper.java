package com.ruifox.collect.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruifox.collect.module.entity.news.NewsFileTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NewsFileTableMapper extends BaseMapper<NewsFileTable> {
    int insertNewsFile(@Param("tableName") String tableName, @Param("newsFile") NewsFileTable newsFile);

    List<NewsFileTable> selectByPageId(@Param("tableName") String tableName, @Param("pageId") String pageId);

    int updateById(@Param("tableName") String tableName, @Param("newsFile") NewsFileTable newsFile);
}
