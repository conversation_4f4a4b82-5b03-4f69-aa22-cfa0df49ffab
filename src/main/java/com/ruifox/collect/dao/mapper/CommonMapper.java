package com.ruifox.collect.dao.mapper;

import com.ruifox.collect.dao.SpiceBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface CommonMapper<T,ID> extends SpiceBaseMapper<T,ID> {

    /**
     * 动态建表
     * @param tableName 表名
     * @param fields key -> 字段名 ; value -> 字段类型
     */
    void createDynamicTable(@Param("tableName") String tableName, @Param("fields") Map<String, String> fields);

    /**
     * 单条文件数据落库
     * @param fileTableName 文件表名
     * @param dataMap 单条文件数据
     */
    void insert(@Param("fileTableName") String fileTableName,@Param("dataMap") Map<Object,Object> dataMap);

    /**
     * 批量数据落库
     * @param tableName 表名
     * @param records 多条数据
     */
    void insertBatch(@Param("tableName") String tableName , @Param("records") List<Map<Object,Object>> records);

    /**
     * 按表名查出所有数据并排序
     * @param tableName 表名
     * @return 排序好的所有数据
     */
    List<Map<Object, Object>> selectByTableNameOrderBySortDesc(@Param("tableName") String tableName);

    List<Map<Object, Object>> selectByTableNameOrderBySortAsc(@Param("tableName") String tableName);

    /**
     * 按表名查出所有数据
     * @param tableName 表名
     * @return 所有数据
     */
    List<Map<Object,Object>> selectByTableName(@Param("tableName") String tableName);

    /**
     * 按表名查出所有数据并按照发布时间排序
     * @param tableName 表名
     * @return 排序好的所有数据
     */
    List<Map<Object, Object>> selectByTableNameOrderByReleased(@Param("tableName") String tableName);

    /**
     * 查出某表按发布时间排序的最新一条数据
     * @param tableName 表名
     * @return 按发布时间排序的最新一条数据
     */
    Map<Object, Object> selectByTableNameLatest(@Param("tableName") String tableName);

    /**
     * 根据pageId查询文件数据
     * @param tableName 表名
     * @param pageId 页面id
     * @return 标题集合
     */
    List<Map<Object, Object>> selectFileRecodeByPageIdAnd200(@Param("tableName") String tableName, @Param("pageId") String pageId);


    /**
     * 根据指定表名插入单条数据
     * @param tableName 表名
     * @param tableInfo 待插入数据
     */
    void insertByTabeleName(@Param("tableName") String tableName, @Param("tableInfo") Map<Object, Object> tableInfo);

    /**
     * 根据指定表名和ID修改指定属性集合
     * @param tableName 表名
     * @param ID 主键ID
     * @param fields 待修改的属性集合
     */
    void updatePropertyByTableNameAndID(@Param("tableName") String tableName, @Param("ID") Integer ID, @Param("fields") Map<Object, Object> fields);

    /**
     * 查询老医院的医生信息
     */
    List<Map<Object,Object>> selectOldDoctor(@Param("catId") Integer catId);

    List<Map<String,Object>> selectDoctorThumb(@Param("tableName") String tableName);
}
