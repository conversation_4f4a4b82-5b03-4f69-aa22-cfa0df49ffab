package com.ruifox.collect.service;

import com.ruifox.collect.common.results.ResponseResult;

import java.util.List;

public interface ImportService {
    ResponseResult<?> singleImport(Integer collectTaskId);

    ResponseResult<?> multiImport(List<Integer> collectTaskIds);

    ResponseResult<?> continuousImport(Integer startCollectTaskId, Integer endCollectTaskId);

    ResponseResult<?> javaImport(Integer collectTaskId);

    ResponseResult<?> continuousJavaImport(Integer startCollectTaskId, Integer endCollectTaskId);

}
