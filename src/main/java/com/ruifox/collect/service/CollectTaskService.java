package com.ruifox.collect.service;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.module.dto.CollectTaskDTO;
import com.ruifox.collect.module.dto.TemplateDTO;

public interface CollectTaskService  {

    // TODO  创建任务模板
    ResponseResult<?> createTaskTemplate(TemplateDTO templateDTO);

    // TODO 创建采集任务
    ResponseResult<?> createCollectTask(CollectTaskDTO collectTaskDTO);

    // TODO 启动采集任务
    ResponseResult<?> startCollectTask(Integer collectTaskId);

    // TODO 启动连续采集任务
    ResponseResult<?> startContinuousCollectTask(Integer beginCollectTaskId, Integer endCollectTaskId);

    // TODO 手动落库
    ResponseResult<?> manualPersistence(Integer collectTaskId);

}
