package com.ruifox.collect.service.impl;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.designmode.factory.ImportTypeFactory;
import com.ruifox.collect.designmode.factory.ImportTypeJavaFactory2;
import com.ruifox.collect.designmode.strategy.ImportType;
import com.ruifox.collect.designmode.strategy.ImportTypeJava2;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.service.ImportService2;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Getter
@Slf4j
public class importServiceImpl2 implements ImportService2 {

    List<Integer> failedCollectTaskIds = new ArrayList<>();

    @Override
    public ResponseResult<?> singleImport(Integer collectTaskId) {
        try {
            // TODO 拿到对应导入类型的通用处理方法
            CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
            ImportType importType = ImportTypeFactory.getImportType(collectTask);
            importType.singleImport(collectTask);
            return ResponseResult.success("单任务导入成功！任务ID："+collectTask.getId());
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "单任务导入失败", e.getMessage());
            failedCollectTaskIds.add(collectTaskId);
            return ResponseResult.fail("单任务导入失败，原因："+e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> multiImport(List<Integer> collectTaskIds) {
        for (Integer collectTaskId : collectTaskIds) {
            this.singleImport(collectTaskId);
        }
        // FIXME 这里应该添加导入记录，哪些导入成功，哪些导入失败。（目前只能从解析记录表中看到单任务导入失败的记录
        return ResponseResult.success("多任务导入运行完成！");
    }

    @Override
    public ResponseResult<?> continuousImport(Integer startCollectTaskId, Integer endCollectTaskId) {
        failedCollectTaskIds = new ArrayList<>();
          for (int collectTaskId = startCollectTaskId; collectTaskId <= endCollectTaskId; collectTaskId++) {
              this.singleImport(collectTaskId);
          }
        return ResponseResult.success("连续任务导入运行完成！\n"+"失败任务ID："+failedCollectTaskIds.toString());
    }

    @Override
    public ResponseResult<?> javaImport(Integer collectTaskId) {
        try {
            // TODO 拿到对应导入类型的通用处理方法
            CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
            ImportTypeJava2 importTypeJava = ImportTypeJavaFactory2.getImportType(collectTask);
            importTypeJava.singleImport(collectTask);
            return ResponseResult.success("单任务导入成功！任务ID："+collectTask.getId());
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "单任务导入失败", e.getMessage());
            return ResponseResult.fail("单任务导入失败，原因："+e.getMessage());
        }
    }
    @Override
    public ResponseResult<?> continuousJavaImport(Integer startCollectTaskId, Integer endCollectTaskId) {
        failedCollectTaskIds = new ArrayList<>();
        for (int collectTaskId = startCollectTaskId; collectTaskId <= endCollectTaskId; collectTaskId++) {
            try {
                // TODO 拿到对应导入类型的通用处理方法
                CollectTask collectTask = RedisGetManager.getCollectTask(collectTaskId);
                ImportTypeJava2 importTypeJava = ImportTypeJavaFactory2.getImportType(collectTask);
                importTypeJava.singleImport(collectTask);
            } catch (Exception e) {
                CrawlerManager.taskFailParseRecord(collectTaskId, "", "单任务导入失败", e.getMessage());
                failedCollectTaskIds.add(collectTaskId);
            }
        }
        return ResponseResult.success("连续任务导入运行完成！\n"+"失败任务ID："+failedCollectTaskIds.toString());
    }
}
