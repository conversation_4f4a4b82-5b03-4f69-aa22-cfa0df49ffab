package com.ruifox.collect.service.impl;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.NewsTableMapper;
import com.ruifox.collect.dao.mapper.TaskDataMapper;
import com.ruifox.collect.dao.mapper.TaskFileFailedMapper;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskData;
import com.ruifox.collect.module.entity.TaskFileFailed;
import com.ruifox.collect.service.ExcelInfoService;
import com.ruifox.collect.system.ApplicationContextProvider;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.util.List;

@Service
@Getter
@Slf4j
public class ExcelInfoServiceImpl implements ExcelInfoService {
    @Override
    public ResponseResult<?> importExcelInfo() {
        TaskFileFailedMapper taskFileFailedMapper = ApplicationContextProvider.getBeanByType(TaskFileFailedMapper.class);
        TaskDataMapper taskDataMapper = ApplicationContextProvider.getBeanByType(TaskDataMapper.class);
        List<TaskFileFailed> taskFileFaileds = taskFileFailedMapper.selectList(null);
        List<TaskData> taskDatas = taskDataMapper.selectList(null);
        NewsTableMapper newsTableMapper = ApplicationContextProvider.getBeanByType(NewsTableMapper.class);

        // 将工作簿写入文件
        String path = "C:\\Users\\<USER>\\Desktop\\采集数据统计.xlsx";

        String result = "";
        try (Workbook workbook = new XSSFWorkbook();
             // 将工作簿写入文件
             FileOutputStream outputStream = new FileOutputStream(path);
        ) {
            Sheet collectDataSheet = workbook.createSheet("采集栏目数据");
            Sheet fileFailedSheet = workbook.createSheet("文件下载失败数据");
            int index = 0;
            for (TaskData taskData : taskDatas) {
                CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(taskData.getCollectTaskId());
                if (index == 0) {
                    Row row0 = collectDataSheet.createRow(0);
                    row0.createCell(0).setCellValue("栏目名称");
                    row0.createCell(1).setCellValue("采集数量");
                    row0.createCell(2).setCellValue("文件下载成功量");
                    row0.createCell(3).setCellValue("文件下载失败量");
                }
                Row row = collectDataSheet.createRow(++index);
                row.createCell(0).setCellValue(collectTask.getImportCategoryName());
                row.createCell(1).setCellValue(taskData.getDataSize());
                row.createCell(2).setCellValue(taskData.getFileSuccessSize());
                row.createCell(3).setCellValue(taskData.getFileFailedSize());
            }
            index = 0;
            for (TaskFileFailed taskFileFailed : taskFileFaileds) {
                CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(taskFileFailed.getCollectTaskId());
                if (index == 0) {
                    Row row0 = fileFailedSheet.createRow(0);
                    row0.createCell(0).setCellValue("栏目名称");
                    row0.createCell(1).setCellValue("标题");
                    row0.createCell(2).setCellValue("原网站地址");
                    row0.createCell(3).setCellValue("下载失败文件地址");
                }
                Row row = fileFailedSheet.createRow(++index);
                row.createCell(0).setCellValue(collectTask.getImportCategoryName());
                row.createCell(1).setCellValue(newsTableMapper.selectByPageId(collectTask.getDataTableName(), taskFileFailed.getPageId()).get(0).getTitle());
                row.createCell(2).setCellValue(taskFileFailed.getUrl());
                row.createCell(3).setCellValue(taskFileFailed.getFilePath());
            }
            workbook.write(outputStream);
            result = "Excel 文件生成成功！";
        } catch (Exception e) {
            result = "Excel 文件生成失败！\n" + "原因为:" + e.getMessage();
            return ResponseResult.fail(result);
        }
        return ResponseResult.success(result);
    }
}
