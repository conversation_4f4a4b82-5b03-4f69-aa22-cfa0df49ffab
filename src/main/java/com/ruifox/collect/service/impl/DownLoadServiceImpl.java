package com.ruifox.collect.service.impl;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.service.DownLoadService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Getter
@Slf4j
public class DownLoadServiceImpl implements DownLoadService {
    @Override
    public ResponseResult<?> downloadFileAgain(Integer collectTaskId) {
        // TODO 启动采集任务
        try {
            String result = CrawlerManager.DownloadAgain(collectTaskId);
            return ResponseResult.success("二次下载启动成功，ID: " + collectTaskId + "，结果是：" + result);
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "二次下载启动失败", e.getMessage());
            return ResponseResult.fail("二次下载启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }
}
