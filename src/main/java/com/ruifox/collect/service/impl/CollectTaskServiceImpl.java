package com.ruifox.collect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruifox.collect.common.constants.ConfigConstant;
import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.common.enums.TaskStatus;
import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.crawler.MySpider;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.dao.mapper.TaskTemplateMapper;
import com.ruifox.collect.designmode.singeton.CollectTaskQueue;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.dto.CollectTaskDTO;
import com.ruifox.collect.module.dto.TemplateDTO;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.module.entity.TaskTemplate;
import com.ruifox.collect.service.CollectTaskService;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import com.ruifox.collect.util.Sleeper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.net.URL;
import java.time.LocalDate;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Getter
@Slf4j
public class CollectTaskServiceImpl extends ServiceImpl<CollectTaskMapper, CollectTask> implements CollectTaskService {

    @Autowired
    TaskTemplateMapper taskTemplateMapper;


    @Override
    public ResponseResult<?> createTaskTemplate(TemplateDTO templateDTO) {

        // FIXME
        try {
            TaskTemplate taskTemplate = BeanUtil.copyProperties(templateDTO, TaskTemplate.class);
            int result = taskTemplateMapper.insert(taskTemplate);
            if (result > 0) {
                return ResponseResult.success("创建任务模板成功！模板ID: " + taskTemplate.getId());
            } else {
                return ResponseResult.fail("创建任务模板失败！");
            }
        } catch (Exception e) {
            log.error("创建任务模板异常", e);
            return ResponseResult.fail("创建任务模板异常，原因：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResponseResult<?> createCollectTask(CollectTaskDTO collectTaskDTO) {
        // TODO 创建采集任务
        // NOTE 关于excel等文件的采集，因为经常需要跟据要求进行特定处理，所以抽离了一个工具类ExcelUtil，这里默认不会执行excel采集
        try {
            // 1.属性拷贝,
            // 采集任务
            CollectTask collectTask = BeanUtil.copyProperties(collectTaskDTO, CollectTask.class);


            // 2.检查采集对象是否存在
            TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());
            // FIXME 这里应该对采集用到的其它信息（如采集类型、解析模板、cookie、特殊处理类）也进行校验，后续有时间再改

            // 3.补足信息
            collectTask.setTaskStatus(TaskStatus.WAITING.getCode());
            //getHost()是获取url的主机名，如www.baidu.com，返回baidu
            collectTask.setHost(new URL(collectTask.getTargetUrl()).getHost());

            // 4.采集任务第一次落库
            // NOTE 目前的处理逻辑是先落一次库（保证基本数据保存），再进行动态建表、建文件夹，然后再修改数据库中对应属性
            CollectTaskMapper collectTaskMapper = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class);
            collectTaskMapper.insert(collectTask);

            // 5.动态建表
            CommonMapper commonMapper = ApplicationContextProvider.getBeanByType(CommonMapper.class);
            collectTask.setDataTableName(collectTask.getHost()
                    .replaceAll("(www.|.com|.org|.scu|.edu|.cn|.net|cn.)", "")
                    .replace(".", "_")
                    .replace("-", "_") + "_" + taskObject.getDataTableSuffix() + "_" + collectTask.getId());
            collectTask.setFileTableName(collectTask.getHost()
                    .replaceAll("(www.|.com|.org|.scu|.edu|.cn|.net|cn.)", "")
                    .replace(".", "_")
                    .replace("-", "_") + "_" + taskObject.getFileTableSuffix() + "_" + collectTask.getId());
            commonMapper.createDynamicTable(collectTask.getDataTableName(), JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class));
            commonMapper.createDynamicTable(collectTask.getFileTableName(), JsonUtil.Json2Obj(taskObject.getFileTableFields(), Map.class));

            // 6.创建保存文件夹
            LocalDate localDate = LocalDate.now();

            String year = String.format("%02d", localDate.getYear());
            String month = String.format("%02d", localDate.getMonthValue());
            String day = String.format("%02d", localDate.getDayOfMonth());
            String fileFolderName = Integer.valueOf(year + month + day) + collectTask.getId() + "";
            collectTask.setFileFolderName(fileFolderName);
            String fileSavePathPrefix = ConfigUtil.getProperties(ConfigConstant.SAVE_PATH) + collectTask.getHost() + "/";
            File fileFolder = new File(fileSavePathPrefix + fileFolderName);
            if (!fileFolder.exists()) {
                if (!fileFolder.mkdirs()) {
                    return ResponseResult.fail("创建保存文件夹失败，任务ID：" + collectTask.getId());
                }
            }
            // 7.更新采集任务
            collectTaskMapper.updateById(collectTask);

            // 8.返回结果
            return ResponseResult.success("创建采集任务成功！任务ID: " + collectTask.getId());
        } catch (Exception e) {
            return ResponseResult.fail("创建采集任务失败，原因：" + e.getMessage());
        }
    }


    @Override
    @Transactional
    public ResponseResult<?> startCollectTask(Integer collectTaskId) {
        // TODO 启动采集任务
        try {
            // 1.检查是否有正在执行的采集任务,并尝试获取ReentrantLock
            boolean flag = CollectTaskQueue.getInstance().tryLock();
            if (!flag) {
                CollectTaskQueue.getInstance().addTask(collectTaskId);
                return ResponseResult.success("当前有正在执行的采集任务,已加入任务队列，ID：" + collectTaskId);
            } else if (CollectTaskQueue.getInstance().getTaskId() != -1) {
                CollectTaskQueue.getInstance().addTask(collectTaskId);
                CollectTaskQueue.getInstance().unlock();
                return ResponseResult.success("当前有正在执行的采集任务,已加入任务队列，ID：" + collectTaskId);
            }

            // 2.采集任务预检
            // NOTE 这里直接获取就行，方法体内报错会直接抛异常
            CollectTask collectTask = RedisGetManager.checkCollectTask(collectTaskId);

            // 3.更新采集请求队列状态
            log.info("即将开始采集任务，ID: " + collectTaskId);
            CollectTaskQueue.getInstance().setTaskId(collectTaskId);
            CollectTaskQueue.getInstance().setTaskState("任务执行中...");

            // 4.异步启动爬虫
            ExecutorService threadPool = Executors.newSingleThreadExecutor();
            threadPool.submit(() -> ApplicationContextProvider.getBeanByType(MySpider.class).doCrawler(collectTask));

            // 5.释放锁（因为ReentrantLock只能是同一线程释放锁，否则抛异常
            Sleeper.sleep(6, TimeUnit.SECONDS);
            CollectTaskQueue.getInstance().unlock();

            return ResponseResult.success("采集任务启动成功，ID: " + collectTaskId);
        } catch (Exception e) {
            CollectTaskQueue.getInstance().unlock();
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "采集任务启动失败", e.getMessage());
            return ResponseResult.fail("采集任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }


    @Override
    public ResponseResult<?> startContinuousCollectTask(
            Integer beginCollectTaskId,
            Integer endCollectTaskId
    ) {
        // TODO 启动连续采集任务
        if(beginCollectTaskId > endCollectTaskId) {
            for (int i = beginCollectTaskId; i >= endCollectTaskId; --i) {
                ApplicationContextProvider.getBeanByType(CollectTaskServiceImpl.class).startCollectTask(i);
            }
        } else {
            for (int i = beginCollectTaskId; i <= endCollectTaskId; ++i) {
                ApplicationContextProvider.getBeanByType(CollectTaskServiceImpl.class).startCollectTask(i);
            }
        }
        return ResponseResult.success("启动连续采集任务成功！");
    }


    @Override
    @Transactional
    public ResponseResult<?> manualPersistence(
            Integer collectTaskId
    ) {
        // TODO 手动落库
        try {
            long startTime = System.currentTimeMillis();
            CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
            TaskObject taskObject = RedisGetManager.getTaskObject(collectTask);
            RedisUtil redisUtil = RedisUtil.getInstance();
            CommonMapper commonMapper = ApplicationContextProvider.getBeanByType(CommonMapper.class);
            Long dataItemsSize = redisUtil.lLen(RedisConstant.DATA_ITEMS + collectTaskId);
            Long fileItemsSize = redisUtil.lLen(RedisConstant.FILE_ITEMS + collectTaskId);
            int batchNum = Integer.parseInt(ConfigUtil.getProperties(ConfigConstant.BATCH_NUM));
            if (dataItemsSize != 0) {
                // 基本数据信息
                String dataTableName = collectTask.getDataTableName();
                commonMapper.createDynamicTable(collectTask.getDataTableName(), JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class));
                int insertLength = Integer.parseInt(dataItemsSize.toString());
                int i = 0;
                while (insertLength > batchNum) {
                    commonMapper.insertBatch(dataTableName, redisUtil.lRange(RedisConstant.DATA_ITEMS + collectTaskId, i, i + batchNum)
                            .stream().map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class)).collect(Collectors.toList()));
                    i += batchNum;
                    insertLength -= batchNum;
                }
                if (insertLength > 0) {
                    commonMapper.insertBatch(dataTableName, redisUtil.lRange(RedisConstant.DATA_ITEMS + collectTaskId, i, i + insertLength)
                            .stream().map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class)).collect(Collectors.toList()));
                }
                //文件信息
                String fileTableName = collectTask.getFileTableName();
                commonMapper.createDynamicTable(collectTask.getFileTableName(), JsonUtil.Json2Obj(taskObject.getFileTableFields(), Map.class));
                insertLength = Integer.parseInt(fileItemsSize.toString());
                i = 0;
                while (insertLength > batchNum) {
                    commonMapper.insertBatch(fileTableName, redisUtil.lRange(RedisConstant.FILE_ITEMS + collectTaskId, i, i + batchNum)
                            .stream().map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class)).collect(Collectors.toList()));
                    i += batchNum;
                    insertLength -= batchNum;
                }
                if (insertLength > 0) {
                    commonMapper.insertBatch(fileTableName, redisUtil.lRange(RedisConstant.FILE_ITEMS + collectTaskId, i, i + insertLength)
                            .stream().map(jsonStr -> (Map<String, String>) JsonUtil.Json2Obj(jsonStr, Map.class)).collect(Collectors.toList()));

                }

                // 清除缓存
                RedisGetManager.deleteRedisById(collectTaskId);
            }
            long endTime = System.currentTimeMillis();

            return ResponseResult.success("落库耗时：" + (endTime - startTime) + "ms");
        } catch (Exception e) {
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "手动落库失败", e.getMessage());
            return ResponseResult.fail("手动落库失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

}
