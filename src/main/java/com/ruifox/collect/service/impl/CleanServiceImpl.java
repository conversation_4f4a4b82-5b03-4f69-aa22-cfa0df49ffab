package com.ruifox.collect.service.impl;

import com.ruifox.collect.common.results.ResponseResult;
import com.ruifox.collect.manager.CleanManager;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.service.CleanService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Getter
@Slf4j
public class CleanServiceImpl implements CleanService {
    @Override
    public ResponseResult<?> cleanNews(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanNews(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanDoctor(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanDoctor(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanImage(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanImage(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanVideo(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanVideo(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanLeader(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanLeader(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanNewspaper(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanNewspaper(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanNewsJava(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanNewsJava(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanDoctorJava(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanDoctorJava(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanImageJava(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanImageJava(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanVideoJava(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanVideoJava(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanLeaderJava(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanLeaderJava(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }

    @Override
    public ResponseResult<?> cleanNewspaperJava(Integer collectTaskId) {
        try {
            String result = CleanManager.cleanNewspaperJava(collectTaskId);
            return ResponseResult.success("清洗任务启动成功，ID: " + collectTaskId + "，结果是：" + result);
        }catch (Exception e){
            CrawlerManager.taskFailParseRecord(collectTaskId, "", "清洗任务启动失败", e.getMessage());
            return ResponseResult.fail("清洗任务启动失败！！！ID：" + collectTaskId + "。原因：" + e.getMessage());
        }
    }
}
