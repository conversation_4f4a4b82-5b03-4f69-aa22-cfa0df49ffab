package com.ruifox.collect.service;

import com.ruifox.collect.common.results.ResponseResult;

public interface CleanService {
    ResponseResult<?> cleanNews(Integer collectTaskId);

    ResponseResult<?> cleanDoctor(Integer collectTaskId);

    ResponseResult<?> cleanImage(Integer collectTaskId);

    ResponseResult<?> cleanVideo(Integer collectTaskId);

    ResponseResult<?> cleanLeader(Integer collectTaskId);

    ResponseResult<?> cleanNewspaper(Integer collectTaskId);

    ResponseResult<?> cleanNewsJava(Integer collectTaskId);

    ResponseResult<?> cleanDoctorJava(Integer collectTaskId);

    ResponseResult<?> cleanImageJava(Integer collectTaskId);

    ResponseResult<?> cleanVideoJava(Integer collectTaskId);

    ResponseResult<?> cleanLeaderJava(Integer collectTaskId);

    ResponseResult<?> cleanNewspaperJava(Integer collectTaskId);
}
