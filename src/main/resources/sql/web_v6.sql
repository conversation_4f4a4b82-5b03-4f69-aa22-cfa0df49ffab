/*
 Navicat Premium Dump SQL

 Source Server         : Mysql-本地虚拟机
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : **************:3306
 Source Schema         : web_v5

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 26/06/2024 11:14:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for collect_task
-- ----------------------------
DROP TABLE IF EXISTS `collect_task`;
CREATE TABLE `collect_task`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务id\r\n',
  `target_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '目标URL',
  `page_sum` int NOT NULL COMMENT '采集总页数',
  `template_id` int NULL DEFAULT NULL COMMENT '模版id',
  `task_type_id` int NOT NULL COMMENT '任务类型id',
  `cookie_id` int NULL DEFAULT NULL COMMENT 'cookie id',
  `task_status` tinyint NULL DEFAULT NULL COMMENT '任务状态 (1.WAITING 2.RUNNING 3.PAUSE 4.SUCCESS 5.FAIL)',
  `import_category_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '待导入栏目名称',
  `import_category_id` int NULL DEFAULT NULL COMMENT '待导入栏目id',
  `host` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '所属网站域名',
  `data_table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基本信息表名',
  `file_table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件信息表名',
  `timeout` int NULL DEFAULT NULL COMMENT '页面访问或页面源码获取超时时间，单位为秒',
  `embedded` int NULL DEFAULT NULL COMMENT '详情页是否内嵌 （0.非内嵌 1.内嵌）',
  `file_folder_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件所在文件夹名称',
  `collect_type` tinyint NULL DEFAULT NULL COMMENT '0 -> 正常采集 ; 1 -> 单页 ; 2 - > 增采 ; 3 -> 列表页修复 ; 4 -> 详情页修复',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '采集任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of collect_task
-- ----------------------------

-- ----------------------------
-- Table structure for file_template
-- ----------------------------
DROP TABLE IF EXISTS `file_template`;
CREATE TABLE `file_template`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title_count` int NULL DEFAULT NULL COMMENT '标题行数',
  `field_desc_row` int NOT NULL DEFAULT -1 COMMENT '字段标题所在行     没有就是-1',
  `field_loc_setting` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '待解析字段位置设置\r\n例如：     \r\n[\r\n    {\"field\": \"depart\",\"field_name\": \"部门\",\"index\": \"0\",\"cell_over\": \"0\"},\r\n    {\"field\": \"name\",\"field_name\": \"姓名\",\"index\": \"1\",\"cell_over\": \"0\"},\r\n    {\"field\": \"content\",\"filed_name\": \"内容\",\"index\": \"-1\",\"cell_over\": \"0\"}\r\n]',
  `is_precise` int(1) UNSIGNED ZEROFILL NOT NULL COMMENT '字段索引位置准确',
  `end_type` int NULL DEFAULT NULL COMMENT '结束类型     \r\n0：根据索引     \r\n1：根据名称     ',
  `end_with` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结束条件     \r\n例如：\r\n[\r\n  {\"0\": \"总结\"}\r\n]',
  `start_sheet` int NOT NULL DEFAULT 0 COMMENT '开始sheet',
  `end_sheet` int NOT NULL DEFAULT 0 COMMENT '结束sheet',
  `is_all_title` int(1) UNSIGNED ZEROFILL NOT NULL COMMENT '是否所有sheet都有标题     \r\n1：都有标题     \r\n0：仅第一页有标题',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of file_template
-- ----------------------------
INSERT INTO `file_template` VALUES (1, 1, 0, '[\r\n    {\"field\": \"depart\",\"field_name\": \"科室\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"1\"},\r\n    {\"field\": \"name\",\"field_name\": \"姓名\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"},\r\n    {\"field\": \"doc_position\",\"field_name\": \"职称\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"},\r\n    {\"field\": \"content\",\"field_name\": \"简介及擅长\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"},\r\n    {\"field\": \"file_path\",\"field_name\": \"姓名\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"}\r\n]', 0, 1, '[\r\n  {\"field_name\": \"姓名\",\"condition\":\"null\",\"index\":\"1\"}\r\n]', 0, 0, 0, '2024-06-20 10:18:24', '2024-06-21 11:26:59');

-- ----------------------------
-- Table structure for parse_fail_record
-- ----------------------------
DROP TABLE IF EXISTS `parse_fail_record`;
CREATE TABLE `parse_fail_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `collect_task_id` int NULL DEFAULT NULL COMMENT '关联采集任务ID',
  `target_url` varchar(600) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目标URL',
  `item_flag` tinyint NULL DEFAULT NULL COMMENT '0 -> 列表页 ； 1 -> 详情页',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '针对列表页/列表项解析失败记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of parse_fail_record
-- ----------------------------

-- ----------------------------
-- Table structure for task_cookie
-- ----------------------------
DROP TABLE IF EXISTS `task_cookie`;
CREATE TABLE `task_cookie`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `cookie` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'cookie内容',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_cookie
-- ----------------------------
INSERT INTO `task_cookie` VALUES (1, '[\r\n  {\"key\": \"__jsluid_s\", \"value\": \"a56ccca07ff11db48aa104c45509de1d\", \"path\": \"/\"},\r\n  {\"key\": \"ASP.NET_SessionId\", \"value\": \"2hcmlf55knw0pyrs5l4dwt55\", \"path\": \"/\"},\r\n  {\"key\": \"ClientScreenHeight\", \"value\": \"1080\", \"path\": \"/admin\"},\r\n  {\"key\": \"ClientScreenWidth\", \"value\": \"1920\", \"path\": \"/admin\"},\r\n  {\"key\": \"PE_ShowWarning\", \"value\": \"false\", \"path\": \"/\"},\r\n  {\"key\": \"www.lygdfyy.com.cn443.ASPXAUTHAdminCookie\", \"value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path\": \"/\"},\r\n  {\"key\": \"www.lygdfyy.com.cn443.ASPXAUTH\", \"value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path\": \"/\"}\r\n]', '2024-06-21 13:46:31', '2024-06-25 14:33:34');

-- ----------------------------
-- Table structure for task_fail_record
-- ----------------------------
DROP TABLE IF EXISTS `task_fail_record`;
CREATE TABLE `task_fail_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `collect_task_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采集任务id',
  `cause_e` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '异常原因',
  `cause_my` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异常备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_fail_record
-- ----------------------------

-- ----------------------------
-- Table structure for task_type
-- ----------------------------
DROP TABLE IF EXISTS `task_type`;
CREATE TABLE `task_type`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务类型id',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务类型名称',
  `data_table_suffix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基本信息表后缀',
  `file_table_suffix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件信息表后缀',
  `data_table_fields` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '基本信息建表字段',
  `file_table_fields` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文件信息建表字段',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_type
-- ----------------------------
INSERT INTO `task_type` VALUES (1, '新闻', 'news', 'news_file', '{\n\"title\":\"varchar(255)\",\n\"target_url\":\"text\",\n\"classification\":\"varchar(255)\",\n\"come_from\":\"varchar(255)\",\n\"released\":\"varchar(255)\",\n\"views\":\"int\",\n\"description\":\"longtext\",\n\"author\":\"varchar(255)\",\n\"editors\":\"varchar(255)\",\n\"collect_task_id\":\"int\",\n\"page_id\":\"varchar(255)\",\n\"host\":\"varchar(255)\",\n\"flag\":\"tinyint\",\n\"content\":\"longtext\",\n\"origin_code\":\"longtext\"\n}', '{\n    \"target_url\" : \"text\",\n    \"local_url\" : \"varchar(255)\",\n    \"origin_url\" : \"text\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-04-30 10:00:14', '2024-06-06 13:36:27');
INSERT INTO `task_type` VALUES (2, '医生', 'doctor', 'doctor_file', '{\n    \"name\":\"varchar(255)\",\n    \"target_url\":\"varchar(255)\",\n    \"classification\":\"varchar(255)\",\n    \"thumb\":\"varchar(255)\",\n    \"depart\":\"varchar(255)\",\n    \"doc_position\":\"varchar(255)\",\n    \"edu_position\":\"varchar(255)\",\n    \"hos_position\":\"varchar(255)\",\n    \"level\":\"varchar(255)\",\n    \"political_visage\":\"varchar(255)\",\n    \"honor\":\"varchar(255)\",\n    \"good_at\":\"longtext\",\n    \"brief\":\"longtext\",\n    \"content\":\"longtext\",\n    \"origin_code\":\"longtext\",\n    \"released\":\"varchar(255)\",\n    \"views\":\"int\",\n    \"clinic_hours\":\"varchar(255)\",\n    \"editors\":\"varchar(255)\",\n    \"come_from\":\"varchar(255)\",\n    \"host\":\"varchar(255)\",\n    \"flag\":\"varchar(255)\",\n    \"page_id\":\"varchar(255)\",\n    \"collect_task_id\":\"int\"\n}', '{\n    \"target_url\" : \"varchar(255)\",\n    \"local_url\" : \"varchar(255)\",\n    \"origin_url\" : \"varchar(255)\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-04-30 10:00:14', '2024-05-30 14:55:04');
INSERT INTO `task_type` VALUES (3, '单页', 'page', 'page_file', '{\n\"title\":\"varchar(255)\",\n\"target_url\":\"text\",\n\"classification\":\"varchar(255)\",\n\"come_from\":\"varchar(255)\",\n\"released\":\"varchar(255)\",\n\"views\":\"int\",\n\"description\":\"longtext\",\n\"author\":\"varchar(255)\",\n\"editors\":\"varchar(255)\",\n\"collect_task_id\":\"int\",\n\"page_id\":\"varchar(255)\",\n\"host\":\"varchar(255)\",\n\"flag\":\"tinyint\",\n\"content\":\"longtext\",\n\"origin_code\":\"longtext\"\n}', '{\n    \"target_url\" : \"text\",\n    \"local_url\" : \"varchar(255)\",\n    \"origin_url\" : \"text\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-06-05 15:25:49', '2024-06-18 15:57:59');
INSERT INTO `task_type` VALUES (4, '导师', 'teacher', 'teacher_file', NULL, NULL, '2024-06-17 14:08:33', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (5, '领导', 'leader', 'leader_file', NULL, NULL, '2024-06-17 14:09:03', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (6, '图集', 'image', 'image_file', '{\r\n\"title\":\"varchar(255)\",\r\n\"target_url\":\"text\",\r\n\"thumb\" : \"varchar(255)\",\r\n\"classification\":\"varchar(255)\",\r\n\"come_from\":\"varchar(255)\",\r\n\"released\":\"varchar(255)\",\r\n\"views\":\"int\",\r\n\"description\":\"longtext\",\r\n\"author\":\"varchar(255)\",\r\n\"photographer\":\"varchar(255)\",\r\n\"collect_task_id\":\"int\",\r\n\"host\":\"varchar(255)\",\r\n\"content\":\"longtext\",\r\n\"page_id\":\"varchar(255)\",\r\n\"origin_code\":\"longtext\",\r\n\"flag\":\"tinyint\"\r\n}', '{\r\n    \"target_url\" : \"varchar(255)\",\r\n    \"local_url\" : \"varchar(255)\",\r\n    \"origin_url\" : \"varchar(255)\",\r\n    \"page_id\" : \"varchar(255)\",\r\n    \"res_code\" : \"int\"\r\n}', '2024-06-17 14:09:05', '2024-06-17 14:11:28');
INSERT INTO `task_type` VALUES (7, '药剂', 'medicament', 'medicament_file', NULL, NULL, '2024-06-17 14:09:05', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (8, '院报', 'newspaper', 'newspaper_file', '{\r\n\"title\":\"varchar(255)\",\r\n\"target_url\":\"text\",\r\n\"thumb\" : \"varchar(255)\",\r\n\"classification\":\"varchar(255)\",\r\n\"come_from\":\"varchar(255)\",\r\n\"released\":\"varchar(255)\",\r\n\"views\":\"int\",\r\n\"description\":\"longtext\",\r\n\"author\":\"varchar(255)\",\r\n\"photographer\":\"varchar(255)\",\r\n\"collect_task_id\":\"int\",\r\n\"host\":\"varchar(255)\",\r\n\"content\":\"longtext\",\r\n\"page_id\":\"varchar(255)\",\r\n\"origin_code\":\"longtext\",\r\n\"flag\":\"tinyint\"\r\n}', '{\r\n    \"target_url\" : \"varchar(255)\",\r\n    \"local_url\" : \"varchar(255)\",\r\n    \"origin_url\" : \"varchar(255)\",\r\n    \"page_id\" : \"varchar(255)\",\r\n    \"res_code\" : \"int\"\r\n}', '2024-06-17 14:09:15', '2024-06-18 15:56:31');
INSERT INTO `task_type` VALUES (9, '招标', 'tender', 'tender_file', NULL, NULL, '2024-06-17 14:09:16', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (10, '下载', 'download', 'download_file', NULL, NULL, '2024-06-17 14:09:18', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (11, '视频', 'video', 'video_file', '{\n\"title\":\"varchar(255)\",\n\"target_url\":\"text\",\n\"classification\":\"varchar(255)\",\n\"come_from\":\"varchar(255)\",\n\"released\":\"varchar(255)\",\n\"views\":\"int\",\n\"description\":\"longtext\",\n\"author\":\"varchar(255)\",\n\"editors\":\"varchar(255)\",\n\"collect_task_id\":\"int\",\n\"page_id\":\"varchar(255)\",\n\"host\":\"varchar(255)\",\n\"flag\":\"tinyint\",\n\"content\":\"longtext\",\n\"origin_code\":\"longtext\"\n}', '{\n    \"target_url\" : \"text\",\n    \"local_url\" : \"varchar(255)\",\n    \"origin_url\" : \"text\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-06-17 14:09:20', '2024-06-18 15:57:07');

-- ----------------------------
-- Table structure for taskTemplate
-- ----------------------------
DROP TABLE IF EXISTS `taskTemplate`;
CREATE TABLE `taskTemplate`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模版id',
  `next_button_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下一页按钮位置',
  `next_button_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下一页按钮名称',
  `list_item_tag` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列表项标签',
  `list_item_click_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列表项中可点击元素的位置',
  `list_item_field_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列表项/外链字段解析模版',
  `embedded_detail_field_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '内嵌详情页字段解析模版',
  `independent_detail_field_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '独立详情页字段解析模版',
  `iframe_content_Ioc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文的iframe标签位置(可以为null)',
  `iframe_detail_field_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'iframe中的解析模板(可以为null)',
  `content_img_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中图片的位置',
  `content_img_container` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中图片所在容器',
  `content_a_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中附件的位置',
  `content_a_container` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中附件所在容器:可能用不上',
  `time_pattern` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间解析模版',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '模版表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of taskTemplate
-- ----------------------------
INSERT INTO `taskTemplate` VALUES (1, '.pagelist a', '下一页', 'li', '.about_box > .lispic > ul > li > a', '{\"title\":\"a&0//span&0;\"}', '', '{\r\n\"title\":\".a_title&0//;\",\r\n\"come_from\":\" \",\r\n\"released\":\" \",\r\n\"views\":\" \",\r\n\"description\":\" \",\r\n\"author\":\" \",\r\n\"photographer\":\" \",\r\n\"content\":\".about_content&0//;\"\r\n}', NULL, NULL, '.about_content img', '.about_content', '.about_content a', '.about_content', 'yyyy-MM-dd', '2024-06-24 18:05:24', '2024-05-30 15:37:57');
INSERT INTO `taskTemplate` VALUES (2, '.tdbg a', '下一页', 'tr', '.linkType > a:nth-of-type(2)', '{\"title\":\".linkType&0//a&1;\",\"released\":\"td&5//;\",\"views\":\"td&6//;\"}', '', '{\"come_from\":\" \",\"author\":\" \",\"title\":\" \",\"released\":\" \",\"content\":\" \",\"views\":\" \"}', '.tdbg > td > iframe', '{\"come_from\":\" \",\"author\":\" \",\"title\":\" \",\"released\":\" \",\"content\":\"#form1&0//;\",\"views\":\" \"}', '#form1 img', '#form1', '#form1 a', '#form1', 'yyyy-MM-dd', '2024-06-24 18:05:26', '2024-06-24 14:55:10');

SET FOREIGN_KEY_CHECKS = 1;
