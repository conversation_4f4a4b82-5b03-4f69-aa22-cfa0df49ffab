/*
 Navicat Premium Dump SQL

 Source Server         : localhost-本地数据源
 Source Server Type    : MySQL
 Source Server Version : 80020 (8.0.20)
 Source Host           : localhost:3306
 Source Schema         : crawler_data

 Target Server Type    : MySQL
 Target Server Version : 80020 (8.0.20)
 File Encoding         : 65001

 Date: 16/07/2024 17:03:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for collect_task
-- ----------------------------
DROP TABLE IF EXISTS `collect_task`;
CREATE TABLE `collect_task`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务id\r\n',
  `target_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '目标URL',
  `page_sum` int NOT NULL COMMENT '采集总页数',
  `collect_type` tinyint NULL DEFAULT NULL COMMENT '1--》正常采集；2--》单详情页采集；5--》异步请求采集；10--》Excel文件采集；100--》重采列表页；200--》重采详情页；',
  `task_type_id` int NOT NULL COMMENT '任务类型id',
  `template_id` int NULL DEFAULT NULL COMMENT '模版id',
  `cookie_id` int NULL DEFAULT NULL COMMENT 'cookie id（可以没有，即为0）',
  `embedded` int NULL DEFAULT NULL COMMENT '详情页是否内嵌 （0.非内嵌 1.内嵌）',
  `timeout` int NULL DEFAULT NULL COMMENT '页面访问或页面源码获取超时时间，单位为秒',
  `import_category_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '待导入栏目名称',
  `import_category_id` int NULL DEFAULT NULL COMMENT '待导入栏目id',
  `host` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '所属网站域名',
  `task_status` tinyint NULL DEFAULT NULL COMMENT '任务状态 (1.等待执行 2.执行中 3.中断 4.成功 5.失败)',
  `real_page_sum` int NULL DEFAULT NULL COMMENT '真实采集页数',
  `data_table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基本信息表名',
  `file_table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件信息表名',
  `file_folder_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件所在文件夹名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 195 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '采集任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of collect_task
-- ----------------------------
INSERT INTO `collect_task` VALUES (124, 'https://www.lygdfyy.com.cn/admin/Index.aspx', 3, 5, 1, 2, 1, 0, 6, '工会动态', 999, 'www.lygdfyy.com.cn', 2, 0, 'lygdfyy_news_124', 'lygdfyy_news_file_124', '20240124', '2024-07-01 18:25:48', '2024-07-16 16:25:36');
INSERT INTO `collect_task` VALUES (194, 'https://www.cd5120.com/departments_mnwka0_ksdt/', 1, 1, 1, 8, NULL, 0, 6, '泌尿外科-科室动态', 999, 'www.cd5120.com', 2, 0, 'cd5120_news_194', 'cd5120_news_file_194', '20240906', '2024-07-12 18:06:15', '2024-07-16 15:00:17');

-- ----------------------------
-- Table structure for task_cookie
-- ----------------------------
DROP TABLE IF EXISTS `task_cookie`;
CREATE TABLE `task_cookie`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `cookie` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'cookie内容',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_cookie
-- ----------------------------
INSERT INTO `task_cookie` VALUES (1, '[\r\n  {\"key\": \"__jsluid_s\", \"value\": \"f6e4f51152b0f31b8c984d83f28faa70\", \"path\": \"/\"},\r\n  {\"key\": \"ASP.NET_SessionId\", \"value\": \"x005yq45v3kakvzvs2wpcnfe\", \"path\": \"/\"},\r\n  {\"key\": \"ClientScreenHeight\", \"value\": \"1080\", \"path\": \"/admin\"},\r\n  {\"key\": \"ClientScreenWidth\", \"value\": \"1920\", \"path\": \"/admin\"},\r\n  {\"key\": \"PE_ShowWarning\", \"value\": \"false\", \"path\": \"/\"},\r\n  {\"key\": \"www.lygdfyy.com.cn443.ASPXAUTHAdminCookie\", \"value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path\": \"/\"},\r\n  {\"key\": \"www.lygdfyy.com.cn443.ASPXAUTH\", \"value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path\": \"/\"}\r\n]', '2024-06-21 13:46:31', '2024-07-02 09:11:48');
INSERT INTO `task_cookie` VALUES (2, '[\r\n  {\"key\": \"__jsluid_s\", \"value\": \"q1cyd145reicyy55wmqe5qve\", \"path\": \"/\"},\r\n  {\"key\": \"ASP.NET_SessionId\", \"value\": \"q1cyd145reicyy55wmqe5qve\", \"path\": \"/\"},\r\n  {\"key\": \"ClientScreenHeight\", \"value\": \"1080\", \"path\": \"/admin\"},\r\n  {\"key\": \"ClientScreenWidth\", \"value\": \"1920\", \"path\": \"/admin\"},\r\n  {\"key\": \"PE_ShowWarning\", \"value\": \"false\", \"path\": \"/\"},\r\n  {\"key\": \"www.lygdfyy.com.cn443.ASPXAUTHAdminCookie\", \"value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path\": \"/\"},\r\n  {\"key\": \"www.lygdfyy.com.cn443.ASPXAUTH\", \"value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path\": \"/\"}\r\n]', '2024-06-21 13:46:31', '2024-07-01 15:10:29');

-- ----------------------------
-- Table structure for task_fail_parse_record
-- ----------------------------
DROP TABLE IF EXISTS `task_fail_parse_record`;
CREATE TABLE `task_fail_parse_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `collect_task_id` int NULL DEFAULT NULL COMMENT '关联采集任务ID',
  `target_url` varchar(600) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目标URL',
  `item_flag` tinyint NULL DEFAULT NULL COMMENT '0 -> 列表页 ； 1 -> 详情页',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1276 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '针对列表页/列表项解析失败记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_fail_parse_record
-- ----------------------------
INSERT INTO `task_fail_parse_record` VALUES (1274, 124, 'https://www.lygdfyy.com.cn/admin/Contents/ArticlePreview.aspx?GeneralID=3749&Title=%e6%88%91%e9%99%a2%e4%b8%be%e5%8a%9e%e8%bf%8e%e6%96%b0%e6%98%a5%e8%b6%a3%e5%91%b3%e8%bf%90%e5%8a%a8%e4%bc%9a', 1, '2024-07-16 16:46:23', '2024-07-16 16:46:23');
INSERT INTO `task_fail_parse_record` VALUES (1275, 124, 'https://www.lygdfyy.com.cn/admin/Contents/ArticlePreview.aspx?GeneralID=3831&Title=%e3%80%90%e7%99%bb%e5%b1%b1%e8%b8%8f%e9%9d%92%ef%bc%8c%e6%94%be%e9%a3%9e%e5%bf%83%e6%83%85%e3%80%91%e6%88%91%e9%99%a2%e5%b7%a5%e4%bc%9a%e7%bb%84%e7%bb%87%e6%98%a5%e6%b8%b8%e8%b8%8f%e9%9d%92%e6%b4%bb%e5%8a%a8', 1, '2024-07-16 16:46:24', '2024-07-16 16:46:24');

-- ----------------------------
-- Table structure for task_fail_record
-- ----------------------------
DROP TABLE IF EXISTS `task_fail_record`;
CREATE TABLE `task_fail_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `collect_task_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采集任务id',
  `cause_e` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '异常原因',
  `cause_my` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异常备注',
  `data_size` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据采集数量',
  `file_size` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件下载数量（图片、附件等）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_fail_record
-- ----------------------------
INSERT INTO `task_fail_record` VALUES (69, '194', NULL, '数据采集完成', '6', '3', '2024-07-16 14:54:24', '2024-07-16 14:54:24');
INSERT INTO `task_fail_record` VALUES (70, '194', NULL, '数据落库成功', '6', '3', '2024-07-16 14:54:24', '2024-07-16 14:54:24');
INSERT INTO `task_fail_record` VALUES (71, '194', 'redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool', '爬虫运行异常', NULL, NULL, '2024-07-16 15:05:13', '2024-07-16 15:05:13');
INSERT INTO `task_fail_record` VALUES (72, '124', NULL, '数据采集完成', '56', '234', '2024-07-16 16:16:30', '2024-07-16 16:16:30');
INSERT INTO `task_fail_record` VALUES (73, '124', NULL, '数据落库成功', '56', '234', '2024-07-16 16:16:30', '2024-07-16 16:16:30');
INSERT INTO `task_fail_record` VALUES (74, '124', 'redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool', '爬虫运行异常', NULL, NULL, '2024-07-16 16:46:23', '2024-07-16 16:46:23');

-- ----------------------------
-- Table structure for task_file_template
-- ----------------------------
DROP TABLE IF EXISTS `task_file_template`;
CREATE TABLE `task_file_template`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title_count` int NULL DEFAULT NULL COMMENT '标题行数',
  `field_desc_row` int NOT NULL DEFAULT -1 COMMENT '字段标题所在行     没有就是-1',
  `field_loc_setting` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '待解析字段位置设置\r\n例如：     \r\n[\r\n    {\"field\": \"depart\",\"field_name\": \"部门\",\"index\": \"0\",\"cell_over\": \"0\"},\r\n    {\"field\": \"name\",\"field_name\": \"姓名\",\"index\": \"1\",\"cell_over\": \"0\"},\r\n    {\"field\": \"content\",\"filed_name\": \"内容\",\"index\": \"-1\",\"cell_over\": \"0\"}\r\n]',
  `is_precise` int(1) UNSIGNED ZEROFILL NOT NULL COMMENT '字段索引位置准确',
  `end_type` int NULL DEFAULT NULL COMMENT '结束类型     \r\n0：根据索引     \r\n1：根据名称     ',
  `end_with` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结束条件     \r\n例如：\r\n[\r\n  {\"0\": \"总结\"}\r\n]',
  `start_sheet` int NOT NULL DEFAULT 0 COMMENT '开始sheet',
  `end_sheet` int NOT NULL DEFAULT 0 COMMENT '结束sheet',
  `is_all_title` int(1) UNSIGNED ZEROFILL NOT NULL COMMENT '是否所有sheet都有标题     \r\n1：都有标题     \r\n0：仅第一页有标题',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_file_template
-- ----------------------------
INSERT INTO `task_file_template` VALUES (1, 1, 0, '[\r\n    {\"field\": \"depart\",\"field_name\": \"科室\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"1\"},\r\n    {\"field\": \"name\",\"field_name\": \"姓名\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"},\r\n    {\"field\": \"doc_position\",\"field_name\": \"职称\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"},\r\n    {\"field\": \"content\",\"field_name\": \"简介及擅长\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"},\r\n    {\"field\": \"file_path\",\"field_name\": \"姓名\",\"index\": \"\",\"cell_over\": \"0\",\"is_public\":\"0\"}\r\n]', 0, 1, '[\r\n  {\"field_name\": \"姓名\",\"condition\":\"null\",\"index\":\"1\"}\r\n]', 0, 0, 0, '2024-06-20 10:18:24', '2024-06-21 11:26:59');

-- ----------------------------
-- Table structure for task_template
-- ----------------------------
DROP TABLE IF EXISTS `task_template`;
CREATE TABLE `task_template`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模版id',
  `next_button_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下一页按钮位置',
  `next_button_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下一页按钮名称',
  `list_item_tag` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列表项标签',
  `list_item_click_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列表项中可点击元素的位置',
  `list_item_field_loc` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '列表项/外链字段解析模版',
  `embedded_detail_field_loc` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内嵌详情页字段解析模版',
  `independent_detail_field_loc` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '独立详情页字段解析模版',
  `iframe_content_Ioc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文的iframe标签位置(可以为null)',
  `iframe_detail_field_loc` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'iframe中的解析模板(可以为null)',
  `content_img_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中图片的位置',
  `content_img_container` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中图片所在容器',
  `content_a_loc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中附件的位置',
  `content_a_container` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正文中附件所在容器:可能用不上',
  `time_pattern` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间解析模版',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '模版表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_template
-- ----------------------------
INSERT INTO `task_template` VALUES (2, '.tdbg a', '下一页', 'tr', '.linkType > a:nth-of-type(2)', '{\"title\":\".linkType&0//a&1;\",\"released\":\"td&5//;\",\"views\":\"td&6//;\"}', '', '{\"come_from\":\" \",\"author\":\" \",\"title\":\" \",\"released\":\" \",\"content\":\" \",\"views\":\" \"}', '.tdbg > td > iframe', '{\"come_from\":\" \",\"author\":\" \",\"title\":\" \",\"released\":\" \",\"content\":\"#form1&0//;\",\"views\":\" \"}', '#form1 img', '#form1', '#form1 a', '#form1', 'yyyy-MM-dd', '2024-07-16 16:02:12', '2024-06-24 14:55:10');
INSERT INTO `task_template` VALUES (8, 'div.pager > a', '下一页', 'li', 'ul.clearfix > li > h2 > a', '{\n    \"title\":\"h2&0//;\",\n    \"released\":\"div.fl > span&0//;\",\n    \"come_from\":\"div.fl > span&1//;\",\n    \"thumb\":\"div.dm-news-list-summary > a > img&0//##src;\",\n    \"description\":\"div.dm-news-list-summary > p&0//;\"\n}', '', '{\n    \"content\":\"div.news-content&0//;\"\n}', NULL, NULL, 'div.news-content img', 'div.news-content', 'div.news-content a', 'div.news-content', 'yyyy-MM-dd', '2024-07-12 20:24:31', '2024-07-12 17:54:32');

-- ----------------------------
-- Table structure for task_type
-- ----------------------------
DROP TABLE IF EXISTS `task_type`;
CREATE TABLE `task_type`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务类型id',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务类型名称',
  `data_table_suffix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基本信息表后缀',
  `file_table_suffix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件信息表后缀',
  `data_table_fields` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '基本信息建表字段',
  `file_table_fields` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文件信息建表字段',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_type
-- ----------------------------
INSERT INTO `task_type` VALUES (1, '新闻', 'news', 'news_file', '{\r\n\"collect_task_id\":\"int\",\r\n\"classification\":\"varchar(255)\",\r\n\"target_url\":\"longtext\",\r\n\"page_id\":\"varchar(255)\",\n\"title\":\"varchar(255)\",\r\n\"thumb\": \"varchar(255)\",\r\n\"released\":\"varchar(255)\",\r\n\"come_from\":\"varchar(255)\",\n\"description\":\"longtext\",\n\"views\":\"int\",\r\n\"content\":\"longtext\",\r\n\"origin_code\":\"longtext\",\n\"author\":\"varchar(255)\",\n\"editors\":\"varchar(255)\",\n\"host\":\"varchar(255)\",\n\"flag\":\"tinyint\"\n}', '{\n    \"target_url\" : \"longtext\",\n    \"local_url\" : \"longtext\",\n    \"origin_url\" : \"longtext\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-04-30 10:00:14', '2024-07-15 09:32:32');
INSERT INTO `task_type` VALUES (2, '医生', 'doctor', 'doctor_file', '{\n    \"name\":\"varchar(255)\",\n    \"target_url\":\"varchar(255)\",\n    \"classification\":\"varchar(255)\",\n    \"thumb\":\"varchar(255)\",\n    \"depart\":\"varchar(255)\",\n    \"doc_position\":\"varchar(255)\",\n    \"edu_position\":\"varchar(255)\",\n    \"hos_position\":\"varchar(255)\",\n    \"level\":\"varchar(255)\",\n    \"political_visage\":\"varchar(255)\",\n    \"honor\":\"varchar(255)\",\n    \"good_at\":\"longtext\",\n    \"brief\":\"longtext\",\n    \"content\":\"longtext\",\n    \"origin_code\":\"longtext\",\n    \"released\":\"varchar(255)\",\n    \"views\":\"int\",\n    \"clinic_hours\":\"varchar(255)\",\n    \"editors\":\"varchar(255)\",\n    \"come_from\":\"varchar(255)\",\n    \"host\":\"varchar(255)\",\n    \"flag\":\"varchar(255)\",\n    \"page_id\":\"varchar(255)\",\n    \"collect_task_id\":\"int\"\n}', '{\n    \"target_url\" : \"longtext\",\n    \"local_url\" : \"longtext\",\n    \"origin_url\" : \"longtext\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-04-30 10:00:14', '2024-06-27 14:13:06');
INSERT INTO `task_type` VALUES (3, '单页', 'page', 'page_file', '{\n\"title\":\"varchar(255)\",\n\"target_url\":\"text\",\n\"classification\":\"varchar(255)\",\n\"come_from\":\"varchar(255)\",\n\"released\":\"varchar(255)\",\n\"views\":\"int\",\n\"description\":\"longtext\",\n\"author\":\"varchar(255)\",\n\"editors\":\"varchar(255)\",\n\"collect_task_id\":\"int\",\n\"page_id\":\"varchar(255)\",\n\"host\":\"varchar(255)\",\n\"flag\":\"tinyint\",\n\"content\":\"longtext\",\n\"origin_code\":\"longtext\"\n}', '{\n    \"target_url\" : \"longtext\",\n    \"local_url\" : \"longtext\",\n    \"origin_url\" : \"longtext\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-06-05 15:25:49', '2024-06-27 14:13:16');
INSERT INTO `task_type` VALUES (4, '导师', 'teacher', 'teacher_file', NULL, NULL, '2024-06-17 14:08:33', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (5, '领导', 'leader', 'leader_file', NULL, NULL, '2024-06-17 14:09:03', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (6, '图集', 'image', 'image_file', '{\r\n\"title\":\"varchar(255)\",\r\n\"target_url\":\"text\",\r\n\"thumb\" : \"varchar(255)\",\r\n\"classification\":\"varchar(255)\",\r\n\"come_from\":\"varchar(255)\",\r\n\"released\":\"varchar(255)\",\r\n\"views\":\"int\",\r\n\"description\":\"longtext\",\r\n\"author\":\"varchar(255)\",\r\n\"photographer\":\"varchar(255)\",\r\n\"collect_task_id\":\"int\",\r\n\"host\":\"varchar(255)\",\r\n\"content\":\"longtext\",\r\n\"page_id\":\"varchar(255)\",\r\n\"origin_code\":\"longtext\",\r\n\"flag\":\"tinyint\"\r\n}', '{\n    \"target_url\" : \"longtext\",\n    \"local_url\" : \"longtext\",\n    \"origin_url\" : \"longtext\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-06-17 14:09:05', '2024-06-27 14:13:17');
INSERT INTO `task_type` VALUES (7, '药剂', 'medicament', 'medicament_file', NULL, NULL, '2024-06-17 14:09:05', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (8, '院报', 'newspaper', 'newspaper_file', '{\r\n\"title\":\"varchar(255)\",\r\n\"target_url\":\"text\",\r\n\"thumb\" : \"varchar(255)\",\r\n\"classification\":\"varchar(255)\",\r\n\"come_from\":\"varchar(255)\",\r\n\"released\":\"varchar(255)\",\r\n\"views\":\"int\",\r\n\"description\":\"longtext\",\r\n\"author\":\"varchar(255)\",\r\n\"photographer\":\"varchar(255)\",\r\n\"collect_task_id\":\"int\",\r\n\"host\":\"varchar(255)\",\r\n\"content\":\"longtext\",\r\n\"page_id\":\"varchar(255)\",\r\n\"origin_code\":\"longtext\",\r\n\"flag\":\"tinyint\"\r\n}', '{\n    \"target_url\" : \"longtext\",\n    \"local_url\" : \"longtext\",\n    \"origin_url\" : \"longtext\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-06-17 14:09:15', '2024-06-27 14:13:20');
INSERT INTO `task_type` VALUES (9, '招标', 'tender', 'tender_file', NULL, NULL, '2024-06-17 14:09:16', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (10, '下载', 'download', 'download_file', NULL, NULL, '2024-06-17 14:09:18', '2024-06-17 14:10:50');
INSERT INTO `task_type` VALUES (11, '视频', 'video', 'video_file', '{\n\"title\":\"varchar(255)\",\n\"target_url\":\"text\",\n\"classification\":\"varchar(255)\",\n\"come_from\":\"varchar(255)\",\n\"released\":\"varchar(255)\",\n\"views\":\"int\",\n\"description\":\"longtext\",\n\"author\":\"varchar(255)\",\n\"editors\":\"varchar(255)\",\n\"collect_task_id\":\"int\",\n\"page_id\":\"varchar(255)\",\n\"host\":\"varchar(255)\",\n\"flag\":\"tinyint\",\n\"content\":\"longtext\",\n\"origin_code\":\"longtext\"\n}', '{\n    \"target_url\" : \"longtext\",\n    \"local_url\" : \"longtext\",\n    \"origin_url\" : \"longtext\",\n    \"page_id\" : \"varchar(255)\",\n    \"res_code\" : \"int\"\n}', '2024-06-17 14:09:20', '2024-06-27 14:13:22');

SET FOREIGN_KEY_CHECKS = 1;
