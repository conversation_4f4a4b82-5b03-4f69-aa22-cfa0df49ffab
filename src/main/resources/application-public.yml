#file: noinspection SpringBootApplicationYaml,SpellCheckingInspection
server:
  port: 9080

spring:
  redis:
    host: "Redis ip"
    port: "Redis 端口"
    password: "Redis 密码"
    timeout: 30000
    jedis:
      pool:
        max-idle: 100
        max-wait: 1000

common:
  geckoDriver: "火狐浏览器Driver路径"
  proxy:
    ip: "代理服务器ip"
    port: "代理服务器端口"
    # 8浏览器 12线程最优
  browser:
    count: 8   #启动浏览器数量 一半固定给DownLoader，其他的Driver备用
  thread:
    count: 12

mysql:
  default: # 默认数据源(必填)
    host: "MySQL ip"
    port: "MySQL 端口"
    database: "MySQL 数据库"
    username: "MySQL 账号"
    password: "MySQL 密码"

  dynamic: # 动态数据源(选填)
    host: "MySQL ip"
    port: "MySQL 端口"
    database: "MySQL 数据库"
    username: "MySQL 账号"
    password: "MySQL 密码"

mybatis-plus:
  global-config:
    db-config:
      id-type: input
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 配置枚举处理器
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  mapper-locations: "classpath*:/mapper/**/*.xml"
