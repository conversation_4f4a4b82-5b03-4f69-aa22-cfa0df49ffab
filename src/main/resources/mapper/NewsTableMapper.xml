<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruifox.collect.dao.mapper.NewsTableMapper">

    <!-- 根据page_id查询数据的语句，同样表名动态指定 -->
    <select id="selectByPageId" resultType="com.ruifox.collect.module.entity.news.NewsTable">
        SELECT * FROM ${tableName} WHERE page_id = #{pageId}
    </select>

    <!-- 根据id更新数据的语句，表名动态指定 -->
    <update id="updateById">
        UPDATE ${tableName}
        SET
            thumb = #{newsTable.thumb},
            content = #{newsTable.content}
        WHERE id = #{newsTable.id}
    </update>
</mapper>