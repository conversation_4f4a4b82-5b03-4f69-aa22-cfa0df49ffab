<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruifox.collect.dao.mapper.station.StationModelFieldMapper"> <!-- 替换为您的实际包路径 -->

    <select id="selectByModelIdWithDocPosition" resultType="java.util.Map">
        select * from station_model_field where model_id = #{modelId} and field = 'doc_position';
    </select>

    <select id="selectByModelIdWithHosPosition" resultType="java.util.Map">
        select * from station_model_field where model_id = #{modelId} and field = 'hos_position';
    </select>

    <select id="selectByModelIdWithEduPosition" resultType="java.util.Map">
        select * from station_model_field where model_id = #{modelId} and field = 'edu_position';
    </select>

    <select id="selectByModelIdWithEduPost" resultType="java.util.Map">
        select * from station_model_field where model_id = #{modelId} and field = 'edu_post';
    </select>

    <select id="selectByModelIdWithPartyPosition" resultType="java.util.Map">
        select * from station_model_field where model_id = #{modelId} and field = 'party_position';
    </select>

    <select id="selectByModelIdWithLeaderPosition" resultType="java.util.Map">
        select * from station_model_field where model_id = #{modelId} and field = 'leader_position';
    </select>


</mapper>