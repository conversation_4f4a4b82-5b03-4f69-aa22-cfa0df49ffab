<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruifox.collect.dao.mapper.NewsFileTableMapper">
    <!-- 插入语句示例，将表名写成动态的 -->
    <insert id="insertNewsFile">
        INSERT INTO ${tableName} (
            target_url,
            page_id,
            local_url,
            origin_url,
            res_code,
        )
        VALUES
            (#{targetUrl},
             #{pageId},
             #{localUrl},
             #{originUrl},
             #{resCode},
    </insert>

    <!-- 根据page_id查询数据的语句，同样表名动态指定 -->
    <select id="selectByPageId" resultType="com.ruifox.collect.module.entity.news.NewsFileTable">
        SELECT * FROM ${tableName} WHERE page_id = #{pageId}
    </select>

    <!-- 根据id更新数据的语句，表名动态指定 -->
    <update id="updateById">
        UPDATE ${tableName}
        SET
            target_url = #{newsFile.targetUrl},
            page_id = #{newsFile.pageId},
            local_url = #{newsFile.localUrl},
            origin_url = #{newsFile.originUrl},
            res_code = #{newsFile.resCode},
        WHERE id = #{newsFile.id}
    </update>
</mapper>