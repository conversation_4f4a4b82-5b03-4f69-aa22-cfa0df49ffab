<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruifox.collect.dao.mapper.CommonMapper"> <!-- 替换为您的实际包路径 -->

    <!--动态建表-->
    <insert id="createDynamicTable" parameterType="map">
        create table if not exists ${tableName} (
        id int not null  auto_increment primary key,
        <foreach collection="fields" index="key" item="value" separator=",">
            ${key} ${value}  DEFAULT NULL
        </foreach>,
        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
    </insert>

    <!--单条数据落库-->
    <insert id="insert" parameterType="java.util.Map">
        insert into ${fileTableName}
        <foreach collection="dataMap" index="key" item="value" separator="," open="(" close=")">
            ${key}
        </foreach>
        values
        <foreach collection="dataMap" index="key" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>;
    </insert>

    <!--批量落库-->
    <insert id="insertBatch" parameterType="java.util.Map">
        INSERT INTO ${tableName} (
        <foreach collection="records[0].keySet()" item="columnName" separator="," open="" close="">
            ${columnName}
        </foreach>
        )
        VALUES
        <foreach collection="records" item="record" separator=",">
            (<foreach collection="record.values()" item="value" separator="," open="" close="">
            #{value}
            </foreach>)
        </foreach>
    </insert>

    <!--按表名查出所有数据并排序-->
    <select id="selectByTableNameOrderBySortDesc" resultType="java.util.Map">
        select * from ${tableName} order by sort DESC;
    </select>

    <!--按表名查出所有数据并排序-->
    <select id="selectByTableNameOrderByIdDesc" resultType="java.util.Map">
        select * from ${tableName} order by id DESC;
    </select>

    <!--按表名查出所有数据并排序-->
    <select id="selectByTableNameOrderBySortAsc" resultType="java.util.Map">
        select * from ${tableName} order by sort ASC;
    </select>

    <!--按表名查出所有数据-->
    <select id="selectByTableName" resultType="java.util.Map">
        select * from ${tableName};
    </select>

    <!--按表名查出所有数据并按照发布时间排序-->
    <select id="selectByTableNameOrderByReleased" resultType="java.util.Map">
        select * from ${tableName} order by released;
    </select>

    <!--查出该表按发布时间排序的最新一条数据-->
    <select id="selectOneByTableNameLatest" resultType="java.util.Map">
        select * from ${tableName} order by released desc limit 0,1;
    </select>

    <!--根据pageId查询文件数据-->
    <select id="selectFileRecodeByPageIdAnd200" resultType="java.util.Map">
        select * from ${tableName} where page_id = #{pageId} and res_code = 200;
    </select>

    <!--获取该表所有的target_url-->
    <select id="selectAllTargetUrl" resultType="java.lang.String">
        select target_url from ${tableName};
    </select>

    <!--通过表名插入单条数据，并获取自动生成的主键值generatedId-->
    <insert id="insertByTabeleName" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="generatedId">
        insert into ${fileTableName}
        <foreach collection="tableInfo" index="key" item="value" separator="," open="(" close=")">
            ${key}
        </foreach>
        values
        <foreach collection="tableInfo" index="key" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>;
    </insert>

    <!--通过表名和主键ID修改指定字段值-->
    <update id="updatePropertyByTableNameAndID">
        UPDATE ${tableName}
        SET
        <foreach collection="fields" item="value" index="key" separator=",">
            ${key}=#{value}
        </foreach>
        WHERE id = #{ID}
    </update>

    <select id="selectOldDoctor" resultType="java.util.Map">
        select doc.id,doc.title,doc.thumb,doc.description,doc.listorder,doc.islink
             ,doc.username,doc.inputtime,doc.updatetime,doc.istop,tc.catname,doc.depart
             ,doc.content,doc.protit,doc.goodat,doc.views
        from (select tcm.id,tcm.title,tcm.thumb,tcm.description,tcm.listorder,tcm.islink,tcm.username
                   ,tcm.inputtime,tcm.updatetime,tcm.istop,tcm.depart,tcm.protit,tcm.goodat
                   ,tcmd.content,tcmd.paper,tcm.views
              from tbl_c_man tcm left join tbl_c_man_data tcmd on tcm.id =tcmd.id where tcm.catid = #{catId}) doc  left join tbl_category tc on doc.depart=tc.catid
    </select>

    <select id="selectDoctorThumb" resultType="java.util.Map">
        select title,thumb from ${tableName};
    </select>

</mapper>