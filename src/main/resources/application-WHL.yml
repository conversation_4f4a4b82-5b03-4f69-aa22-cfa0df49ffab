#file: noinspection SpringBootApplicationYaml,SpellCheckingInspection
server:
  port: 9080

spring:
  redis:
    host: 127.0.0.1
    port: 6379
    timeout: 30000
    jedis:
      pool:
        max-idle: 100
        max-wait: 1000
  servlet:
    multipart:
      enabled: true

common:
  geckoDriver: C:\\develop\\chromedriver-win64\\chromedriver-win64\\chromedriver.exe
#  C:\\develop\\geckodriver.exe #启动的浏览器
#  C:\\develop\\chromedriver-win64\\chromedriver-win64\\chromedriver.exe
  # C:\\Program Files\\Mozilla Firefox\\geckodriver.exe 火狐
  # C:\Program Files\Google\Chrome\Application\chromedriver129.exe chrome
  residentPageUrl: https://www.sogou.com/ #常驻标签页


  proxy:
    ip: ************ # 代理服务器IP
    port: 80 # 代理服务器端口

  # 8浏览器 12线程最优
  browser:
    count: 1   #启动浏览器数量 6个固定给DownLoader，其他的Driver备用，特殊时将其设为1
  thread:
    count: 12   #启动线程数量 12线程最优

  # 批量落库，暂定以500条数据为一组
  database:
    batchNum: 500

  # 文件保存路径
  save-path: C:/Collect_Data/

mysql:
#  default:
#    host: 127.0.0.1
#    port: 3306
#    database: collect_job
#    username: root
#    password: 279110233
#  default:
#    host: 127.0.0.1
#    port: 3306
#    database: lszph
#    username: root
#    password: 279110233
  NewPHPdynamic:
    host: 127.0.0.1
    port: 3306
    database: cqmu
    username: root
    password: 279110233
#  OldPHPdynamic:
#    host: 127.0.0.1
#    port: 3306
#    database: old_cd5120
#    username: root
#    password: 279110233
  resourceDynamic:
    host: ***********
    port: 3306
    database: cloud-group-resource-cqmu
    username: root
    password: eNkSk4VE
#  buildDynamic:
#    host: ***********
#    port: 3306
#    database: cloud-group-build-gxhzsrmyy
#    username: root
#    password: eNkSk4VE
  buildDynamic:
    host: ***********
    port: 3306
    database: cloud-group-build-cqmu
    username: root
    password: eNkSk4VE
#  resourceDynamic:
#    host: ***********
#    port: 3306
#    database: cloud-group-station-lsz120
#    username: root
#    password: eNkSk4VE



mybatis-plus:
  global-config:
    db-config:
      id-type: input
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 配置枚举处理器
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  mapper-locations:
    - "classpath*:/mapper/**/*.xml"
    - "classpath*:/mapper2/**/*.xml"
