#file: noinspection SpringBootApplicationYaml,SpellCheckingInspection
server:
  port: 9080

spring:
  redis:
    host: *************
    port: 6379
    password: redis
    timeout: 30000
    jedis:
      pool:
        max-idle: 100
        max-wait: 1000
  servlet:
    multipart:
      enabled: true

common:
  geckoDriver: C:\\Program Files\\Mozilla Firefox\\geckodriver.exe #启动的浏览器
  # C:\\Program Files\\Mozilla Firefox\\geckodriver.exe 火狐
  # C:\Program Files\Google\Chrome\Application\chromedriver129.exe chrome
  residentPageUrl: https://www.sogou.com/ #常驻标签页

  proxy:
    ip: ************ # 代理服务器IP
    port: 80 # 代理服务器端口

  # 8浏览器 12线程最优
  browser:
    count: 6   #启动浏览器数量 6个固定给DownLoader，其他的Driver备用，特殊时将其设为1
  thread:
    count: 12   #启动线程数量 12线程最优

  # 批量落库，暂定以500条数据为一组
  database:
    batchNum: 500

  # 文件保存路径
  save-path: C:/Collect_Data/

mysql:
  default:
    host: **********
    port: 3306
    database: cd5120
    username: root
    password: 2003815zsy
  dynamic:
    host: ***********
    port: 13306
    database: foxhcs_cd5120
    username: root
    password: Ruifox@12345
    #新PHP
#  dynamic:
#    host: ***********
#    port: 13306
#    username: root
#    password: Ruifox@12345

#Java
#  RuiFoxdynamic:
#    host: ***********
#    port: 3306
#    database:
#    username: root
#    password: eNkSk4VE

#老PHP
#  NewPHPdynamic:
#    host: ***********
#    port: 3306
#    username: root
#    password: 698df57c53daf555

mybatis-plus:
  global-config:
    db-config:
      id-type: input
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 配置枚举处理器
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  mapper-locations: "classpath*:/mapper/**/*.xml"
