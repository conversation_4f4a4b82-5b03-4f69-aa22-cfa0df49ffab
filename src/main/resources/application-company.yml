##file: noinspection SpringBootApplicationY<PERSON>l,SpellCheckingInspection
#server:
#  port: 9080
#
#spring:
#  redis:
#    host:
#    port: 6379
#    password:
#    timeout: 30000
#    jedis:
#      pool:
#        max-idle: 100
#        max-wait: 1000
#
#common:
#  geckoDriver: C:\\Program Files\\Mozilla Firefox\\geckodriver.exe
#
#  proxy:
#    ip: ************ # 代理服务器IP
#    port: 80 # 代理服务器端口
#
#  # 4浏览器 12线程最优
#  browser:
#    count: 4
#  thread:
#    count: 12
#
#mysql:
#  default:
#    host:
#    port: 3306
#    database:
#    username:
#    password:
#  dynamic:
#    host: ***********
#    port: 3306
#    database:
#    username: root
#    password: eNkSk4VE
#
#
#mybatis-plus:
#  global-config:
#    db-config:
#      id-type: input
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    # 配置枚举处理器
#    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
#  mapper-locations: "classpath*:/mapper/**/*.xml"
