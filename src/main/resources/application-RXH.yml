#file: noinspection SpringBootApplicationYaml,SpellCheckingInspection
server:
  port: 9081

spring:
  redis:
    host: ***************
    port: 6379
    password: redis123
    timeout: 30000
    jedis:
      pool:
        max-idle: 100
        max-wait: 1000

common:
  geckoDriver: D:\\backen-developTools\\geckodriver-v0.36.0-win32\\geckodriver.exe  #启动的浏览器
  # C:\\Program Files\\Mozilla Firefox\\geckodriver.exe 火狐


  # C:\Program Files\Google\Chrome\Application\chromedriver129.exe chrome
  residentPageUrl: https://www.sogou.com/ #常驻标签页

  proxy:
    ip: ************ # 代理服务器IP
    port: 80 # 代理服务器端口

  # 8浏览器 12线程最优
  browser:
    count: 2   #启动浏览器数量 6个固定给DownLoader，其他的Driver备用，特殊时将其设为1
  thread:
    count: 12   #启动线程数量 12线程最优

  # 批量落库，暂定以500条数据为一组
  database:
    batchNum: 500

  # 文件保存路径
  save-path: D:/Collect_Data/


testDownload:
  config:
    #    excelUnKnown: "D:\\医院数据\\南宁市第二人民医院\\unknownDoctorList2.xlsx"
    #    汉源县中医院
    #    fileName: "D:\\Collect_Data\\myzyy"
    #    refer: "https://myzyy.master228.netms.net"

    #     江苏学会
    #    fileName: "D:\\Collect_Data\\hyxyy2"
    #    refer: "http://hyxzyy.lan24.foxtest.net/"

    #    四川大学华西第二医院|四川大学华西妇产儿童医院账号密码
    fileName: "D:\\Collect_Data\\motherchildren"
    refer: ""


    #    成都市第五人民医院
    #    fileName: "D:\\Collect_Data\\cd5120"
    #    refer: ""

    #    凉山州第一人民医院
#    fileName: "D:\\Collect_Data\\lsz120"
#    refer: ""

token:
  url: "http://***********:9100/auth/auth/login"
  #宁南县医院账号密码
  #  account: "nnxrmyy13158888925"
  #  password: "o5MHy1tEVyf3tAnJwR5a6b9JFZhveT4BwI8oaiZDxQ8="

  #汉源县中医院账号密码
  #  account: "hyxzyy13158888925"
  #  password: "Lm0cf7WSqmBqeyin6EuapSpWJHTOz4kwfRemR9Am4ys="

  #江苏学会账号密码
  #  account: "jrha13158888925"
  #  password: "+kzDqaoFT3ps5n9JnVpPMtG+G0tEZYOgve8KowHnWYI="

  #绵阳市中医医院账号密码
  #  account: "myzyy13158888925"
  #  password: "lULygag0sL8VUCnLCGF+OVgKUFObDGcf/pSa/jR3z5U="

  #绵阳市第三人民医院账号密码
  #  account: "mysdsrm13096378734"
  #  password: "+hemq6/yhn7SnyGmFteUgt/3YWrn5iOXQg+33ukKHS0="

  #四川大学华西第二医院|四川大学华西妇产儿童医院账号密码
  describe: "四川大学华西第二医院|四川大学华西妇产儿童医院"
  account: "motherchildren13158888925"
  password: "K3VlNZDk2PgSpuCj4wjMvykEoeLAjjMwJyKJFKKuTgs="
  refreshToken: "xxx"

  #成都市第五人民医院
  #  describe: "成都市第五人民医院"
  #  account: "cd513158888925"
  #  password: "YoLWv+5/Ag3YLoRRffgLLOBSlTWt3KVn8t3dP4JWUnI="
  #  refreshToken: "xxxxxxx"


  #凉山州第一人民医院
#  account: "lsz12013158888925"
#  password: "ki0SXIeGJ+RPmF8Q6LtvUuCDgy+CQz8wKeNIYJiSzsw="
#  refreshToken: "eyJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************.14nuhGSd6_vZXCZFaRq5sMw7sD_iF-PR_Y9GjWIhq3M"


mysql:
  default:
    host: ***************
    port: 3306
    database: collect
    username: root
    password: root
  handleDynamic:
    #    宁南第二医院
    #    host: ***************
    #    port: 3306
    #    database: nn2yy
    #    username: root
    #    password: root
    #    重庆医科大学附属永川医院
    #    host: ***************
    #    port: 3306
    #    database: cqmu
    #    username: root
    #    password: root
    #    jrha 吕凌相关文章
    host: ***************
    port: 3306
    database: jrha_for_someone
    username: root
    password: root

  reloadDynamic:
    #    华西二院重新导入测试
    #    host: ***************
    #    port: 3306
    #    database: resource_motherchildren
    #    username: root
    #    password: root
    #    成都第五人民医院重新导入
    host: ***********
    port: 3306
    database: cloud-group-resource-cd5120
    username: root
    password: eNkSk4VE
  oldJavaDynamic:
    #    凉山州第一人民医院
    host: ***********
    port: 3306
    database: cloud-group-station-lsz120
    username: root
    password: eNkSk4VE
    #    凉山州第一人民医院本地测试
  #    host: ***************
  #    port: 3306
  #    database: cloud-group-station-lsz120
  #    username: root
  #    password: root


  resourceDynamic:
    #   江苏学会测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-resource-jrha
    #    username: root
    #    password: root
    #   江苏学会
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-resource-jrha
    #    username: root
    #    password: eNkSk4VE
    #   宁南县测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-resource-nnxrmyy
    #    username: root
    #    password: root
    #   宁南县
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-resource-nnxrmyy
    #    username: root
    #    password: eNkSk4VE
    #    汉源县中医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-resource-hyxzyy
    #    username: root
    #    password: eNkSk4VE
    #    汉源县中医院测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-resource-hyxzyy
    #    username: root
    #    password: root

    #    绵阳中医院测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-resource-myzyy
    #    username: root
    #    password: root
    #    绵阳中医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-resource-myzyy
    #    username: root
    #    password: eNkSk4VE
    #    绵阳市第三人民医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-resource-mysdsrm
    #    username: root
    #    password: eNkSk4VE
    #    四川大学华西第二医院|四川大学华西妇产儿童医院
    host: ***********
    port: 3306
    database: cloud-group-resource-motherchildren
    username: root
    password: eNkSk4VE

    #    成都市第五人民医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-resource-cd5120
    #    username: root
    #    password: eNkSk4VE
    #    成都市第五人民医院测试
  #    host: ***************
  #    port: 3306
  #    database: cloud-group-resource-cd5120
  #    username: root
  #    password: root

  buildDynamic:
    #   江苏学会测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-build-jrha
    #    username: root
    #    password: root
    #   江苏学会
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-build-jrha
    #    username: root
    #    password: eNkSk4VE
    #   宁南县测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-build-nnxrmyy
    #    username: root
    #    password: root
    #   宁南县
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-build-nnxrmyy
    #    username: root
    #    password: eNkSk4VE
    #    #    汉源县中医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-build-hyxzyy
    #    username: root
    #    password: eNkSk4VE
    #    汉源县中医院测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-build-hyxzyy
    #    username: root
    #    password: root
    #    绵阳市中医医院测试
    #    host: ***************
    #    port: 3306
    #    database: cloud-group-build-myzyy
    #    username: root
    #    password: root
    #    绵阳市中医医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-build-myzyy
    #    username: root
    #    password: eNkSk4VE
    #    绵阳市第三人民医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-build-mysdsrm
    #    username: root
    #    password: eNkSk4VE

    #    四川大学华西第二医院|四川大学华西妇产儿童医院
    host: ***********
    port: 3306
    database: cloud-group-build-motherchildren
    username: root
    password: eNkSk4VE

    #    成都市第五人民医院
    #    host: ***********
    #    port: 3306
    #    database: cloud-group-build-cd5120
    #    username: root
    #    password: eNkSk4VE

    #    成都市第五人民医院测试
  #    host: ***************
  #    port: 3306
  #    database: cloud-group-build-cd5120
  #    username: root
  #    password: root

  haxyyDynamic:
    host: ***************
    port: 3306
    database: cloud-group-station-haxyy
    username: root
    password: root
  #  RuiFoxdynamic:
  #    host: ***********
  #    port: 3306
  #    database:
  #    username: root
  #    password: eNkSk4VE
  NewPHPdynamic:
    #PHP测试
    #    host: ***************
    #    port: 3306
    #    database: foxhcs_jrha
    #    username: root
    #    password: root
    #PHP
    #   江苏学会
    #    host: ***********
    #    port: 13306
    #    database: foxhcs_jrha
    #    username: root
    #    password: Ruifox@12345
    #   汉源县中医院
    #    host: ***********
    #    port: 13306
    #    database: foxhcs_hyxzyy
    #    username: root
    #    password: Ruifox@12345
    #   汉源县中医院最新
    #    host: ***************
    #    port: 3306
    #    database: foxhcs_hyxzyy
    #    username: root
    #    password: root
    #   绵阳市中医医院最新
    #    host: ***************
    #    port: 3306
    #    database: foxhcs_myzyy_new
    #    username: root
    #    password: root
    #   重庆医科大学附属永川医院
    #    host: ***********
    #    port: 13306
    #    database: foxhcs_ychcqmu
    #    username: root
    #    password: Ruifox@12345
    #   重庆医科大学附属永川医院测试
    #    host: ***************
    #    port: 3306
    #    database: foxhcs_ychcqmu
    #    username: root
    #    password: root

    #   成都市第五人民医院
    host: ***************
    port: 3306
    database: cd5120
    username: root
    password: root

  OldPHPdynamic:
    #    host: ***********
    #    port: 3306
    #    database:
    #    username: root
    #    password: 698df57c53daf555
    #   绵阳市中医医院
    #    host: ***************
    #    port: 3306
    #    database: foxhcs_myzyy
    #    username: root
    #    password: root

    #   四川大学华西第二医院|四川大学华西妇产儿童医院
    host: ***************
    port: 3306
    database: motherchildren_new
    username: root
    password: root

    #   成都市第五人民医院
    #    host: ***************
    #    port: 3306
    #    database: cd5120
    #    username: root
    #    password: root

    #   成都市第五人民医院最新
    #    host: ***************
    #    port: 3306
    #    database: cd5120_new
    #    username: root
    #    password: root

    #   成都市第五人民医院最新2
    #    host: ***************
    #    port: 3306
    #    database: cd5120_8_8
    #    username: root
    #    password: root

    #   凉山州第一人民医院
#    host: ***************
#    port: 3306
#    database: lszph
#    username: root
#    password: root

mybatis-plus:
  global-config:
    db-config:
      id-type: input
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 配置枚举处理器
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  mapper-locations:
    - "classpath*:/mapper/**/*.xml"
    - "classpath*:/mapper2/**/*.xml"
