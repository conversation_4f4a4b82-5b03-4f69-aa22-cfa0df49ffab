package com.ruifox.collect.updateInfo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.station.StationCategoryMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorDataMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.station.StationCategory;
import com.ruifox.collect.module.entity.station.StationMDoctor;
import com.ruifox.collect.module.entity.station.StationMDoctorData;
import com.ruifox.collect.system.DynamicDataSource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import us.codecraft.webmagic.selector.Json;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SpringBootTest
@RunWith(SpringRunner.class)
public class OneClickImportDepart {
    @Autowired
    StationMDoctorDataMapper stationMDoctorDataMapper;
    @Autowired
    StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    StationCategoryMapper stationCategoryMapper;

    @Test
    public void test001() {
        DynamicDataSource.changeDynamicDataSource();
        List<StationMDoctorData> stationMDoctorDatas = stationMDoctorDataMapper.selectList(null);
        LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationCategory::getType, 9);
        List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
        for (StationMDoctorData stationMDoctorData : stationMDoctorDatas) {
            String departJson = stationMDoctorData.getDepart();
            if (departJson != null && !departJson.isEmpty()) {
//                List<String> departs = JSONObject.parseArray(departJson, String.class);
                String[] split = departJson.split(",");
                List<String> departs = Arrays.stream(split).collect(Collectors.toList());
                for (String depart : departs) {
                    LambdaQueryWrapper<StationMDoctor> wrapper1 = new LambdaQueryWrapper<>();
                    wrapper1.eq(StationMDoctor::getDataId, stationMDoctorData.getDataId());
                    StationMDoctor stationMDoctor = stationMDoctorMapper.selectList(wrapper1).get(0);
                    stationMDoctor.setId(null);
                    stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");

                    for (StationCategory stationCategory : stationCategories) {
                        if (Objects.equals(depart, stationCategory.getId().toString())) {
                            int id = stationCategory.getId();
                            LambdaQueryWrapper<StationCategory> wrapper2 = new LambdaQueryWrapper<>();
                            wrapper2.eq(StationCategory::getPid, id)
                                    .eq(StationCategory::getName, "医生简介");
                            StationCategory stationCategory1 = stationCategoryMapper.selectOne(wrapper2);
                            stationMDoctor.setCatId(stationCategory1.getId());
                            break;
                        }
                    }

                    stationMDoctorMapper.insert(stationMDoctor);
                    Integer id = stationMDoctor.getId();
                    stationMDoctor.setSortLevel(id);
                    stationMDoctorMapper.updateById(stationMDoctor);
                }
            }
        }
    }

}
