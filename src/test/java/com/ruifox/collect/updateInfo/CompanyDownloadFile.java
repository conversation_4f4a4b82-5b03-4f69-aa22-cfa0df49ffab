package com.ruifox.collect.updateInfo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCPagesDataMapper;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.HmsCNewsData;
import com.ruifox.collect.module.entity.hms.HmsCPagesData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@SpringBootTest
@RunWith(SpringRunner.class)
public class CompanyDownloadFile {
    @Test
    public void test01() {
        try {
            CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById("484");
            DynamicDataSource.changeDynamicDataSource();

            HmsCNewsDataMapper hmsCNewsDataMapper = ApplicationContextProvider.getBeanByType(HmsCNewsDataMapper.class);

            LambdaQueryWrapper<HmsCNewsData> lqw = new LambdaQueryWrapper<>();
            //所有有未下载文件的数据的id
            List<Integer> list = List.of(1910,1911,1931,1933,1940,1951,1955,1987,2009,2046,2097,2098,2100,2142,2154,2161,2177,2179,2180,2196,2248,2371,2484,2525,2769,3634,3636,3644,3653,3654,3657,3660,3661,3667,3681,3700,3702,3722,3729,3773,3776,3792,3799,3801,3812,3813,3814,3816,3818,3819,3820,3821,3824,3834,3844,3846,3848,3851,3855,3857,3858,3860,3861,3862,3863,3864,3865,3878,3879,3881,3882,3886,3889,3890,3895,3897,3899,3905,3907,3911,3913,3917,3919,3920,3929,3933,3934,3935,3936,3939,3940,3942,3952,3955,3962,3964,3965,3966,3968,3969,3971,3972,3973,3974,3976,3978,3980,3983,3984,3986,3991,3992,3995,4000,4002,4003,4005,4007,4010,4024,4025,4026,4085,4088,4116,4134,4139,4143,4157,4162,4180,4195,4224,4229,4230,4246,4248,4254,4255,4260,4262,4264,4273,4291,4293,4294,4295,4296,4298,4300,4303,4306,4308,4309,4310,4311,4312,4313,4315,4316,4317,4319,4321,4322,4323,4325,4327,4330,4331,4333,4336,4337,4340,4342,4345,4346,4347,4348,4349,4350,4351,4352,4353,4354,4355,4356,4357,4360,4361,4362,4367,4368,4369,4370,4371,4372,4374,4375,4376,4377,4379,4380,4381,4382,4384,4385,4386,4387,4395,4396,4397,4399,4404,4405,4406,4407,4408,4411,4428,4431,4435,4441,4493,4504,4505,4507,4513,4535,4553,4555,4557,4563,4566,4571,4572,4573,4574,4575,4576,4578,4580,4581,4582,4617,4618,4621,4623,4625,4629,4632,4646,4647,4648,4710,4713,4721,4726,4727,4732,4748,4751,4754,4760,4765,4775,4785,4787,4791,4793,4797,4807,4813,4814,4816,4818,4821,4824,4949,5045,5047,5048,5050,5057,5058,5062,5071,5076,5079,5082,5083,5089,5177,7153,7164,7172,7175,7183,7187,7213,7242,7244,9989,10262,10300,10457,10466,11691,14512,14515,14518,14519,14520,14521,14522,14523,14524,14525,14526,14527,14528,14529,14530,14531,14532,14533,14534,14535,14536,14537,14538,14540,14541,14542,14543,14544,14545,14546,14547,14548,14549,14550,14551,14552,14553,14554,14555,14557,14558,14559,14560,14561,14562,14563,14564,14565,14566,14567,14568,14569,14570,14571,14572,14573,14574,14575,14576,14577,14578,14579,14580,14581,14582,14583,14584,14585,14586,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14666,14667,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14687,14689,14690,14691,14693,14694,14695,14697,14699,14700,14702,14704,14706,14708,14709,14711,14712,14715,14718,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14735,14737,14750,14783,14784,14789,14790,14793,14794,14795,15259,15589,15668,15669,15670,15675,15676,15677,15742,15743,15746,15747,15765,15789,15790,15791,16334,16339,16347,16348,16351,16354,16356,16357,16361,16370,16371,16386,16387,16388,16418,16419,16421,16425,16454,16460,16502,16503,16505,16506,16507,16509,16511,16512,16513,16514,16736,16741,16746,16756,16768,16772,16774,16802,16842,16845,16854,16857,16885,16889,16911,16912,16939,17061,17062,17063,17073,17074,17089,17113,17129,17146,17147,17152,17154,17160,17165,17166,17167,17168,17169,17170,17174,17178,17182,17186,17193,17194,17205,17218,17220,17221,17225,17226,17248,17259,17267,17270,17286,17291,17297,17305,17308,17311,17314,17315,17319,17322,17325,17327,17332,17333,17334,17338,17339,17345,17347,17348,17354,17367,17369,17374,17377,17378,17387,17397,17402,17406,17407,17409,17411,17414,17417,17420,17424,17436,17437,17438,17441,17459,17463,17478,17479,17483,17488,17489,17495,17501,17598,18591,18592,18646,18647,18665,18689,18732,18735,18736,18740,18743,18744,18747,18750,18751,18752,18753,18754,18755,18756,18757,18761,18762,18763,18767,18769,18770,18772,18773,18775,18804,18826,18827,18832,18834,18861,18863,18963,18964,18968,18974,18982,18994,18996,18998,19000,19002,19011,19018,19021,19022,19023,19025,19039,19053,19054,19066,19067,19070,19078,19094,19098,19101,19102,19107,19173,19201,19205,19208,19211,19216,19218,19236,19237,19238,19245,19247,19248,19251,19255,19256,19259,19260,19262,19263,19264,19266,19267,19268,19270,19271,19272,19273,19274,19275,19276,19277,19278,19283,19285,19286,19289,19290,19291,19293,19294,19297,19298,19300,19301,19305,19307,19310,19311,19313,19314,19315,19316,19317,19318,19319,19321,19322,19323,19324,19325,19326,19327,19328,19329,19330,19331,19334,19335,19336,19337,19338,19339,19340,19342,19344,19347,19348,19349,19350,19351,19352,19353,19361,19362,19365,19373,19377,19380,19381,19388,19401,19419,19435,19438,19448,19453,19460,19465,19476,19477,19479,19483,19484,19489,19492,19504,19506,19513,19522,19532,19541,19545,19704,19706,19712,19720,20187,20196,20230,20309,20310,20311,20320,20322,20325,20330,20331,20332,20334,20367,20370,20371,20386,20387,20388,20389,20551,20552,20553,20555,20556,20567,31616,31634,31638,31640,31647,31648,31650,31845,31911,31917,31918,31919,31921,31922,31923,31963,31975,31977,31978,32033,32036,32037,32038,32039,32040,32041,32042,32043,32044,32045,32046,32047,32048,32049,32050,32051,32052,32053,32054,32055,32056,32057,32058,32059,32060,32061,32062,32063,32064,32065,32066,32067,32068,32069,32070,32071,32072,32073,32074,32075,32076,32077,32079,32080,32081,32082,32083,32084,32085,32086,32087,32088,32089,32090,32091,32092,32094,32095,32096,32097,32098,32099,32100,32101,32102,32103,32104,32105,32106,32108,32109,32110,32111,32112,32113,32114,32116,32117,32125,32126,32127,32128,32129,32130,32131,32132,32133,32134,32153,32155,32159,32162,32163,32165,32166,32167,32171,32172,32177,32179,32180,32181,32183,32184,32185,32186,32187,32188,32189,32191,32193,32198,32199,32200,32202,32203,32206,32209,32211,32212,32213,32214,32216,32217,32218,32222,32223,32224,32225,32227,32229,32234,32235,32238,32239,32241,32242,32243,32246,32247,32268,32269,32340,32376,32377,32378,32379,32380,32381,32382,32383,32384,32385,32386,32387,32388,32389,32390,32391,32392,32393,32394,32395,32396,32397,32398,32399,32400,32401,32402,32403,32404,32405,32406,32407,32408,32409,32410,32411,32412,32413,32414,32415,32416,32417,32418,32419,32420,32421,32422,32423,32424,32425,32426,32427,32428,32429,32430,32431,32432,32433,32434,32435,32436,32501,32646,32667,32668,32670,32671,32673,32682,32683,32684,32783,32888,32906,32909,32911,32915,32917,33031,33032,33033,33035,33110,33111,33112,33113,33115,33116,33118,33119,33124,33125,33156,33158,33159,33163,33166,33167,33169,33171,33172,33174,33176,33178,33181,33182,33185,33186,33189,33191,33193,33200,33202,33205,33207,33210,33211,33212,33213,33214,33215,33216,33217,33218,33219,33220,33221,33222,33223,33224,33225,33227,33229,33242,33243,33244,33245,33247,33252,33416,33419,33462,33463,33500,33504,33505,33506,33507,33512,33513,33515,33516,33517,33518,33521,33522,33523,33524,33525,33526,33527,33528,33529,33530,33531,33532,33533,33534,33535,33536,33537,33538,33539,33542,33543,33546,33547,33549,33550,33552,33553,33554,33555,33556,33557,33558,33559,33560,33561,33562,33563,33564,33565,33566,33568,33570,33571,33572,33573,33574,33575,33576,33577,33584,33585,33586,33587,33588,33589,33597,33598,33599,33600,33601,33602,33603,33604,33605,33606,33607,33608,33609,33610,33611,33613,33614,33615,33616,33617,33618,33620,33621,33622,33623,33624,33625,33626,33627,33628,33629,33630,33631,33632,33633,33634,33635,33636,33637,33638,33640,33644,33645,33646,33647,33648,33649,33650,33651,33652,33653,33654,33655,33658,33659,33660,33661,33662,33671,33672,33673,33674,33675,33676,33679,33680,33684,33685,33686,33687,33688,33689,33690,33691,33692,33693,33696,33697,33698,33699,33701,33702,33703,33704,33705,33707,33708,33709,33716,33717,33718,33722,33737,33739,33740,33742,33743,33744,33763,33764,33765,33766,33767,33768,33769,33770,33771,33772,33783,33821,33822,33862,33863,33864,33865,33892,33893,33896,33897,33898,33899,33913,33924,33925,33926,33944,33949,33953,33954,33994,33996,34030,34031,34039,34040,34044,34045,34117,34118,34164,34165,34168,34171,34174,34175,34178,34179,34184,34185,34187,34189,34192,34193,34196,34197,34216,34217,34218,34219,34220,34221,34230,34231,34232,34233,34242,34243,34253,34254,34260,34261,34264,34270,34271,34278,34279,34280,34281,34288,34289,34290,34291,34292,34293,34294,34295,34298,34299,34300,34301,34304,34305,34308,34309,34310,34311,34312,34315,34316,34317,34318,34325,34326,34332,34333,34334,34335,34336,34337,34344,34345,34346,34347,34352,34359,34360,34363,34364,34366,34368,34373,34374,34375,34376,34381,34382,34383,34384,34395,34397,34398,34405,34406,34407,34408,34409,34410,34413,34414,34417,34418,34419,34420,34423,34424,34443,34444,34445,34446,34629,34630,34653,34654);

            lqw.in(HmsCNewsData::getDid, list);
            List<HmsCNewsData> hmsCNewsDataList = hmsCNewsDataMapper.selectList(lqw);

            System.out.println(hmsCNewsDataList.size());
            int r1 = 0;
            int r2 = 0;
            for(HmsCNewsData hmsCNewsData : hmsCNewsDataList) {
                try {
                    Long did = hmsCNewsData.getDid();
                    String content = hmsCNewsData.getContent();
                    String thumb = hmsCNewsData.getThumb();

                    Document document = Jsoup.parse(content);

                    // 下载图片，并去除无用属性
                    Elements imgList = document.select("img");
                    for(Element element : imgList) {
                        //对重庆附一的特殊处理
                        String attr = element.attr("src");
                        String alt = element.attr("alt");
                        attr = attr.replace("&amp;", "&");
                        if(attr.contains("https://www.hospital-cqmu.com")) {
                            attr = attr.replace("https://www.hospital-cqmu.com", "https://index.hospital-cqmu.com");
                        }
                        if(attr.startsWith("/__local/")) {
                            attr = "https://index.hospital-cqmu.com" + attr;
                        }
                        if(attr.contains("/__local/")) {
                            String localPath = FileDataManager.downloadFromHttpUrl(attr, "https://www.hospital-cqmu.com/", "pageId", collectTask);
                            if(StringUtils.isNotBlank(localPath)) {
                                //C:/Collect_Data/index.hospital-cqmu.com/，是我们存放下载资源的文件夹
                                //https://hospital-cqmu.netms.net/oss/，是线上的资源的前缀，可以通过查看其它的图片来确定
                                String value = localPath.replace("C:/Collect_Data/index.hospital-cqmu.com/", "https://hospital-cqmu.netms.net/oss/");
                                element.attr("src", value);
                            }
                        }
                        if(alt.contains("/__local/")){
                            element.attr("alt","");
                        }
                        element.removeAttr("vurl");
                        element.removeAttr("orisrc");
                    }

                    imgList = document.select("iframe");
                    for(Element element : imgList) {
                        String attr = element.attr("src");
                        attr = attr.replace("&amp;", "&");
                        if(attr.contains("https://www.hospital-cqmu.com")) {
                            attr = attr.replace("https://www.hospital-cqmu.com", "https://index.hospital-cqmu.com");
                        }
                        if(attr.startsWith("/__local/")) {
                            attr = "https://index.hospital-cqmu.com" + attr;
                        }
                        if(attr.contains("/__local/")) {
                            String localPath = FileDataManager.downloadFromHttpUrl(attr, "https://www.hospital-cqmu.com/", "pageId", collectTask);
                            if(StringUtils.isNotBlank(localPath)) {
                                String value = localPath.replace("C:/Collect_Data/index.hospital-cqmu.com/", "https://hospital-cqmu.netms.net/oss/");
                                element.attr("src", value);
                            }
                        }
                        element.removeAttr("vurl");
                        element.removeAttr("orisrc");
                    }


                    imgList = document.select("source");
                    for(Element element : imgList) {
                        String attr = element.attr("src");
                        attr = attr.replace("&amp;", "&");
                        if(attr.contains("https://www.hospital-cqmu.com")) {
                            attr = attr.replace("https://www.hospital-cqmu.com", "https://index.hospital-cqmu.com");
                        }
                        if(attr.startsWith("/__local/")) {
                            attr = "https://index.hospital-cqmu.com" + attr;
                        }
                        if(attr.contains("/__local/")) {
                            String localPath = FileDataManager.downloadFromHttpUrl(attr, "https://www.hospital-cqmu.com/", "pageId", collectTask);
                            if(StringUtils.isNotBlank(localPath)) {
                                String value = localPath.replace("C:/Collect_Data/index.hospital-cqmu.com/", "https://hospital-cqmu.netms.net/oss/");
                                element.attr("src", value);
                            }
                        }
                        element.removeAttr("vurl");
                        element.removeAttr("orisrc");
                    }

                    // 下载文件
                    Elements linkList = document.select("a");
                    for(Element element : linkList) {
                        String attr = element.attr("href");
                        attr = attr.replace("&amp;", "&");
                        if(attr.contains("https://www.hospital-cqmu.com")) {
                            attr = attr.replace("https://www.hospital-cqmu.com", "https://index.hospital-cqmu.com");
                        }
                        if(attr.startsWith("/__local/")) {
                            attr = "https://index.hospital-cqmu.com" + attr;
                        }
                        if(attr.contains("/__local/")) {
                            String localPath = FileDataManager.downloadFromHttpUrl(attr, "https://www.hospital-cqmu.com/", "pageId", collectTask);
                            if(StringUtils.isNotBlank(localPath)) {
                                String value = localPath.replace("C:/Collect_Data/index.hospital-cqmu.com/", "https://hospital-cqmu.netms.net/oss/");
                                element.attr("href", value);
                            }
                        }
                    }

                    //下载特殊的视频
                    Elements videoList = document.select("script");
                    for(Element element : videoList) {
                        String attr = element.attr("vurl");
                        attr = attr.replace("&amp;", "&");
                        if(attr.contains("https://www.hospital-cqmu.com")) {
                            attr = attr.replace("https://www.hospital-cqmu.com", "https://index.hospital-cqmu.com");
                        }
                        if(attr.startsWith("/__local/")) {
                            attr = "https://index.hospital-cqmu.com" + attr;
                        }
                        if(attr.contains("/__local/")) {
                            String localPath = FileDataManager.downloadFromHttpUrl(attr, "https://www.hospital-cqmu.com/", "pageId", collectTask);
                            if(StringUtils.isNotBlank(localPath)) {
                                String value = localPath.replace("C:Collect_Data/index.hospital-cqmu.com/", "https://hospital-cqmu.netms.net/oss/");
                                element.attr("vurl", value);
                            }
                        }
                    }


                    content = document.body().toString();
                    content = content.replace("<body>", "");
                    content = content.replace("</body>", "");
                    List<String> imgUrls = document.select("img").eachAttr("src");
                    List<String> linkUrls = document.select("a").eachAttr("href");
                    if(StringUtils.isBlank(thumb)) {
                        thumb = "";
                    } else {
                        if(thumb.contains("/__local/")) {
                            if(!imgUrls.isEmpty()) {
                                thumb = imgUrls.get(0);
                            } else {
                                thumb = "";
                            }
                        }
                    }
                    DynamicDataSource.changeDynamicDataSource();
                    LambdaUpdateWrapper<HmsCNewsData> luw = new LambdaUpdateWrapper<>();
                    luw.set(HmsCNewsData::getContent, content)
                            .set(HmsCNewsData::getContentImage, JsonUtil.obj2String(imgUrls))
                            .set(HmsCNewsData::getContentLink, JsonUtil.obj2String(linkUrls))
                            .set(HmsCNewsData::getThumb, thumb)
                            .eq(HmsCNewsData::getDid, did);
                    int update = hmsCNewsDataMapper.update(luw);
                    if(update < 1) {
                        throw new RuntimeException("下载失败，影响行数小于1");
                    }

                    System.out.println("修改成功， r1：" + (++r1) + "，r2：" + (r2));
                } catch (Exception e) {
                    System.out.println("修改失败， r1：" + (r1) + "，r2：" + (++r2));
                    RedisUtil.getInstance().lLeftPush("下载失败的id", hmsCNewsData.getDid().toString());
                    e.printStackTrace();
                }finally {
                    DynamicDataSource.changeDefaultDataSource();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
