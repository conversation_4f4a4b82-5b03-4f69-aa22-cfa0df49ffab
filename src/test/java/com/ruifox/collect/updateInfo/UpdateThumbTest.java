package com.ruifox.collect.updateInfo;

// 导入必要的类

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.module.entity.hms.HmsCMans;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;

@SpringBootTest // 标记此类为Spring Boot测试类
@RunWith(SpringRunner.class) // 指定使用SpringRunner来运行测试
public class UpdateThumbTest {

    @Test // 标记此方法为测试方法
    public void test() {
        try {
            String folderPath = "C:\\Users\\<USER>\\Desktop\\部分医生";
            List<String> jpgFiles = classifyJpgFiles(folderPath);
            //存放正常修改的医生
            Map<String, String> map = new HashMap<>();
            //存放科室姓名重复的医生
            Map<String, String> reMap = new HashMap<>();
            //存放未找到的医生
            Map<String, String> notFoundMap = new HashMap<>();

            DynamicDataSource.changeDynamicDataSource();

            HmsCMansMapper hmsCMansMapper = ApplicationContextProvider.getBeanByType(HmsCMansMapper.class);
            HmsCMansDataMapper hmsCMansDataMapper = ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class);
            HmsCategoryMapper hmsCategoryMapper = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class);

            LambdaQueryWrapper<HmsCMans> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(HmsCMans::getStatus, 99);
            List<HmsCMans> hmsCMans = hmsCMansMapper.selectList(wrapper1);
            List<Integer> dataIds = new ArrayList<>();
            hmsCMans.stream().forEach(hmsCMans1 -> dataIds.add(Math.toIntExact(hmsCMans1.getDataid())));

            LambdaQueryWrapper<HmsCMansData> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(HmsCMansData::getDid, dataIds);
            List<HmsCMansData> hmsCMansDataList = hmsCMansDataMapper.selectList(wrapper);

            for (String fileNameUrl : jpgFiles) {
                String[] split = fileNameUrl.split("\\+");
                int equalNum = 0;
                String name = split[1];
                for (HmsCMansData hmsCMansData : hmsCMansDataList) {
                    if (hmsCMansData.getTitle().equals(name)) {
                        map.put(fileNameUrl,String.valueOf(hmsCMansData.getDid()));
                        equalNum++;
                    }
                }
                //表示该医生在提供的数据中能被多次找到，即可能他叫张三，呼吸科，在呼吸科还有一个人叫张三丰，他去提供的数据中查找是就会重复
                //将其移除成功的map中
                if (equalNum > 1) {
                    map.remove(fileNameUrl);
                    reMap.put(name, fileNameUrl);
                }
                //表示该医生姓名都在提供的数据中无法被找到
                else if (equalNum == 0) {
                    notFoundMap.put(name, fileNameUrl);
                }
            }

            //根据已采集的医生数据去匹配提供的医生数据
//            for (HmsCMansData hmsCMansData : hmsCMansDataList) {
//                int equalNum = 0;
//                String departName = "";
//                for (String fileNameUrl : jpgFiles) {
//                    String title = hmsCMansData.getTitle();
//                    //先判断是否有这个名字，再判断科室是否相同
//                    if (fileNameUrl.contains(title)) {
////                        String departNameid = hmsCMansData.getDepart();
////                        if (departNameid.contains(",")) {
////                            String[] departNameIds = departNameid.split(",");
////                            for (String departNameId : departNameIds) {
////                                departName = hmsCategoryMapper.selectById(Integer.valueOf(departNameId)).getName();
////                                if (fileNameUrl.contains(departName)) {
////                                    equalNum++;
////                                    map.put(String.valueOf(hmsCMansData.getDid()), fileNameUrl);
////                                    break;
////                                }
////                            }
////                        } else {
////                            departName = hmsCategoryMapper.selectById(Integer.valueOf(departNameid)).getName();
////                            if (departName.equals("心脏大血管外科/胸外科")) {
////                                departName = "心脏大血管外科胸外科";
////                            }
////                            if (fileNameUrl.contains(departName)) {
////                                equalNum++;
////                                map.put(String.valueOf(hmsCMansData.getDid()), fileNameUrl);
////                            }
////                        }
//
//                        //二次提供数据，直接匹配名字
//                        map.put(String.valueOf(hmsCMansData.getDid()), fileNameUrl);
//                        equalNum++;
//                    }
//                }
//                String did = String.valueOf(hmsCMansData.getDid());
//
//                String info = did + "  " + hmsCMansData.getTitle();
//                //表示该医生在提供的数据中能被多次找到，即可能他叫张三，呼吸科，在呼吸科还有一个人叫张三丰，他去提供的数据中查找是就会重复
//                //将其移除成功的map中
//                if (equalNum > 1) {
//                    map.remove(did);
//                    reMap.put(info, departName);
//                }
//                //表示该医生姓名都在提供的数据中无法被找到
//                else if (equalNum == 0) {
//                    if (departName == "") {
//                        String departNameid = hmsCMansData.getDepart();
//                        StringBuilder departNames = new StringBuilder();
//                        if (!departNameid.isEmpty()) {
//                            String[] departNameIds = null;
//                            if (departNameid.contains(",")) {
//                                departNameIds = departNameid.split(",");
//                            }
//                            if (departNameIds != null) {
//                                for (String departNameId : departNameIds) {
//                                    departNames.append(hmsCategoryMapper.selectById(Integer.valueOf(departNameId)).getName());
//                                    departNames.append(",");
//                                }
//                            } else {
//                                departNames.append(hmsCategoryMapper.selectById(Integer.valueOf(departNameid)).getName());
//                            }
//                            departNames.append("——未找到该医生");
//                        }
//                        notFoundMap.put(info, departNames.toString());
//                    } else {
//                        notFoundMap.put(info, departName);
//                    }
//                }
//            }

            //目前除了时分秒后只有3位数字，这里又是for循环，随机数很可能会重复，所有因为目前医生数量小于1000，直接按顺序依次添加
            Random random = new Random();
            int i = 1;
            for (String key : map.keySet()) {
                //int i = random.nextInt(900) + 100;
                LocalDateTime localDateTime = LocalDateTime.now();
                String hour = String.format("%02d", localDateTime.getHour());
                String minute = String.format("%02d", localDateTime.getMinute());
                String temp = String.format("%05d", i);
                String filename = hour + minute + temp;
                String newUrl = "C:\\Collect_Data\\www.myzyy.com\\20250513\\" + filename + ".jpg";
                String newThumb = "http://myzyy.1024199.foxtest.net/oss/20250513/" + filename + ".jpg";
                String url = key;
                File sourceFile = new File(url);
                File destinationFile = new File(newUrl);
                try {
                    Files.copy(sourceFile.toPath(), destinationFile.toPath());
                    LambdaUpdateWrapper<HmsCMansData> luw = new LambdaUpdateWrapper<>();
                    luw.set(HmsCMansData::getThumb, newThumb).eq(HmsCMansData::getDid, map.get(key));
                    hmsCMansDataMapper.update(luw);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                i++;
            }

            RedisUtil.getInstance().set("成功的医生", JsonUtil.obj2String(map));
            RedisUtil.getInstance().set("成功的数量", JsonUtil.obj2String(map.size()));
            RedisUtil.getInstance().set("失败的数量", JsonUtil.obj2String(hmsCMansDataList.size() - map.size()));

            try {
                // 创建工作簿
                Workbook workbook = new XSSFWorkbook();
                // 创建工作表
                Sheet sheet = workbook.createSheet("重复的医生");
                Sheet sheet1 = workbook.createSheet("未找到的医生");

                int rowNum = 0;
                for (Map.Entry<String, String> entry : reMap.entrySet()) {
                    Row row = sheet.createRow(rowNum++);
                    Cell keyCell = row.createCell(0);
                    Cell valueCell = row.createCell(1);
                    keyCell.setCellValue(entry.getKey());
                    valueCell.setCellValue(entry.getValue());
                }
                for (Map.Entry<String, String> entry : notFoundMap.entrySet()) {
                    Row row = sheet1.createRow(rowNum++);
                    Cell keyCell = row.createCell(0);
                    Cell valueCell = row.createCell(1);
                    keyCell.setCellValue(entry.getKey());
                    valueCell.setCellValue(entry.getValue());
                }

                // 将工作簿写入文件
                FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\重复和未找到.xlsx");
                workbook.write(outputStream);
                workbook.close();
                outputStream.close();

                System.out.println("Excel 文件生成成功！");
            } catch (Exception e) {
                e.printStackTrace();
            }


        } catch (Exception e) {
            e.printStackTrace(); // 捕获异常并打印堆栈信息
        } finally {
            // 切换回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    // 方法：分类jpg文件
    public static List<String> classifyJpgFiles(String folderPath) {
        List<String> jpgFiles = new ArrayList<>(); // 初始化jpg文件列表
        File folder = new File(folderPath); // 创建文件对象表示文件夹
        File[] files = folder.listFiles(); // 获取文件夹中的所有文件
        if (files != null) { // 如果文件不为空
            for (File file : files) { // 遍历每个文件
                if (file.isDirectory()) {
                    classifyJpgFiles(file.getAbsolutePath()).forEach(jpgFiles::add); // 递归处理子文件夹
                }
                // 检查是否为文件且扩展名为.jpg
                if (file.isFile() && file.getName().toLowerCase().endsWith(".jpg")) {
                    jpgFiles.add(file.getAbsolutePath()); // 将文件的绝对路径添加到列表
                }
            }
        }
        return jpgFiles; // 返回jpg文件列表
    }

    @Test
    public void tete1(){
        String a = "{1111}";
        System.out.println(a.substring(1,a.length()-1));
    }
}
