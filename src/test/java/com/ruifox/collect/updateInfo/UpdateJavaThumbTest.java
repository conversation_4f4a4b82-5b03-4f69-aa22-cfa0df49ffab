package com.ruifox.collect.updateInfo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorDataMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.module.entity.station.StationMDoctorData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

@SpringBootTest // 标记此类为Spring Boot测试类
@RunWith(SpringRunner.class) // 指定使用SpringRunner来运行测试
public class UpdateJavaThumbTest {
    @Autowired
    private CommonMapper commonMapper;

    @Test // 标记此方法为测试方法
    public void test() {
        try {
            /*String folderPath = "C:\\Users\\<USER>\\Desktop\\WPS图片批量处理";
            List<String> jpgFiles = classifyJpgFiles(folderPath);*/
            //存放正常修改的医生
            Map<String, String> map = new HashMap<>();
            //存放科室姓名重复的医生
            Map<String, String> reMap = new HashMap<>();
            //存放未找到的医生
            Map<String, String> notFoundMap = new HashMap<>();

            List<Map<String,Object>> list = commonMapper.selectDoctorThumb("gdjnh_doctor_479");

            DynamicDataSource.changeDynamicDataSource();

            StationMDoctorDataMapper stationMDoctorDataMapper = ApplicationContextProvider.getBeanByType(StationMDoctorDataMapper.class);
            List<StationMDoctorData> stationMDoctorDatas = stationMDoctorDataMapper.selectList(null);
            for (StationMDoctorData stationMDoctorData : stationMDoctorDatas) {
                int num = 0;
                String name = stationMDoctorData.getTitle();
                /*for (String fileNameUrl : jpgFiles) {
                    if(fileNameUrl.contains(name)) {
                        map.put(stationMDoctorData.getTitle(), fileNameUrl);
                        num++;
                    }
                }*/
                for (Map<String,Object> map1 : list){
                    if(map1.get("title").equals(stationMDoctorData.getTitle())){
                        map.put(stationMDoctorData.getTitle(), map1.get("thumb").toString());
                        num++;
                    }
                }
                if(num > 1){
                    reMap.put(stationMDoctorData.getTitle(), stationMDoctorData.getDepart());
                    map.remove(stationMDoctorData.getTitle());
                }else if(num == 0){
                    notFoundMap.put(stationMDoctorData.getTitle(), stationMDoctorData.getDepart());
                }
            }


            for (String key : map.keySet()) {
                String url = map.get(key);
                String thumb = CrawlerManager.changeFileUrl(new File(url));
                LambdaUpdateWrapper<StationMDoctorData> luw = new LambdaUpdateWrapper<>();
                luw.set(StationMDoctorData::getThumb, thumb).eq(StationMDoctorData::getTitle, key);
                stationMDoctorDataMapper.update(luw);
            }

            RedisUtil.getInstance().set("成功的医生", JsonUtil.obj2String(map));
            RedisUtil.getInstance().set("成功的数量", JsonUtil.obj2String(map.size()));
            RedisUtil.getInstance().set("失败的数量", JsonUtil.obj2String(stationMDoctorDatas.size() - map.size()));

            try {
                // 创建工作簿
                Workbook workbook = new XSSFWorkbook();
                // 创建工作表
                Sheet sheet = workbook.createSheet("sheet1");
                Sheet sheet1 = workbook.createSheet("sheet2");

                int rowNum = 0;
                for (Map.Entry<String, String> entry : reMap.entrySet()) {
                    Row row = sheet.createRow(rowNum++);
                    Cell keyCell = row.createCell(0);
                    Cell valueCell = row.createCell(1);
                    keyCell.setCellValue(entry.getKey());
                    valueCell.setCellValue(entry.getValue());
                }
                for (Map.Entry<String, String> entry : notFoundMap.entrySet()) {
                    Row row = sheet1.createRow(rowNum++);
                    Cell keyCell = row.createCell(0);
                    Cell valueCell = row.createCell(1);
                    keyCell.setCellValue(entry.getKey());
                    valueCell.setCellValue(entry.getValue());
                }

                // 将工作簿写入文件
                FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\重复和未找到.xlsx");
                workbook.write(outputStream);
                workbook.close();
                outputStream.close();

                System.out.println("Excel 文件生成成功！");
            } catch (Exception e) {
                e.printStackTrace();
            }


        } catch (Exception e) {
            e.printStackTrace(); // 捕获异常并打印堆栈信息
        } finally {
            // 切换回默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    // 方法：分类jpg文件
    public static List<String> classifyJpgFiles(String folderPath) {
        List<String> jpgFiles = new ArrayList<>(); // 初始化jpg文件列表
        File folder = new File(folderPath); // 创建文件对象表示文件夹
        File[] files = folder.listFiles(); // 获取文件夹中的所有文件
        if (files != null) { // 如果文件不为空
            for (File file : files) { // 遍历每个文件
                if (file.isDirectory()) {
                    classifyJpgFiles(file.getAbsolutePath()).forEach(jpgFiles::add); // 递归处理子文件夹
                }
                // 检查是否为文件且扩展名为.jpg
                if (file.isFile() && file.getName().toLowerCase().endsWith(".jpg")) {
                    jpgFiles.add(file.getAbsolutePath()); // 将文件的绝对路径添加到列表
                }
            }
        }
        return jpgFiles; // 返回jpg文件列表
    }
}
