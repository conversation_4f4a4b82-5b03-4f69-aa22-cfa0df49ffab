package com.ruifox.collect.updateInfo;

import com.ruifox.collect.dao.mapper.NewsTableMapper;
import com.ruifox.collect.module.entity.news.NewsTable;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class UpdateFileNewsInfoTest {
    @Autowired
    NewsTableMapper newsTableMapper;

    @Test
    public void test() {
        List<NewsTable> newsTables = newsTableMapper.selectList(null);
        for (NewsTable newsTable : newsTables) {
            String content = newsTable.getContent();
            Document document = Jsoup.parse(content);
            Elements select = document.select("a");
            for (Element element : select) {
                String href = element.attr("href");
                if(href.contains("http://www.xxzrmyy.com/include/FCKeditor/editor/")) {
                    element.attr("href","");
                }
            }
            content = document.toString();
            newsTable.setContent(content);
            newsTableMapper.updateById(newsTable);
        }
    }
}
