package com.ruifox.collect.updateInfo;

// 导入必要的类

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;

@SpringBootTest // 标记此类为Spring Boot测试类
@RunWith(SpringRunner.class) // 指定使用SpringRunner来运行测试
public class UpdateThumbUrlTest {

    @Test // 标记此方法为测试方法
    public void test() {
        DynamicDataSource.changeDynamicDataSource();

        Map<String, String> map = JsonUtil.Json2Obj(RedisUtil.getInstance().get("成功的医生"), Map.class);
        List<String> list = new ArrayList<>();
        map.keySet().forEach(key -> list.add(key));
        HmsCMansDataMapper hmsCMansDataMapper = ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class);
        LambdaQueryWrapper<HmsCMansData> mapper = new LambdaQueryWrapper<>();
        mapper.in(HmsCMansData::getDid, list);
        List<HmsCMansData> hmsCMansDataList = hmsCMansDataMapper.selectList(mapper);
        for (HmsCMansData hmsCMansData : hmsCMansDataList) {
            String thumb = hmsCMansData.getThumb();
            thumb = thumb.replace("https://cdlyy.netms.net/","http://cdlyy-foxhcs.foxtest.net/");
            hmsCMansData.setThumb(thumb);
            LambdaUpdateWrapper<HmsCMansData> luw = new LambdaUpdateWrapper<>();
            luw.set(HmsCMansData::getThumb, thumb).eq(HmsCMansData::getDid, hmsCMansData.getDid());
            hmsCMansDataMapper.update(luw);
        }

        DynamicDataSource.changeDefaultDataSource();
    }
}
