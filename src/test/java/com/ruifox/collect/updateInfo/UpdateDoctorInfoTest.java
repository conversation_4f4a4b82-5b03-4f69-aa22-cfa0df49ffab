package com.ruifox.collect.updateInfo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.module.entity.hms.HmsCMans;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
@RunWith(SpringRunner.class)
public class UpdateDoctorInfoTest {

    @Test
    public void test() {
        try {

            DynamicDataSource.changeDynamicDataSource();

            HmsCMansDataMapper hmsCMansDataMapper = ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class);
            HmsCMansMapper hmsCMansMapper = ApplicationContextProvider.getBeanByType(HmsCMansMapper.class);
            List<String> list = List.of("6", "7", "8", "9", "10");
            LambdaQueryWrapper<HmsCMansData> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(HmsCMansData::getDocPosition, list);
            List<HmsCMansData> hmsCMansDataList = hmsCMansDataMapper.selectList(wrapper);

            for (HmsCMansData hmsCMansData : hmsCMansDataList) {
                Long did = hmsCMansData.getDid();
                LambdaQueryWrapper<HmsCMans> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCMans::getDataid, did);
                hmsCMansMapper.delete(wrapper1);
                    LambdaQueryWrapper<HmsCMansData> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(HmsCMansData::getDid, did);
                hmsCMansDataMapper.delete(wrapper2);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}



