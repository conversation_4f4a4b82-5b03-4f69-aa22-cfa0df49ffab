package com.ruifox.collect.updateInfo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsDataMapper;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.module.entity.hms.HmsCNewsData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class UpdateNewsInfoTest {

    @Test
    public void test() {
        try {

            DynamicDataSource.changeDynamicDataSource();

            HmsCNewsDataMapper hmsCNewsDataMapper = ApplicationContextProvider.getBeanByType(HmsCNewsDataMapper.class);
            List<HmsCNewsData> hmsCNewsDataList = hmsCNewsDataMapper.selectList(new LambdaQueryWrapper<>());

            for (HmsCNewsData hmsCNewsData : hmsCNewsDataList) {

                Long did = hmsCNewsData.getDid();

                String content = hmsCNewsData.getContent();
                Document document = Jsoup.parse(content);
                Elements elements = document.select("div");
                for (Element element : elements) {
                    if (element.select("img").isEmpty()) {
                        element.attr("style","font-size: 18px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2;");
                    }
                }
                content = document.toString();
                LambdaUpdateWrapper<HmsCNewsData> wrapper = new LambdaUpdateWrapper<>();
                wrapper.set(HmsCNewsData::getContent, content).eq(HmsCNewsData::getDid, did);
                hmsCNewsDataMapper.update(wrapper);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}



