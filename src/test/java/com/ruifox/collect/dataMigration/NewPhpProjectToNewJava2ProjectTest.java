package com.ruifox.collect.dataMigration;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.dao.mapper.station.StationCategoryMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station.StationCategory;
import com.ruifox.collect.module.entity.station2.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class NewPhpProjectToNewJava2ProjectTest {
    @Autowired
    private HmsCNewsMapper hmsCNewsMapper;
    @Autowired
    private HmsCNewsDataMapper hmsCNewsDataMapper;
    @Autowired
    private HmsCMansMapper hmsCMansMapper;
    @Autowired
    private HmsCImageMapper hmsCImageMapper;
    @Autowired
    private HmsCImageDataMapper hmsCImageDataMapper;

    @Autowired
    private StationMImageDataMapper2 stationMImageDataMapper;
    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;
    @Autowired
    private HmsCMansDataMapper hmsCMansDataMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private StationMDoctorDataMapper2 stationMDoctorDataMapper;
    @Autowired
    private StationCategoryMapper stationCategoryMapper;

    @Autowired
    private StationCategoryMapper2 stationCategoryMapper2;

    @Autowired
    private HmsModelFieldMapper hmsModelFieldMapper;
    @Autowired
    private StationModelFieldMapper2 stationModelFieldMapper2;

    //广西横州市医院
    private String fileName = "C:\\Collect_Data\\cqmu";
    private String refer = "https://master.hospital-cqmu.com/";


    private static final Set<Integer> SUCCESSFUL_IDS = Set.of(
            1024, 1025, 1026, 1027, 4, 1028, 1029, 1032, 1033, 1034, 1035, 1036,
            1040, 1041, 1042, 1043, 1044, 1046, 1047, 1048, 1049, 1050, 1051, 1053,
            1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1064, 1065, 1066, 1078,
            1079, 1080, 1081, 1083, 1084, 1089, 1090, 1091, 1092, 1094, 1095, 1100,
            1101, 1102, 1103, 1105, 1106, 1118, 1119, 1120, 1121, 1122, 1123, 1132,
            1135, 1136, 1137, 114, 1138, 115, 1140, 1141, 1144, 123, 1148, 126,
            1153, 1154, 132, 1156, 133, 134, 1158, 135, 1159, 1160, 137, 138,
            140, 1164, 1165, 142, 1166, 143, 144, 145, 147, 1171, 1172, 149,
            1173, 150, 1174, 151, 152, 1176, 1177, 1178, 157, 1181, 158, 159,
            160, 1184, 161, 1185, 1186, 163, 1187, 164, 1188, 1189, 166, 1190,
            167, 168, 1192, 1193, 1194, 1195, 172, 1196, 1199, 176, 1200, 1201,
            1202, 180, 1205, 1207, 184, 1208, 1209, 1211, 188, 1214, 1215, 192,
            1216, 196, 1221, 1223, 200, 1224, 1225, 204, 208, 212, 216, 1242,
            220, 1244, 1245, 224, 1248, 1249, 228, 1252, 1254, 1255, 232, 1256,
            1257, 1258, 1259, 236, 1260, 1261, 1262, 1263, 240, 1264, 1265, 1266,
            244, 1271, 248, 1272, 1273, 252, 256, 1282, 1283, 260, 264, 268,
            272, 276, 1300
    );


    @Test
    public void moveNewsJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();

        Map<Integer, Integer> map = new HashMap<>();

//        getNewsID(map);

//        removeSuccessfulColumns(map);

        // 综合新闻
//        map.put(4, 11);
//        map.put(353,255);


        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i] + " ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    /**
     * 从给定的Map中移除所有键（ID）存在于成功列表中的条目。
     * 这个方法会直接修改传入的Map对象，得到的结果就是所有未成功运行的栏目。
     *
     * @param dataMap 待处理的Map，键为栏目ID (Integer)，值为任意类型 (V)。
     *                这个Map通常包含了所有的栏目数据。
     * @param <V>     Map中值的泛型类型。
     */
    public <V> void removeSuccessfulColumns(Map<Integer, V> dataMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            return; // 如果map为空或null，则无需操作
        }
        dataMap.keySet().removeIf(SUCCESSFUL_IDS::contains);
    }

    /**
     * 【重构优化版】获取源ID到目标ID的映射关系
     * 1. 修正了匹配逻辑，使其能处理更复杂的URI/URL关系。
     * 2. 通过使用Map大幅提升了性能，避免了双重循环。
     *
     * @param map 用于存放最终ID映射结果的Map
     */
    public void getNewsID(Map<Integer, Integer> map) {
        // 1. 切换到源数据源，一次性获取所有 modelId=1 的新闻分类 (逻辑不变)
        DynamicDataSource.changeNewPhpDynamicDataSource();
        LambdaQueryWrapper<HmsCategory> sourceWrapper = new LambdaQueryWrapper<>();
        sourceWrapper.eq(HmsCategory::getModelId, 1).isNull(HmsCategory::getDeletedAt);
        List<HmsCategory> sourceCategories = hmsCategoryMapper.selectList(sourceWrapper);

        if (sourceCategories == null || sourceCategories.isEmpty()) {
            log.info("没有需要迁移的源分类数据。");
            return;
        }

        // 2. 切换到目标数据源，一次性获取所有 modelId=20 的分类 (逻辑不变)
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationCategory2> targetWrapper = new LambdaQueryWrapper<>();
        targetWrapper.eq(StationCategory2::getModelId, 20);
        List<StationCategory2> targetCategories = stationCategoryMapper2.selectList(targetWrapper);

        if (targetCategories == null || targetCategories.isEmpty()) {
            log.info("没有可供映射的目标分类数据。");
            return;
        }

        // 【性能优化】将目标分类列表转换为Map，以分类名称为Key。
        // Value是List，因为可能存在同名的目标分类。
        // 这样查找的时间复杂度从 O(M) 降为 O(1)。
        Map<String, List<StationCategory2>> targetMapByName = targetCategories.stream()
                .filter(c -> c.getName() != null) // 过滤掉没有名称的目标分类
                .collect(Collectors.groupingBy(StationCategory2::getName));

        // 3. 在内存中高效地执行匹配逻辑
        for (HmsCategory sourceCategory : sourceCategories) {
            if (sourceCategory.getName() == null) {
                log.warn("源分类名称为空，已跳过。源ID: {}", sourceCategory.getId());
                continue;
            }

            // 【性能优化】通过名称直接从Map中获取候选列表，而不是遍历整个targetCategories
            List<StationCategory2> potentialMatches = targetMapByName.get(sourceCategory.getName());

            // 如果根据名称找不到任何候选，直接记录并跳过
            if (potentialMatches == null || potentialMatches.isEmpty()) {
                log.warn("根据名称未能找到任何候选目标分类。源ID: {}, 名称: {}", sourceCategory.getId(), sourceCategory.getName());
                continue;
            }

            Optional<StationCategory2> finalMatchOpt;
            String pString = sourceCategory.getPString();

            // 判断源是否为英文数据
            boolean isSourceEnglish = (pString != null && pString.contains("374"));

            if (isSourceEnglish) {
                // --- 规则 A: 源为英文数据 ---
                // 在重名候选项中，只寻找同样是英文站的目标 (site_id=3)
                finalMatchOpt = potentialMatches.stream()
                        .filter(t -> t.getSiteId() != null && t.getSiteId() == 3)
                        .findFirst(); // 按名称匹配即可，取第一个找到的英文目标

                if (finalMatchOpt.isPresent()) {
                    log.info("英文源匹配成功 (p_string含374): 源ID[{}], 名称[{}]",
                            sourceCategory.getId(), sourceCategory.getName());
                }
            } else {
                // --- 规则 B: 源为非英文数据 ---
                // 在重名候选项中，寻找非英文站的目标，并应用复杂的URL规则
                finalMatchOpt = potentialMatches.stream()
                        .filter(t -> t.getSiteId() != null && t.getSiteId() != 3)
                        .filter(t -> areNonEnglishSitesMatching(sourceCategory, t)) // 调用URL匹配引擎
                        .findFirst();

                if (finalMatchOpt.isPresent()) {
                    log.info("非英文源URL匹配成功: 源ID[{}], 名称[{}]",
                            sourceCategory.getId(), sourceCategory.getName());
                }
            }

            // 如果找到了最终的匹配项，则存入Map
            if (finalMatchOpt.isPresent()) {
                map.put(Math.toIntExact(sourceCategory.getId()), finalMatchOpt.get().getId());
            } else {
                log.warn("未能为源ID[{}]找到任何匹配项, 名称: '{}', p_string: '{}'",
                        sourceCategory.getId(), sourceCategory.getName(), pString);
            }
        }
    }

    /**
     * 匹配引擎：仅用于非英文站，执行复杂的URL匹配规则。
     * 包含“全路径匹配”和“首尾分段匹配”双重逻辑。
     *
     * @param sourceCategory 源分类对象
     * @param targetCategory 目标分类对象
     * @return 如果匹配则返回 true，否则返回 false
     */
    private boolean areNonEnglishSitesMatching(HmsCategory sourceCategory, StationCategory2 targetCategory) {
        String sourceUrl = sourceCategory.getUrl();
        String targetUri = targetCategory.getUri();

        if (sourceUrl == null || sourceUrl.isEmpty() || targetUri == null) {
            return false;
        }

        // 准备好不含'/'的核心目标URI
        String coreTargetUri = targetUri.startsWith("/") ? targetUri.substring(1) : targetUri;

        // --- 规则1：全路径直接匹配 ---
        if (sourceUrl.equals(coreTargetUri)) {
            return true;
        }

        // --- 规则2：首尾分段匹配 ---
        // 1. 提取核心源URL
        String coreSourceUrl;
        int firstUnderscoreIndex = sourceUrl.indexOf('_');
        if (firstUnderscoreIndex != -1) {
            coreSourceUrl = sourceUrl.substring(firstUnderscoreIndex + 1);
        } else {
            return false;
        }

        if (coreSourceUrl.isEmpty()) {
            return false;
        }

        // 2. 分割
        String[] sourceParts = coreSourceUrl.split("_");
        String[] targetParts = coreTargetUri.split("_");

        // 3. 校验
        if (sourceParts.length == 0 || targetParts.length == 0) {
            return false;
        }

        // 4. 匹配
        boolean firstPartMatches = !sourceParts[0].isEmpty() && sourceParts[0].equals(targetParts[0]);
        boolean lastPartMatches = sourceParts[sourceParts.length - 1].equals(targetParts[targetParts.length - 1]);

        return firstPartMatches && lastPartMatches;
    }


    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        LambdaQueryWrapper<HmsCNews> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息与判断是否为发布状态
        wrapper.eq(HmsCNews::getCatid, originCatId)
                .eq(HmsCNews::getState, 0)
                .eq(HmsCNews::getStatus, 99)
//                .gt(HmsCNews::getPublishTime,1749469679)    // TODO 临时需要
                .orderByAsc(HmsCNews::getListorder);

        List<HmsCNews> originNews = hmsCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCNews>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMArticleData2> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMArticleData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMArticleData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMArticleData2 targetNewData = mergedMap.get(targetNew);

            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetNew);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId, targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMArticleDataMapper.insert(targetNewData);
            //落库 -> folder_resource
            FolderResource folderResource = new FolderResource().builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetNewData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetNewData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetNew.setDataId(Long.valueOf(folderResource.getId()));
            targetNew.setSortLevel(Math.toIntExact(targetNew.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetNew);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start >= end) {
                start = end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMReference, StationMArticleData2> processTaskChunk(
            List<HmsCNews> chunk, Integer targetCatId) {
        Map<StationMReference, StationMArticleData2> chunkResult = new LinkedHashMap<>();
        try {
            for (HmsCNews originNew : chunk) {
                // 新的news
                StationMReference stationMArticle = new StationMReference();
                stationMArticle.setCatId(targetCatId);
                stationMArticle.setPublishTime(Double.valueOf(originNew.getPublishTime() + "000"));
                stationMArticle.setState(99);
                stationMArticle.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMArticle.setIsTop(0);

                String linkUrl = originNew.getIslink();
                int isLink = 0;
                if (linkUrl != null && !linkUrl.isEmpty()) {
                    isLink = 1;
                }

                DynamicDataSource.changeNewPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<HmsCNewsData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCNewsData::getDid, originNew.getDataid());
                HmsCNewsData originNewsData = hmsCNewsDataMapper.selectOne(wrapper1);

                //处理访问量
                stationMArticle.setViews(originNewsData.getViews());
                // 新的articleData
                StationMArticleData2 stationMArticleData = new StationMArticleData2();
                stationMArticleData.setUuid(UUID.randomUUID().toString());
                stationMArticleData.setTitle(originNewsData.getTitle());
                stationMArticleData.setIgnoreReason(null);
                stationMArticleData.setPhotographer(null);
                stationMArticleData.setCreateUserId(1);
                stationMArticleData.setUpdateUserId(1);
                stationMArticleData.setState(2);
                // 处理作者
                String author = originNewsData.getAuthor();
                author = changeSpecialChat(author);
                if (!author.equals("0") && !author.isEmpty())
                    stationMArticleData.setAuthor(originNewsData.getAuthor());
                if (author.equals("[]")) {
                    stationMArticleData.setAuthor("");
                }
                // 缩略图处理 （只有一张图片）
                // TODO 临时特殊处理
                String oss = originNewsData.getThumb();
                if (oss != null && !oss.isBlank()) {
                    // TODO 临时特殊处理
//                    String thumb = oss.replace( oss.substring(0, oss.indexOf("oss")),"https://www.jrha.net.cn/");
//                    System.out.println(thumb);
                    String thumb = originNewsData.getThumb();
                    // 调用接口
                    if (!thumb.isBlank()) {
                        thumb = CrawlerManager.DownLoad(thumb, fileName + "\\news", refer);
                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    }
                    stationMArticleData.setThumb(thumb);
                }

                // 处理正文,摘要
                String content = originNewsData.getContent();
                String description = originNewsData.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String imgUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {

                                imgUrl = CrawlerManager.DownLoad(imgUrl, fileName + "\\news", refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getDid());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("href");
//                            String attachUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("?")) {
                                    continue;
                                }
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("weixin")) {
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, fileName + "\\news", refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getDid());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String videoUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, fileName + "\\news", refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getDid());
                    }
                    content = document.body().children().toString();
                } else {
                    // 是链接的话,content为链接
                    content = linkUrl;
                    description = originNewsData.getDescription();
                }
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                //TODO 处理来源
                stationMArticleData.setComefrom(originNewsData.getComefrom());
                //TODO 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                //TODO 处理发布时间
                stationMArticleData.setCreateTime(Double.valueOf(originNew.getPublishTime() + "000"));
                stationMArticleData.setUpdateTime(Double.valueOf(originNew.getPublishTime() + "000"));
                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }
        return chunkResult;
    }


    //医师职称
    private static HashMap<Integer, String> phpDocPosition = new HashMap<>();
    private static HashMap<String, Integer> java2DocPosition = new HashMap<>();

    //教务职称
    private static HashMap<Integer, String> phpEduPosition = new HashMap<>();
    private static HashMap<String, Integer> java2EduPosition = new HashMap<>();

    //教学岗位
    private static HashMap<Integer, String> phpEduPost = new HashMap<>();
    private static HashMap<String, Integer> java2EduPost = new HashMap<>();

    @Test
    public void moveDoctorJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        Map<Integer, Integer> map = new HashMap<>();

        //获得当前catID以及目标catID
//        getDoctorID(map);
//        map.put(3,4); //医生介绍.
        // 获得当前catID(doctor_php.sql)以及目标catID(doctor_java.sql)
        map.put(3, 10);      // 专家介绍 -> 专家介绍
        map.put(121, 24);    // 护理专家 -> 护理专家
        map.put(122, 25);    // 门诊排班 -> 门诊排班
        map.put(171, 74);    // 医生介绍 -> 医生介绍 (nfmnk)
        map.put(175, 78);    // 医生介绍 -> 医生介绍 (xxgnk)
        map.put(179, 82);    // 医生介绍 -> 医生介绍 (xhnk)
        map.put(183, 86);    // 医生介绍 -> 医生介绍 (sznk)
        map.put(187, 90);    // 医生介绍 -> 医生介绍 (xynk)
        map.put(191, 94);    // 医生介绍 -> 医生介绍 (hxywzzyxk)
        map.put(195, 98);    // 医生介绍 -> 医生介绍 (grk)
        map.put(199, 102);   // 医生介绍 -> 医生介绍 (sjnk)
        map.put(203, 106);   // 医生介绍 -> 医生介绍 (pfk)
        map.put(207, 110);   // 医生介绍 -> 医生介绍 (zxyjhk)
        map.put(211, 114);   // 医生介绍 -> 医生介绍 (jsk)
        map.put(215, 118);   // 医生介绍 -> 医生介绍 (zlk)
        map.put(219, 122);   // 医生介绍 -> 医生介绍 (lnbk)
        map.put(223, 126);   // 医生介绍 -> 医生介绍 (qkyxk)
        map.put(227, 130);   // 医生介绍 -> 医生介绍 (kfyxk)
        map.put(231, 134);   // 医生介绍 -> 医生介绍 (jtbck)
        map.put(235, 138);   // 专家介绍 -> 专家介绍 (jkglzx)
        map.put(239, 142);   // 医生介绍 -> 医生介绍 (xgwk)
        map.put(243, 146);   // 医生介绍 -> 医生介绍 (wcwk)
        map.put(247, 150);   // 医生介绍 -> 医生介绍 (gdwk)
        map.put(251, 154);   // 医生介绍 -> 医生介绍 (rxjzxwk)
        map.put(255, 158);   // 医生介绍 -> 医生介绍 (mzk)
        map.put(259, 162);   // 医生介绍 -> 医生介绍 (xwk)
        map.put(263, 166);   // 医生介绍 -> 医生介绍 (sjwk)
        map.put(267, 170);   // 医生介绍 -> 医生介绍 (gk)
        map.put(271, 174);   // 医生介绍 -> 医生介绍 (ssk)
        map.put(275, 178);   // 医生介绍 -> 医生介绍 (mnwk)
        map.put(279, 182);   // 医生介绍 -> 医生介绍 (ck)
        map.put(283, 186);   // 医生介绍 -> 医生介绍 (szyxzx)
        map.put(287, 190);   // 医生介绍 -> 医生介绍 (yk)
        map.put(291, 194);   // 医生介绍 -> 医生介绍 (ebyhk)
        map.put(295, 198);   // 医生介绍 -> 医生介绍 (hmwk)
        map.put(299, 202);   // 医生介绍 -> 医生介绍 (kqk)
        map.put(303, 206);   // 医生介绍 -> 医生介绍 (jzyxk)
        map.put(307, 210);   // 医生介绍 -> 医生介绍 (zzyxk)
        map.put(311, 214);   // 医生介绍 -> 医生介绍 (lcyyk)
        map.put(315, 218);   // 医生介绍 -> 医生介绍 (blk)
        map.put(319, 222);   // 药师介绍 -> 药师介绍 (yxb)
        map.put(323, 226);   // 专家介绍 -> 专家介绍 (yxjyk)
        map.put(327, 230);   // 医生介绍 -> 医生介绍 (fsk)
        map.put(331, 234);   // 医生介绍 -> 医生介绍 (csk)
        map.put(335, 238);   // 医生介绍 -> 医生介绍 (sxk)
        map.put(339, 242);   // 医生介绍 -> 医生介绍 (hyxk)
        map.put(469, 364);   // 医生介绍 -> 医生介绍 (fk)
        map.put(473, 368);   // 医生介绍 -> 医生介绍 (hlb)
        map.put(969, 845);   // 护理专家 -> 护理专家
        map.put(986, 861);   // 专家介绍 -> 专家介绍 (lcfzyxjczx)
        map.put(1077, 930);  // 医生介绍 -> 医生介绍 (xzdxgwk_ICU)
        map.put(1088, 941);  // 医生介绍 -> 医生介绍 (ttk)
        map.put(1099, 952);  // 医生介绍 -> 医生介绍 (fsmyk)
        map.put(1304, 1119); // 国家级人才 -> 国家级人才


        //获得列表数据
        getPositionList();

        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveDoctorJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveDoctorJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i] + " ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getDoctorID(Map<Integer, Integer> map) {
        // 找到所有model为医生的的catId及其DepartName
        LambdaQueryWrapper<HmsCategory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(HmsCategory::getModelId, 2)
                .eq(HmsCategory::getName, "科室医生");
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper1);
        Integer[] origin = new Integer[hmsCategories.size()];

        String[] DepartName = new String[origin.length];
        for (int i = 0; i < hmsCategories.size(); i++) {
            origin[i] = Math.toIntExact(hmsCategories.get(i).getId());

            LambdaQueryWrapper<HmsCategory> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(HmsCategory::getId, hmsCategories.get(i).getPid());
            DepartName[i] = hmsCategoryMapper.selectOne(wrapper2).getName();
        }

        //根据DepartName一致找到targetID
        DynamicDataSource.changeBuildDynamicDataSource();
        for (int i = 0; i < origin.length; i++) {
            LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationCategory2::getName, DepartName[i]);
            System.out.println(stationCategoryMapper2.selectList(wrapper));
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectList(wrapper).get(0);
            if (stationCategory2 == null)
                continue;

            LambdaQueryWrapper<StationCategory2> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(StationCategory2::getPid, stationCategory2.getId())
                    .eq(StationCategory2::getName, "科室医生");
            StationCategory2 stationCategory = stationCategoryMapper2.selectOne(wrapper3);
            if (stationCategory == null)
                continue;
            map.put(origin[i], stationCategory.getId());
        }
        System.out.println(map);
    }

    public void getPositionList() {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        //获得phpDoc医师职称列表
        LambdaQueryWrapper<HmsModelField> wrapperDoc = new LambdaQueryWrapper<>();
        wrapperDoc.eq(HmsModelField::getModelId, 2)
                .eq(HmsModelField::getField, "doc_position");
        HmsModelField PhpDoc = hmsModelFieldMapper.selectOne(wrapperDoc);
        String setting = PhpDoc.getSetting();
        Map mapSetting = JsonUtil.Json2Obj(setting, Map.class);
        List<Map<Object, Object>> options = (List<Map<Object, Object>>) mapSetting.get("options");
        System.out.println(options.get(0).get("value") + " -----  " + options.get(0).get("key"));
        System.out.println(JsonUtil.obj2String(options));

        options.forEach(map1 -> phpDocPosition.put(Integer.valueOf(map1.get("key").toString()), map1.get("value").toString()));

        //获得phpDoc教务职称列表
        LambdaQueryWrapper<HmsModelField> wrapperEdu = new LambdaQueryWrapper<>();
        wrapperEdu.eq(HmsModelField::getModelId, 2)
                .eq(HmsModelField::getField, "edu_position");
        HmsModelField PhpEdu = hmsModelFieldMapper.selectOne(wrapperEdu);
        String setting1 = PhpEdu.getSetting();
        Map map1 = JsonUtil.Json2Obj(setting1, Map.class);
        List<Map<Object, Object>> options2 = (List<Map<Object, Object>>) map1.get("options");
        options2.forEach(map3 -> phpEduPosition.put(Integer.valueOf(map3.get("key").toString()), map3.get("value").toString()));

        //获得phpDoc教学岗位列表
        LambdaQueryWrapper<HmsModelField> wrapperEduPost = new LambdaQueryWrapper<>();
        wrapperEduPost.eq(HmsModelField::getModelId, 2)
                .eq(HmsModelField::getField, "edu_post");
        HmsModelField PhpEduPost = hmsModelFieldMapper.selectOne(wrapperEduPost);
        String setting2 = PhpEduPost.getSetting();
        Map map2 = JsonUtil.Json2Obj(setting2, Map.class);
        List<Map<Object, Object>> options3 = (List<Map<Object, Object>>) map2.get("options");
        options3.forEach(map3 -> phpEduPost.put(Integer.valueOf(map3.get("key").toString()), map3.get("value").toString()));

//        --------------------------------------------------------------------------------------------------
        //获得java2.0库数据列表
        DynamicDataSource.changeResourceDynamicDataSource();
        //获得javaDoc医师职称列表
        LambdaQueryWrapper<StationModelField2> wrapperDoc2 = new LambdaQueryWrapper<>();
        wrapperDoc2.eq(StationModelField2::getModelId, 19)
                .eq(StationModelField2::getField, "doc_position");
        StationModelField2 PhpDoc2 = stationModelFieldMapper2.selectOne(wrapperDoc2);
        String javaSetting = PhpDoc2.getSetting();
        Map JavaSetting = JsonUtil.Json2Obj(javaSetting, Map.class);
        List<Map<Object, Object>> javaOptions = (List<Map<Object, Object>>) JavaSetting.get("options");
        javaOptions.forEach(map3 -> java2DocPosition.put(map3.get("label").toString(), Integer.valueOf(map3.get("value").toString())));

        //获得javaDoc教务职称列表
        LambdaQueryWrapper<StationModelField2> wrapperEdu2 = new LambdaQueryWrapper<>();
        wrapperEdu2.eq(StationModelField2::getModelId, 19)
                .eq(StationModelField2::getField, "edu_post");
        StationModelField2 PhpEdu2 = stationModelFieldMapper2.selectOne(wrapperEdu2);
        String javaSetting1 = PhpEdu2.getSetting();
        Map JavaMap1 = JsonUtil.Json2Obj(javaSetting1, Map.class);
        List<Map<Object, Object>> javaOptions2 = (List<Map<Object, Object>>) JavaMap1.get("options");
        javaOptions2.forEach(map3 -> java2EduPosition.put(map3.get("label").toString(), Integer.valueOf(map3.get("value").toString())));

        //获得javaDoc教学岗位列表
        LambdaQueryWrapper<StationModelField2> wrapperEduPost2 = new LambdaQueryWrapper<>();
        wrapperEduPost2.eq(StationModelField2::getModelId, 19)
                .eq(StationModelField2::getField, "edu_post");
        StationModelField2 PhpEduPost2 = stationModelFieldMapper2.selectOne(wrapperEduPost2);
        String javaSetting2 = PhpEduPost2.getSetting();
        Map javaMap2 = JsonUtil.Json2Obj(javaSetting2, Map.class);
        List<Map<Object, Object>> javaOptions3 = (List<Map<Object, Object>>) javaMap2.get("options");
        javaOptions3.forEach(map3 -> java2EduPost.put(map3.get("label").toString(), Integer.valueOf(map3.get("value").toString())));
    }


    public void moveDoctorJavaMethod(Integer originCatId, Integer targetCatId) {

        DynamicDataSource.changeNewPhpDynamicDataSource();

        LambdaQueryWrapper<HmsCMans> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(HmsCMans::getCatid, originCatId)
                .eq(HmsCMans::getState, 0)
                .eq(HmsCMans::getStatus, 99)
                .orderByAsc(HmsCMans::getListorder)
                .orderByDesc(HmsCMans::getSort);
        List<HmsCMans> originMans = hmsCMansMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 6;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMDoctorData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCMans>> taskChunks = divideTasks(originMans, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMDoctorData2> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } catch (Exception e) {
                    System.err.println("解析错误！" + e.getMessage());
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMDoctorData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMDoctorData2> map : resultMaps) {
            mergedMap.putAll(map);
        }


        // 处理合并后的结果
        for (StationMReference targetMan : mergedMap.keySet()) {
            StationMDoctorData2 targetManData = mergedMap.get(targetMan);

            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetMan);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId, targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMDoctorDataMapper.insert(targetManData);
            //落库 -> folder_resource
            FolderResource folderResource = FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetManData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetManData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetMan.setDataId(Long.valueOf(folderResource.getId()));
            targetMan.setSortLevel(Math.toIntExact(targetMan.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetMan);
        }
    }

    private Map<StationMReference, StationMDoctorData2> processTaskChunk2(
            List<HmsCMans> chunk, Integer targetCatId) {


        DynamicDataSource.changeNewPhpDynamicDataSource();

        Map<StationMReference, StationMDoctorData2> chunkResult = new LinkedHashMap<>();

        List<HmsCategory> HmsCategories = hmsCategoryMapper.selectList(null);
        Map<Integer, String> catMap = new HashMap<>();
        for (HmsCategory hmsCategory : HmsCategories) {
            catMap.put(hmsCategory.getId().intValue(), hmsCategory.getName());
        }

        for (HmsCMans originMan : chunk) {
            DynamicDataSource.changeNewPhpDynamicDataSource();
            // 新的doctor
            StationMReference stationMDoctor = new StationMReference();
            stationMDoctor.setCatId(targetCatId);
            stationMDoctor.setPublishTime(Double.valueOf(originMan.getPublishTime() + "000"));
            stationMDoctor.setState(99);
            stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMDoctor.setIsTop(0);
//            stationMDoctor.setIsLock(0);

            // 找到修改前的manData并修改后存入新的doctorData
            LambdaQueryWrapper<HmsCMansData> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(HmsCMansData::getDid, originMan.getDataid());
            System.out.println(originMan);
            HmsCMansData originManData = hmsCMansDataMapper.selectOne(wrapper1);
            if (originManData == null)
                System.out.println("====================不存在===================");

            // 新的articleData
            StationMDoctorData2 stationMDoctorData = new StationMDoctorData2();
            stationMDoctorData.setUuid(UUID.randomUUID().toString());
            stationMDoctorData.setTitle(originManData.getTitle());
            stationMDoctorData.setIgnoreReason(null);
            stationMDoctorData.setCreateTime((double) System.currentTimeMillis());
            stationMDoctorData.setUpdateTime((double) System.currentTimeMillis());
            stationMDoctorData.setCreateUserId(1);
            stationMDoctorData.setUpdateUserId(1);
            stationMDoctorData.setState(2);

            // 缩略图处理 （只有一张图片）
            String thumb = originManData.getThumb();
            // 调用接口
            if (!thumb.isBlank()) {
                thumb = CrawlerManager.DownLoad(thumb, fileName + "\\doctor", refer);
                thumb = CrawlerManager.changeFileUrl(new File(thumb));
            }
            stationMDoctorData.setThumb(thumb);

            // 处理正文,摘要
            String content = originManData.getContent();
            content = changeSpecialChat(content);
            Document document = Jsoup.parse(content);
            Elements imgList = document.select("img");
            Elements linkList = document.select("a");
            Elements videoList = document.select("video");
            // 图片路径集合处理
            try {
                for (Element element : imgList) {
                    String imgUrl = element.attr("src");
                    if (StringUtils.isNotBlank(imgUrl)) {
                        imgUrl = CrawlerManager.DownLoad(imgUrl, fileName + "\\doctor", refer);
                        if (StringUtils.isNotBlank(imgUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("图片下载失败,是资源id为:" + originManData.getDid());
            }

            // 链接路径集合处理
            try {
                for (Element element : linkList) {
                    String attachUrl = element.attr("href");
                    if (StringUtils.isNotBlank(attachUrl)) {
                        attachUrl = CrawlerManager.DownLoad(attachUrl, fileName + "\\doctor", refer);
                        if (StringUtils.isNotBlank(attachUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                            element.attr("href", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("链接下载失败,是资源id为:" + originManData.getDid());
            }

            // 视频路径集合处理
            try {
                for (Element element : videoList) {
                    String videoUrl = element.attr("src");
                    if (StringUtils.isNotBlank(videoUrl)) {
                        videoUrl = CrawlerManager.DownLoad(videoUrl, fileName + "\\doctor", refer);
                        if (StringUtils.isNotBlank(videoUrl)) {
                            String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("视频下载失败,是资源id为:" + originManData.getDid());
            }
            content = document.toString();

            stationMDoctorData.setContent(content);

            //TODO  处理医师职称
            String protit = originManData.getDocPosition();
            if (protit != null && !protit.isEmpty()) {
                int code = Integer.parseInt(protit);
                stationMDoctorData.setDocPosition(java2DocPosition.get(phpDocPosition.get(code)));
            }
            //TODO 处理教务职称
            String eduPosition = originManData.getEduPosition();
            if (eduPosition != null && !eduPosition.isEmpty()) {
                int code = Integer.parseInt(eduPosition);
                stationMDoctorData.setEduPosition(java2EduPosition.get(phpEduPosition.get(code)));
            }
            //TODO 处理教务岗位
            String eduPost = originManData.getEduPost();
            if (eduPost != null && !eduPost.isEmpty()) {
                int code = Integer.parseInt(eduPost);
                stationMDoctorData.setEduPost(java2EduPost.get(phpEduPost.get(code)));
            }

            //TODO 处理擅长
            stationMDoctorData.setGoodat(originManData.getGoodat());
            // 处理科室
            //TODO 获取以前科室名称
            String depart = originManData.getDepart();
            if (!depart.isEmpty()) {
                String[] departs = depart.split(",");
                List<String> departNames = new ArrayList<>();
                List<Integer> newDepartIds = new ArrayList<>();
                for (int i = 0; i < departs.length; i++) {
                    departNames.add(catMap.get(Integer.parseInt(departs[i])));
                }
                //TODO 通过科室名称匹配现在科室id
                DynamicDataSource.changeBuildDynamicDataSource();
                LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(StationCategory::getName, departNames)
                        .eq(StationCategory::getType, 9);
                List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
                for (StationCategory stationCategory : stationCategories) {
                    newDepartIds.add(stationCategory.getId());
                }
                stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
            }

            //TODO 处理观看数
            stationMDoctor.setViews(originManData.getViews());
            //TODO  处理发布时间
            stationMDoctorData.setCreateTime(Double.valueOf(originMan.getPublishTime() + "000"));
            stationMDoctorData.setUpdateTime(Double.valueOf(originMan.getPublishTime() + "000"));

            chunkResult.put(stationMDoctor, stationMDoctorData);
        }

        return chunkResult;
    }


    @Test
    public void moveImageJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        Map<Integer, Integer> map = new HashMap<>();

        //获得当前catID以及目标catID
//        getImageID(map);
//        map.put(494, 385);
//        map.put(499, 390);
//        map.put(505, 396);
//        map.put(511, 402);
//        map.put(517, 408);
//        map.put(523, 414);
//        map.put(529, 420);
//        map.put(535, 426);
//        map.put(541, 432);
//        map.put(547, 438);
//        map.put(553, 444);
//        map.put(559, 450);
//        map.put(565, 456);
//        map.put(571, 462);
//        map.put(577, 468);
//        map.put(583, 474);
//        map.put(589, 480);
//        map.put(595, 486);
//        map.put(601, 492);
//        map.put(607, 498);
//        map.put(613, 504);
//        map.put(619, 510);
//        map.put(625, 516);
//        map.put(631, 522);
//        map.put(637, 528);
//        map.put(643, 534);
//        map.put(649, 540);
//        map.put(655, 546);
//        map.put(661, 552);
//        map.put(667, 558);
//        map.put(673, 564);
//        map.put(679, 570);
//        map.put(685, 576);
//        map.put(691, 582);
//        map.put(697, 588);
//        map.put(703, 594);
//        map.put(709, 600);
//        map.put(715, 606);
//        map.put(721, 612);
//        map.put(727, 618);
//        map.put(733, 624);
//        map.put(739, 630);
//        map.put(745, 636);
//        map.put(751, 642);
//        map.put(757, 648);
//        map.put(991, 866);
//        map.put(1082, 935);
//        map.put(1093, 946);
//        map.put(1104, 957);

        // 重医一院赋
//        map.put(762, 653);
//        map.put(838, 725);

        // 重医影音
//        map.put(841, 728);

        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveImageJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveDoctorJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i] + " ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getImageID(Map<Integer, Integer> map) {
        // 1. 切换到源数据源，一次性获取所有 modelId=6 的新闻分类 (逻辑不变)
        DynamicDataSource.changeNewPhpDynamicDataSource();
        LambdaQueryWrapper<HmsCategory> sourceWrapper = new LambdaQueryWrapper<>();
        sourceWrapper.eq(HmsCategory::getModelId, 6).isNull(HmsCategory::getDeletedAt);
        List<HmsCategory> sourceCategories = hmsCategoryMapper.selectList(sourceWrapper);

        if (sourceCategories == null || sourceCategories.isEmpty()) {
            log.info("没有需要迁移的源分类数据。");
            return;
        }

        // 2. 切换到目标数据源，一次性获取所有 modelId=41 的分类 (逻辑不变)
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationCategory2> targetWrapper = new LambdaQueryWrapper<>();
        targetWrapper.eq(StationCategory2::getModelId, 41);
        List<StationCategory2> targetCategories = stationCategoryMapper2.selectList(targetWrapper);

        if (targetCategories == null || targetCategories.isEmpty()) {
            log.info("没有可供映射的目标分类数据。");
            return;
        }

        // 【性能优化】将目标分类列表转换为Map，以分类名称为Key。
        // Value是List，因为可能存在同名的目标分类。
        // 这样查找的时间复杂度从 O(M) 降为 O(1)。
        Map<String, List<StationCategory2>> targetMapByName = targetCategories.stream()
                .filter(c -> c.getName() != null) // 过滤掉没有名称的目标分类
                .collect(Collectors.groupingBy(StationCategory2::getName));

        // 3. 在内存中高效地执行匹配逻辑
        for (HmsCategory sourceCategory : sourceCategories) {
            if (sourceCategory.getName() == null) {
                log.warn("源分类名称为空，已跳过。源ID: {}", sourceCategory.getId());
                continue;
            }

            // 【性能优化】通过名称直接从Map中获取候选列表，而不是遍历整个targetCategories
            List<StationCategory2> potentialMatches = targetMapByName.get(sourceCategory.getName());

            // 如果根据名称找不到任何候选，直接记录并跳过
            if (potentialMatches == null || potentialMatches.isEmpty()) {
                log.warn("根据名称未能找到任何候选目标分类。源ID: {}, 名称: {}", sourceCategory.getId(), sourceCategory.getName());
                continue;
            }

            Optional<StationCategory2> finalMatchOpt;
            String pString = sourceCategory.getPString();

            // 判断源是否为英文数据
            boolean isSourceEnglish = (pString != null && pString.contains("374"));

            if (isSourceEnglish) {
                // --- 规则 A: 源为英文数据 ---
                // 在重名候选项中，只寻找同样是英文站的目标 (site_id=3)
                finalMatchOpt = potentialMatches.stream()
                        .filter(t -> t.getSiteId() != null && t.getSiteId() == 3)
                        .findFirst(); // 按名称匹配即可，取第一个找到的英文目标

                if (finalMatchOpt.isPresent()) {
                    log.info("英文源匹配成功 (p_string含374): 源ID[{}], 名称[{}]",
                            sourceCategory.getId(), sourceCategory.getName());
                }
            } else {
                // --- 规则 B: 源为非英文数据 ---
                // 在重名候选项中，寻找非英文站的目标，并应用复杂的URL规则
                finalMatchOpt = potentialMatches.stream()
                        .filter(t -> t.getSiteId() != null && t.getSiteId() != 3)
                        .filter(t -> areNonEnglishSitesMatching(sourceCategory, t)) // 调用URL匹配引擎
                        .findFirst();

                if (finalMatchOpt.isPresent()) {
                    log.info("非英文源URL匹配成功: 源ID[{}], 名称[{}]",
                            sourceCategory.getId(), sourceCategory.getName());
                }
            }

            // 如果找到了最终的匹配项，则存入Map
            if (finalMatchOpt.isPresent()) {
                map.put(Math.toIntExact(sourceCategory.getId()), finalMatchOpt.get().getId());
            } else {
                log.warn("未能为源ID[{}]找到任何匹配项, 名称: '{}', p_string: '{}'",
                        sourceCategory.getId(), sourceCategory.getName(), pString);
            }
        }
    }


    public void moveImageJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeNewPhpDynamicDataSource();
//        Integer originCatId=116;
//        Integer targetCatId=23;
        LambdaQueryWrapper<HmsCImage> wrapper = new LambdaQueryWrapper<>();
        //TODO 找到所有存在的图集消息
        wrapper.eq(HmsCImage::getCatid, originCatId)
                .eq(HmsCImage::getState, 0)
                .eq(HmsCImage::getStatus, 99)
                .orderByAsc(HmsCImage::getListorder);
        List<HmsCImage> originImages = hmsCImageMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMImageData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCImage>> taskChunks = divideTasks(originImages, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMImageData2> chunkResult =
                            processTaskChunk3(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMImageData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMImageData2> map : resultMaps) {
            mergedMap.putAll(map);
        }


        // 处理合并后的结果
        for (StationMReference targetImage : mergedMap.keySet()) {
            StationMImageData2 targetImageData = mergedMap.get(targetImage);

            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetImage);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId, targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMImageDataMapper.insert(targetImageData);
            //落库 -> folder_resource
            FolderResource folderResource = FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetImageData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetImageData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetImage.setDataId(Long.valueOf(folderResource.getId()));
            targetImage.setSortLevel(Math.toIntExact(targetImage.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetImage);
        }
    }

    private Map<StationMReference, StationMImageData2> processTaskChunk3(
            List<HmsCImage> chunk, Integer targetCatId) {
        Map<StationMReference, StationMImageData2> chunkResult = new LinkedHashMap<>();

        try {
            for (HmsCImage originImage : chunk) {
                //TODO 新的image
                StationMReference stationMImage = new StationMReference();
                stationMImage.setCatId(targetCatId);
                stationMImage.setPublishTime(Double.valueOf(originImage.getPublishTime() + "000"));
                stationMImage.setState(99);
                stationMImage.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMImage.setIsTop(0);

                //TODO 找到修改前的imageData并修改存入新的imageData
                DynamicDataSource.changeNewPhpDynamicDataSource();
                LambdaQueryWrapper<HmsCImageData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCImageData::getDid, originImage.getDataid());
                HmsCImageData originImageData = hmsCImageDataMapper.selectOne(wrapper1);

                //TODO 新的imageData
                StationMImageData2 stationMImageData = new StationMImageData2();
                stationMImageData.setUuid(UUID.randomUUID().toString());
                stationMImageData.setTitle(originImageData.getTitle());
                stationMImageData.setCreateTime(Double.valueOf(originImage.getPublishTime() + "000"));
                stationMImageData.setUpdateTime(Double.valueOf(originImage.getPublishTime() + "000"));
                stationMImageData.setCreateUserId(1);
                stationMImageData.setUpdateUserId(1);
                stationMImageData.setState(2);

                //TODO 处理访问量
                stationMImage.setViews(originImageData.getViews());
                //TODO 处理作者
                String author = originImageData.getAuthor();
                author = changeSpecialChat(author);
                if (author != null && !author.equals("[]") && !author.isEmpty()) {
                    stationMImageData.setAuthor(author);
                }
                //TODO 处理摄影师
                String photographer = originImageData.getPhotographer();
                if (photographer != null && !photographer.equals("[]") && !photographer.isEmpty()) {
                    photographer = changeSpecialChat(photographer);
                    stationMImageData.setPhotographer(photographer);
                }
                //TODO 处理来自
                String comeFrom = originImageData.getComefrom();
                if (comeFrom != null && !comeFrom.isEmpty()) {
                    comeFrom = changeSpecialChat(comeFrom);
                    stationMImageData.setComefrom(comeFrom);
                }
                //TODO 处理images
                String images = originImageData.getImages();
                JSONArray jsonArray = JSONArray.parseArray(images);
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    //TODO 单图片的图集
                    if (jsonArray.size() == 1) {
                        JSONObject item = jsonArray.getJSONObject(0);
                        //TODO 设置图集时间戳
                        long currentTimeMillis = System.currentTimeMillis();
                        item.put("id", currentTimeMillis);
                        //TODO 获取url图片进行替换
                        String url = item.getString("url");
                        if (url != null && !url.isBlank()) {
                            url = CrawlerManager.DownLoad(url, fileName, refer);
                            url = CrawlerManager.changeFileUrl(new File(url));
                        }
                        item.put("url", url);
                        //TODO 设置图片标题
                        item.put("title", originImageData.getTitle() + ".jpg");
                        //TODO 获取description并解码Unicode转义字符
                        String description = item.getString("description");
                        if (description != null && !description.isEmpty()) {
                            String decodedDescription = decodeUnicode(description);
                            decodedDescription = StringUtils.abbreviate(decodedDescription, 11);
                            item.put("description", decodedDescription);
                        }
                    }
                    JSONObject dbItem = jsonArray.getJSONObject(0);
                    JSONArray jsonArray1 = new JSONArray();
                    jsonArray1.add(dbItem);
                    stationMImageData.setImages(jsonArray1.toJSONString());
                }
                //TODO 处理发布时间
//                stationMImageData.setPublishTime(Double.valueOf(originImage.getPublishTime()+"000"));
                chunkResult.put(stationMImage, stationMImageData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }

        return chunkResult;
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }

    /**
     * 解码 Unicode 转义字符，如 \u5e74 -> 年
     */
    public static String decodeUnicode(String input) {
        int length = input.length();
        StringBuilder out = new StringBuilder();

        for (int i = 0; i < length; i++) {
            char c = input.charAt(i);
            if (c == '\\' && i + 5 < length && input.charAt(i + 1) == 'u') {
                String hex = input.substring(i + 2, i + 6);
                try {
                    out.append((char) Integer.parseInt(hex, 16));
                    i += 5;
                } catch (NumberFormatException e) {
                    out.append(c);
                }
            } else {
                out.append(c);
            }
        }

        return out.toString();
    }

}
