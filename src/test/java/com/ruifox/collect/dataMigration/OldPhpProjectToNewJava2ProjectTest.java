package com.ruifox.collect.dataMigration;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.dao.mapper.tbl.*;
import com.ruifox.collect.dao.mapper.station.StationCategoryMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.module.entity.tbl.*;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station.StationCategory;
import com.ruifox.collect.module.entity.station2.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class OldPhpProjectToNewJava2ProjectTest {
    @Autowired
    private TblCNewsMapper tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapper tblCNewsDataMapper;
    @Autowired
    private TblCMan2Mapper tblCManMapper;
    @Autowired
    private TblCManDataHxeyMapper tblCManDataMapper;

    @Autowired
    private HmsCImageMapper hmsCImageMapper;
    @Autowired
    private HmsCImageDataMapper hmsCImageDataMapper;

    @Autowired
    private StationMImageDataMapper2 stationMImageDataMapper;
    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;
    @Autowired
    private TblCategoryMapper tblCategoryMapper;

    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private StationMDoctorDataMapper2 stationMDoctorDataMapper;
    @Autowired
    private StationCategoryMapper stationCategoryMapper;

    @Autowired
    private StationCategoryMapper2 stationCategoryMapper2;

    @Autowired
    private HmsModelFieldMapper hmsModelFieldMapper;
    @Autowired
    private StationModelFieldMapper2 stationModelFieldMapper2;

    //广西横州市医院
    private String fileName = "C:\\Collect_Data\\gxhzsrmyy";
    private String refer = "http://www.gxhzsrmyy.cn/";


    //江苏学会
//    private String fileName = "C:\\Collect_Data\\jrha3";
//    private String refer = "http://jrha.lan24.foxtest.net/";

    @Test
    public void moveNewsJava() {
        DynamicDataSource.changeOldPhpDynamicDataSource();

        Map<Integer, Integer> map = new HashMap<>();

        //getNewsID(map);
// Based on matching news_old_category.url to news_new_category.uri
        map.put(4, 6);
        map.put(118, 16);
        map.put(119, 17);


        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i]+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getNewsID(Map<Integer, Integer> map){
        DynamicDataSource.changeOldPhpDynamicDataSource();
        // 找到所有model为新闻的catId及其CategoryName
        LambdaQueryWrapper<TblCategory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TblCategory::getModelid,2);  // tbl库中新闻模型的modelId是2
        List<TblCategory> tblCategories = tblCategoryMapper.selectList(wrapper1);
        Integer[] origin = new Integer[tblCategories.size()];
        String[] CategoryName = new String[origin.length];
        for (int i = 0; i < tblCategories.size(); i++) {
            origin[i]= tblCategories.get(i).getCatid();
            CategoryName[i]=tblCategories.get(i).getCatname();
        }

        //根据CategoryName一致找到targetID
        DynamicDataSource.changeBuildDynamicDataSource();
        for (int i = 0; i < origin.length; i++) {
            LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationCategory2::getName,CategoryName[i]);
            wrapper.eq(StationCategory2::getModelId,20);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper);
            if (stationCategory2==null)
                continue;
            map.put(origin[i],stationCategory2.getId());
        }
    }

    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCNews> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息与判断是否为发布状态
        wrapper.eq(TblCNews::getCatid, originCatId)
                .eq(TblCNews::getState, 0)
                .eq(TblCNews::getStatus, "99")
//                .gt(TblCNews::getInputtime,1731600000)    // TODO 临时需要
                .orderByAsc(TblCNews::getListorder);

        List<TblCNews> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCNews>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMArticleData2> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMArticleData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMArticleData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMArticleData2 targetNewData = mergedMap.get(targetNew);

            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetNew);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMArticleDataMapper.insert(targetNewData);
            //落库 -> folder_resource
            FolderResource folderResource= new FolderResource().builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetNewData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetNewData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetNew.setDataId(Long.valueOf(folderResource.getId()));
            targetNew.setSortLevel(Math.toIntExact(targetNew.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetNew);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start = end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMReference, StationMArticleData2> processTaskChunk(
            List<TblCNews> chunk, Integer targetCatId) {
        Map<StationMReference, StationMArticleData2> chunkResult = new LinkedHashMap<>();
        try {
            for (TblCNews originNew : chunk) {
                // 新的news
                StationMReference stationMArticle = new StationMReference();
                stationMArticle.setCatId(targetCatId);
                stationMArticle.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));
                stationMArticle.setState(99);
                stationMArticle.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMArticle.setIsTop(0);

                String linkUrl = originNew.getUrl();
                int isLink = (originNew.getIslink() != null && originNew.getIslink() > 0) ? 1 : 0;

                DynamicDataSource.changeOldPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblCNewsData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCNewsData::getId, originNew.getId());
                TblCNewsData originNewsData = tblCNewsDataMapper.selectOne(wrapper1);

                //处理访问量
                stationMArticle.setViews(originNew.getViews());
                // 新的articleData
                StationMArticleData2 stationMArticleData = new StationMArticleData2();
                stationMArticleData.setUuid(UUID.randomUUID().toString());
//                stationMArticleData.setTitle(originNew.getTitle());
                stationMArticleData.setIgnoreReason(null);
                stationMArticleData.setPhotographer(null);
                stationMArticleData.setCreateUserId(1);
                stationMArticleData.setUpdateUserId(1);
                stationMArticleData.setState(2);
                // 处理作者
                String author = (originNewsData != null) ? originNewsData.getAuthor() : "";
                if (author != null) {
                    author = changeSpecialChat(author);
                    if (!author.equals("0") && !author.isEmpty() && !author.equals("[]"))
                        stationMArticleData.setAuthor(author);
                }
                // 缩略图处理 （只有一张图片）
                // TODO 临时特殊处理
                String oss = originNewsData.getThumb();
                if (oss != null && !oss.isBlank()){
                    // TODO 临时特殊处理
//                    String thumb = oss.replace( oss.substring(0, oss.indexOf("oss")),"https://www.jrha.net.cn/");
//                    System.out.println(thumb);
                    String thumb = originNewsData.getThumb();
                    // 调用接口
                    if (!thumb.isBlank()) {
                        thumb = CrawlerManager.DownLoad(thumb, fileName+"\\news" ,refer);
                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    }
                    stationMArticleData.setThumb(thumb);
                }

                // 处理正文,摘要
                String content = (originNewsData != null) ? originNewsData.getContent() : "";
                String description = originNewsData.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String imgUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {

                                imgUrl = CrawlerManager.DownLoad(imgUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + ((originNewsData != null) ? originNewsData.getId() : originNew.getId()));
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("href");
//                            String attachUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("?")) {
                                    continue;
                                }
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("weixin")){
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + ((originNewsData != null) ? originNewsData.getId() : originNew.getId()));
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String videoUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + ((originNewsData != null) ? originNewsData.getId() : originNew.getId()));
                    }
                    content = document.body().children().toString();
                } else {
                    // 是链接的话,content为链接
                    content = linkUrl;
                    description = linkUrl;
                }
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                //TODO 处理来源
                stationMArticleData.setComefrom(originNewsData.getComefrom());
                //TODO 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                //TODO 处理发布时间
                stationMArticleData.setCreateTime(Double.valueOf(originNew.getInputtime() + "000"));
                stationMArticleData.setUpdateTime(Double.valueOf(originNew.getUpdatetime() + "000"));
                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }
        return chunkResult;
    }


    //医师职称
    private static HashMap<Integer,String> phpDocPosition = new HashMap<>();
    private static HashMap<String,Integer> java2DocPosition = new HashMap<>();

    //教务职称
    private static HashMap<Integer,String> phpEduPosition = new HashMap<>();
    private static HashMap<String,Integer> java2EduPosition = new HashMap<>();

    //教学岗位
    private static HashMap<Integer,String> phpEduPost = new HashMap<>();
    private static HashMap<String,Integer> java2EduPost = new HashMap<>();

    @Test
    public void moveDoctorJava() {
        DynamicDataSource.changeOldPhpDynamicDataSource();
        Map<Integer, Integer> map = new HashMap<>();

        //获得当前catID以及目标catID
//        getDoctorID(map);
        map.put(3, 5);
        map.put(117, 15);
        map.put(181, 79);
        map.put(185, 83);


        //获得列表数据
        getPositionList();

        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveDoctorJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveDoctorJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i]+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getDoctorID(Map<Integer, Integer> map){
        // 找到所有model为医生的的catId及其DepartName
        LambdaQueryWrapper<HmsCategory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(HmsCategory::getModelId,2)
                .eq(HmsCategory::getName,"科室医生");
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper1);
        Integer[] origin = new Integer[hmsCategories.size()];

        String[] DepartName = new String[origin.length];
        for (int i = 0; i < hmsCategories.size(); i++) {
            origin[i]= Math.toIntExact(hmsCategories.get(i).getId());

            LambdaQueryWrapper<HmsCategory> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(HmsCategory::getId,hmsCategories.get(i).getPid());
            DepartName[i]=hmsCategoryMapper.selectOne(wrapper2).getName();
        }

        //根据DepartName一致找到targetID
        DynamicDataSource.changeBuildDynamicDataSource();
        for (int i = 0; i < origin.length; i++) {
            LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationCategory2::getName,DepartName[i]);
            System.out.println(stationCategoryMapper2.selectList(wrapper));
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectList(wrapper).get(0);
            if (stationCategory2==null)
                continue;

            LambdaQueryWrapper<StationCategory2> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(StationCategory2::getPid,stationCategory2.getId())
                    .eq(StationCategory2::getName,"科室医生");
            StationCategory2 stationCategory = stationCategoryMapper2.selectOne(wrapper3);
            if (stationCategory==null)
                continue;
            map.put(origin[i],stationCategory.getId());
        }
        System.out.println(map);
    }

    public void getPositionList(){
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCMan2> wrapper = new LambdaQueryWrapper<>();
        List<TblCMan2> doctors = tblCManMapper.selectList(wrapper);
        Set<String> docPositions = new HashSet<>();
        Set<String> eduPositions = new HashSet<>();
        Set<String> eduPosts = new HashSet<>();
        for (TblCMan2 doctor : doctors) {
            String desc = doctor.getDescription();
            if (desc != null) {
                String docPosition = extractByRegex(desc, "医生职称:(.*?)；");
                String eduPosition = extractByRegex(desc, "教务职称:(.*?)；");
                String eduPost = extractByRegex(desc, "教务岗位:(.*?)；");
                if (docPosition != null && !docPosition.isEmpty()) docPositions.add(docPosition);
                if (eduPosition != null && !eduPosition.isEmpty()) eduPositions.add(eduPosition);
                if (eduPost != null && !eduPost.isEmpty()) eduPosts.add(eduPost);
            }
        }
        System.out.println("医生职称唯一值: " + docPositions);
        System.out.println("教务职称唯一值: " + eduPositions);
        System.out.println("教务岗位唯一值: " + eduPosts);
    }



    public void moveDoctorJavaMethod(Integer originCatId,Integer targetCatId) {

        DynamicDataSource.changeOldPhpDynamicDataSource();

        LambdaQueryWrapper<TblCMan2> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCMan2::getCatid, originCatId)
                .eq(TblCMan2::getState, 0)
                .eq(TblCMan2::getStatus, 99)
                .orderByAsc(TblCMan2::getListorder)
                .orderByDesc(TblCMan2::getSort);
        List<TblCMan2> originMans = tblCManMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 6;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMDoctorData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCMan2>> taskChunks = divideTasks(originMans, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMDoctorData2> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                }catch (Exception e){
                    System.err.println("解析错误！"+e.getMessage());
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMDoctorData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMDoctorData2> map : resultMaps) {
            mergedMap.putAll(map);
        }


        // 处理合并后的结果
        for (StationMReference targetMan : mergedMap.keySet()) {
            StationMDoctorData2 targetManData = mergedMap.get(targetMan);

            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetMan);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMDoctorDataMapper.insert(targetManData);
            //落库 -> folder_resource
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetManData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetManData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetMan.setDataId(Long.valueOf(folderResource.getId()));
            targetMan.setSortLevel(Math.toIntExact(targetMan.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetMan);
        }
    }

    private Map<StationMReference, StationMDoctorData2> processTaskChunk2(
            List<TblCMan2> chunk, Integer targetCatId) {


        DynamicDataSource.changeOldPhpDynamicDataSource();

        Map<StationMReference, StationMDoctorData2> chunkResult = new LinkedHashMap<>();

        List<TblCategory> tblCategories = tblCategoryMapper.selectList(null);
        Map<Integer, String> catMap = new HashMap<>();
        for (TblCategory tblCategory : tblCategories ) {
            catMap.put(tblCategory.getCatid(), tblCategory.getCatname());
        }

        for (TblCMan2 originMan : chunk) {
            DynamicDataSource.changeOldPhpDynamicDataSource();
            // 新的doctor
            StationMReference stationMDoctor = new StationMReference();
            stationMDoctor.setCatId(targetCatId);
            stationMDoctor.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));
            stationMDoctor.setState(99);
            stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMDoctor.setIsTop(0);
//            stationMDoctor.setIsLock(0);

            // 找到修改前的manData并修改后存入新的doctorData
            LambdaQueryWrapper<TblCManDataHxey> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TblCManDataHxey::getId, originMan.getId());
            System.out.println(originMan);
            TblCManDataHxey originManData = tblCManDataMapper.selectOne(wrapper1);
            if (originManData==null)
                System.out.println("====================不存在===================");

            // 新的articleData
            StationMDoctorData2 stationMDoctorData = new StationMDoctorData2();
            stationMDoctorData.setUuid(UUID.randomUUID().toString());
            stationMDoctorData.setTitle(originMan.getTitle());
            stationMDoctorData.setIgnoreReason(null);
            stationMDoctorData.setCreateTime((double) System.currentTimeMillis());
            stationMDoctorData.setUpdateTime((double) System.currentTimeMillis());
            stationMDoctorData.setCreateUserId(1);
            stationMDoctorData.setUpdateUserId(1);
            stationMDoctorData.setState(2);

            // 缩略图处理 （只有一张图片）
            String thumb = originMan.getThumb();
            // 调用接口
            if (!thumb.isBlank()) {
                thumb = CrawlerManager.DownLoad(thumb, fileName+"\\doctor" ,refer);
                thumb = CrawlerManager.changeFileUrl(new File(thumb));
            }
            stationMDoctorData.setThumb(thumb);

            // 处理正文,摘要
            String content = (originManData != null) ? originManData.getContent() : "";
            content = changeSpecialChat(content);
            Document document = Jsoup.parse(content);
            Elements imgList = document.select("img");
            Elements linkList = document.select("a");
            Elements videoList = document.select("video");
            // 图片路径集合处理
            try {
                for (Element element : imgList) {
                    String imgUrl = element.attr("src");
                    if (StringUtils.isNotBlank(imgUrl)) {
                        imgUrl = CrawlerManager.DownLoad(imgUrl, fileName+"\\doctor" ,refer);
                        if (StringUtils.isNotBlank(imgUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("图片下载失败,是资源id为:" + originMan.getId());
            }

            // 链接路径集合处理
            try {
                for (Element element : linkList) {
                    String attachUrl = element.attr("href");
                    if (StringUtils.isNotBlank(attachUrl)) {
                        attachUrl = CrawlerManager.DownLoad(attachUrl, fileName+"\\doctor" ,refer);
                        if (StringUtils.isNotBlank(attachUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                            element.attr("href", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("链接下载失败,是资源id为:" + originMan.getId());
            }

            // 视频路径集合处理
            try {
                for (Element element : videoList) {
                    String videoUrl = element.attr("src");
                    if (StringUtils.isNotBlank(videoUrl)) {
                        videoUrl = CrawlerManager.DownLoad(videoUrl, fileName+"\\doctor" ,refer);
                        if (StringUtils.isNotBlank(videoUrl)) {
                            String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("视频下载失败,是资源id为:" + originMan.getId());
            }
            content = document.toString();

            stationMDoctorData.setContent(content);

            // 正则提取职称等
            String desc = originMan.getDescription() != null ? originMan.getDescription() : "";
            String docPositionStr = extractByRegex(desc, "医生职称:(.*?)；");
            String eduPositionStr = extractByRegex(desc, "教务职称:(.*?)；");
            String eduPostStr = extractByRegex(desc, "教务岗位:(.*?)；");
            Integer docPosition = null;
            Integer eduPosition = null;
            Integer eduPost = null;
            try { if(docPositionStr!=null&&!docPositionStr.isEmpty()) docPosition = Integer.valueOf(docPositionStr); } catch(Exception ignore){}
            try { if(eduPositionStr!=null&&!eduPositionStr.isEmpty()) eduPosition = Integer.valueOf(eduPositionStr); } catch(Exception ignore){}
            try { if(eduPostStr!=null&&!eduPostStr.isEmpty()) eduPost = Integer.valueOf(eduPostStr); } catch(Exception ignore){}
            stationMDoctorData.setDocPosition(docPosition);
            stationMDoctorData.setEduPosition(eduPosition);
            stationMDoctorData.setEduPost(eduPost);
            stationMDoctorData.setGoodat(originMan.getPositive());
            String depart = originMan.getDepart();
            if (depart != null && !depart.isEmpty()) {
                String[] departs = depart.split(",");
                List<String> departNames = new ArrayList<>();
                List<Integer> newDepartIds = new ArrayList<>();
                for (String dep : departs) {
                    departNames.add(catMap.getOrDefault(Integer.parseInt(dep), dep));
                }
                DynamicDataSource.changeBuildDynamicDataSource();
                LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(StationCategory::getName, departNames)
                        .eq(StationCategory::getType,4);
                List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
                for (StationCategory stationCategory : stationCategories) {
                    newDepartIds.add(stationCategory.getId());
                }
                stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
            }

            //TODO 处理擅长
            stationMDoctorData.setGoodat(originMan.getPositive());
            //TODO 处理发布时间
            stationMDoctorData.setCreateTime(Double.valueOf(originMan.getInputtime() + "000"));
            stationMDoctorData.setUpdateTime(Double.valueOf(originMan.getUpdatetime() + "000"));

            chunkResult.put(stationMDoctor, stationMDoctorData);
        }

        return chunkResult;
    }

    // 正则提取工具
    private String extractByRegex(String text, String regex) {
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }


    @Test
    public void moveImageJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        Map<Integer, Integer> map = new HashMap<>();

        //获得当前catID以及目标catID
//        getImageID(map);
        // 新增的栏目ID映射
        map.put(142, 40);


        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveImageJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveDoctorJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i]+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getImageID(Map<Integer, Integer> map){
        DynamicDataSource.changeNewPhpDynamicDataSource();
        LambdaQueryWrapper<HmsCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HmsCategory::getModelId,6);
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper);
        Integer[] origin = new Integer[hmsCategories.size()];
        String[] categoryName = new String[hmsCategories.size()];
        for (int i = 0; i < origin.length; i++) {
            origin[i]= Math.toIntExact(hmsCategories.get(i).getId());
            categoryName[i] = hmsCategories.get(i).getName();

            DynamicDataSource.changeBuildDynamicDataSource();
            LambdaQueryWrapper<StationCategory2> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(StationCategory2::getName,categoryName);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper1);
            if (stationCategory2!=null)
                map.put(origin[i],stationCategory2.getId());
        }

    }



    public void moveImageJavaMethod(Integer originCatId,Integer targetCatId){
        DynamicDataSource.changeNewPhpDynamicDataSource();
//        Integer originCatId=116;
//        Integer targetCatId=23;
        LambdaQueryWrapper<HmsCImage> wrapper =new LambdaQueryWrapper<>();
        //TODO 找到所有存在的图集消息
        wrapper.eq(HmsCImage::getCatid, originCatId)
                .eq(HmsCImage::getState, 0)
                .eq(HmsCImage::getStatus,  99)
                .orderByAsc(HmsCImage::getListorder);
        List<HmsCImage> originImages = hmsCImageMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMImageData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCImage>> taskChunks = divideTasks(originImages, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMImageData2> chunkResult =
                            processTaskChunk3(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMImageData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMImageData2> map : resultMaps) {
            mergedMap.putAll(map);
        }


        // 处理合并后的结果
        for (StationMReference targetImage : mergedMap.keySet()) {
            StationMImageData2 targetImageData = mergedMap.get(targetImage);

            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetImage);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMImageDataMapper.insert(targetImageData);
            //落库 -> folder_resource
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetImageData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetImageData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetImage.setDataId(Long.valueOf(folderResource.getId()));
            targetImage.setSortLevel(Math.toIntExact(targetImage.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetImage);
        }
    }
    private Map<StationMReference, StationMImageData2> processTaskChunk3(
            List<HmsCImage> chunk, Integer targetCatId){
        Map<StationMReference, StationMImageData2> chunkResult=new LinkedHashMap<>();

        try {
            for (HmsCImage originImage : chunk) {
                //TODO 新的image
                StationMReference stationMImage = new StationMReference();
                stationMImage.setCatId(targetCatId);
                stationMImage.setPublishTime(Double.valueOf(originImage.getPublishTime()+"000"));
                stationMImage.setState(99);
                stationMImage.setUri("/"+ CrawlerManager.randomCharacterGenerator()+".html");
                stationMImage.setIsTop(0);

                //TODO 找到修改前的imageData并修改存入新的imageData
                DynamicDataSource.changeNewPhpDynamicDataSource();
                LambdaQueryWrapper<HmsCImageData> wrapper1=new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCImageData::getDid, originImage.getDataid());
                HmsCImageData originImageData=hmsCImageDataMapper.selectOne(wrapper1);

                //TODO 新的imageData
                StationMImageData2 stationMImageData = new StationMImageData2();
                stationMImageData.setUuid(UUID.randomUUID().toString());
                stationMImageData.setTitle(originImageData.getTitle());
                stationMImageData.setCreateTime(Double.valueOf(originImage.getPublishTime()+"000"));
                stationMImageData.setUpdateTime(Double.valueOf(originImage.getPublishTime()+"000"));
                stationMImageData.setCreateUserId(1);
                stationMImageData.setUpdateUserId(1);
                stationMImageData.setState(2);

                //TODO 处理访问量
                stationMImage.setViews(originImageData.getViews());
                //TODO 处理作者
                String author =originImageData.getAuthor();
                author = changeSpecialChat(author);
                if(author != null &&!author.equals("[]")&&!author.isEmpty()){
                    stationMImageData.setAuthor(author);
                }
                //TODO 处理摄影师
                String photographer =originImageData.getPhotographer();
                if(photographer  != null &&!photographer.equals("[]")&&!photographer.isEmpty()){
                    photographer = changeSpecialChat(photographer);
                    stationMImageData.setPhotographer(photographer);
                }
                //TODO 处理来自
                String comeFrom =originImageData.getComefrom();
                if(comeFrom != null &&!comeFrom.isEmpty()){
                    comeFrom = changeSpecialChat(comeFrom);
                    stationMImageData.setComefrom(comeFrom);
                }
                //TODO 处理images
                String images =originImageData.getImages();
                JSONArray jsonArray =  JSONArray.parseArray(images);
                if(jsonArray!=null&&!jsonArray.isEmpty()){
                    //TODO 单图片的图集
                    if(jsonArray.size()==1){
                        JSONObject item =jsonArray.getJSONObject(0);
                        //TODO 设置图集时间戳
                        long currentTimeMillis = System.currentTimeMillis();
                        item.put("id",currentTimeMillis);
                        //TODO 获取url图片进行替换
                        String url = item.getString("url");
                        if(url!=null&&!url.isBlank()){
                            url=CrawlerManager.DownLoad(url,"C:\\Collect_Data\\motherchildren","http://lsz120.web24.foxtest.net/");
                            url=CrawlerManager.changeFileUrl(new File(url));
                        }
                        item.put("url", url);
                        //TODO 设置图片标题
                        item.put("title",originImageData.getTitle()+".jpg");
                        //TODO 获取description并解码Unicode转义字符
                        String description=item.getString("description");
                        if(description!=null&&!description.isEmpty()){
                            String decodedDescription = decodeUnicode(description);
                            decodedDescription=StringUtils.abbreviate(decodedDescription, 11);
                            item.put("description", decodedDescription);
                        }
                    }
                    JSONObject  dbItem=jsonArray.getJSONObject(0);
                    JSONArray jsonArray1=new JSONArray();
                    jsonArray1.add(dbItem);
                    stationMImageData.setImages(jsonArray1.toJSONString());
                }
                //TODO 处理发布时间
//                stationMImageData.setPublishTime(Double.valueOf(originImage.getPublishTime()+"000"));
                chunkResult.put(stationMImage, stationMImageData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }

        return chunkResult;
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }
    /**
     * 解码 Unicode 转义字符，如 \u5e74 -> 年
     */
    public static String decodeUnicode(String input) {
        int length = input.length();
        StringBuilder out = new StringBuilder();

        for (int i = 0; i < length; i++) {
            char c = input.charAt(i);
            if (c == '\\' && i + 5 < length && input.charAt(i + 1) == 'u') {
                String hex = input.substring(i + 2, i + 6);
                try {
                    out.append((char) Integer.parseInt(hex, 16));
                    i += 5;
                } catch (NumberFormatException e) {
                    out.append(c);
                }
            } else {
                out.append(c);
            }
        }

        return out.toString();
    }

}
