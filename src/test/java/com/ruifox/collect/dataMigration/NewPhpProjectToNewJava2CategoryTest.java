package com.ruifox.collect.dataMigration;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.station2.StationCategoryMapper2;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class NewPhpProjectToNewJava2CategoryTest {

    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;

    @Autowired
    private StationCategoryMapper2 stationCategoryMapper2;

    private ExecutorService executor;
    private int threadCount;

    // Model ID映射表：旧model_id -> 新model_id
    private static final Map<Integer, Integer> MODEL_ID_MAPPING = new HashMap<>();
    
    // 全局sort计数器和锁，确保sort值唯一性和线程安全
    private volatile int globalSortCounter = 3;
    private final Object sortLock = new Object();

    static {
        MODEL_ID_MAPPING.put(0, 0);    // 保持不变
        MODEL_ID_MAPPING.put(1, 20);   // 文章
        MODEL_ID_MAPPING.put(2, 19);   // 医生
        MODEL_ID_MAPPING.put(3, 30);   // 单页
        MODEL_ID_MAPPING.put(5, 29);   // 领导
        MODEL_ID_MAPPING.put(6, 41);   // 图集
        MODEL_ID_MAPPING.put(9, 54);   // 招标
        MODEL_ID_MAPPING.put(11, 40);  // 视频
    }

    @Before
    public void setUp() {
        // 根据CPU核心数动态配置线程数
        this.threadCount = Math.min(Runtime.getRuntime().availableProcessors(), 6);
        this.executor = Executors.newFixedThreadPool(threadCount);
        log.info("测试线程池初始化完成，线程数：{}", threadCount);
    }

    @PreDestroy
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 主要的栏目迁移测试方法
     */
    @Test
    public void migrateCategoryTest() {
        try {
            log.info("开始执行栏目数据迁移...");

            // 1. 切换到默认数据源，获取源数据
            DynamicDataSource.changeDefaultDataSource();

            List<HmsCategory> sourceCategories;
            LambdaQueryWrapper<HmsCategory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCategory::getSiteId, 1)
                    .orderByAsc(HmsCategory::getSort)
                    .orderByAsc(HmsCategory::getId);
            sourceCategories = hmsCategoryMapper.selectList(wrapper);


            if (sourceCategories.isEmpty()) {
                log.warn("没有找到需要迁移的栏目数据");
                return;
            }

            log.info("找到 {} 个栏目需要迁移", sourceCategories.size());

            // 2. 处理旧数据库数据
            List<HmsCategory> processedCategories = processAndReassignIds(sourceCategories);

            // 3. 按层级排序（纯内存操作，不涉及数据库）
            List<HmsCategory> sortedCategories = processedCategories.stream()
                    .sorted(Comparator.comparing(HmsCategory::getLevel, Comparator.nullsFirst(Integer::compareTo))
                            .thenComparing(HmsCategory::getSort, Comparator.nullsFirst(Integer::compareTo)))
                    .collect(Collectors.toList());

            // 4. 按层级重新分配sort值
            List<HmsCategory> finalCategories = reassignSortByLevel(sortedCategories);

            // 5. 验证最终处理结果
            validateProcessedData(finalCategories);

            // 6. 切换到新Java2数据源，执行迁移
            DynamicDataSource.changeBuildDynamicDataSource();
            try {
                migrateCategories(finalCategories);
            } finally {
                DynamicDataSource.changeDefaultDataSource();
            }

            log.info("栏目数据迁移完成！");

        } catch (Exception e) {
            log.error("栏目数据迁移失败", e);
            throw e;
        }
    }

    // ------------------------------------------旧数据处理方法------------------------------------------------------

    /**
     * 处理并重新分配ID的综合方法
     * 1. 过滤掉id=1的记录（首页2）
     * 2. 重新分配id（从3开始，连续递增）
     * 3. 更新pid、p_string、sort等关联字段
     */
    private List<HmsCategory> processAndReassignIds(List<HmsCategory> sourceCategories) {
        log.info("开始处理分类数据...");

        // 1. 过滤掉id=1的记录，并按原始id排序
        List<HmsCategory> filteredCategories = sourceCategories.stream()
                .filter(category -> !Long.valueOf(1).equals(category.getId()))
                .sorted(Comparator.comparing(HmsCategory::getId))
                .collect(Collectors.toList());

        log.info("过滤后剩余 {} 条记录", filteredCategories.size());

        if (filteredCategories.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 创建id映射关系（旧id -> 新id，从3开始连续分配）
        Map<Long, Long> idMapping = new HashMap<>();
        idMapping.put(0L, 0L); // 添加0的映射，用于处理顶级分类

        Long newId = 3L;
        for (HmsCategory category : filteredCategories) {
            Long oldId = category.getId();
            idMapping.put(oldId, newId);
            log.debug("ID映射: {} -> {}", oldId, newId);
            newId++;
        }

        log.info("创建id映射关系完成，共 {} 个映射（不含0）", idMapping.size() - 1);

        // 3. 处理每条记录
        List<HmsCategory> processedCategories = new ArrayList<>();

        for (HmsCategory oldCategory : filteredCategories) {
            try {
                // 创建新对象并复制属性
                HmsCategory newCategory = new HmsCategory();
                BeanUtil.copyProperties(oldCategory, newCategory);

                Long oldId = oldCategory.getId();
                Long newMappedId = idMapping.get(oldId);

                // 更新id
                newCategory.setId(newMappedId);

                // 保持原sort值，用于后续按层级排序（不在这里分配新sort值）
                newCategory.setSort(oldCategory.getSort());

                // 更新pid
                Integer oldPid = oldCategory.getPid();
                if (oldPid == null || oldPid == 0) {
                    newCategory.setPid(0); // 顶级分类
                } else if (oldPid == 1) {
                    // 如果原父级是id=1（被删除的首页2），设为顶级
                    newCategory.setPid(0);
                    log.info("记录 '{}' (原id={}) 的父级是被删除的首页2，设置为顶级",
                            newCategory.getName(), oldId);
                } else {
                    Long newPid = idMapping.get(Long.valueOf(oldPid));
                    if (newPid != null) {
                        newCategory.setPid(newPid.intValue());
                    } else {
                        // 父级不在过滤后的数据中（可能是引用了其他site_id的数据），设为顶级
                        newCategory.setPid(0);
                        log.warn("记录 '{}' (原id={}) 的父级id={} 不存在于当前数据集中，设置为顶级",
                                newCategory.getName(), oldId, oldPid);
                    }
                }

                // 更新p_string路径
                String newPString = rebuildPString(oldCategory.getPString(), idMapping);
                newCategory.setPString(newPString);

                processedCategories.add(newCategory);

                log.debug("处理记录完成: '{}' 原id={} -> 新id={}, 新pid={}, 新p_string={}",
                        newCategory.getName(), oldId, newMappedId,
                        newCategory.getPid(), newCategory.getPString());

            } catch (Exception e) {
                log.error("处理记录失败: id={}, name={}", oldCategory.getId(), oldCategory.getName(), e);
                throw new RuntimeException("处理记录失败", e);
            }
        }

        log.info("分类数据处理完成，共处理 {} 条记录", processedCategories.size());
        return processedCategories;
    }

    /**
     * 重建p_string路径
     */
    private String rebuildPString(String oldPString, Map<Long, Long> idMapping) {
        if (StrUtil.isBlank(oldPString)) {
            return "0,";
        }

        // 解析原路径，格式如: "0,111,119,147,"
        String[] pathIds = oldPString.split(",");
        List<String> newPathIds = new ArrayList<>();

        for (String pathIdStr : pathIds) {
            if (StrUtil.isNotBlank(pathIdStr)) {
                try {
                    Long pathId = Long.valueOf(pathIdStr.trim());

                    if (pathId == 0) {
                        newPathIds.add("0");
                    } else if (pathId == 1) {
                        // 跳过被删除的id=1
                        log.debug("路径中包含被删除的id=1，跳过");
                        continue;
                    } else {
                        Long newPathId = idMapping.get(pathId);
                        if (newPathId != null) {
                            newPathIds.add(newPathId.toString());
                        } else {
                            log.warn("路径中的id={} 不存在于映射中，可能是引用了其他数据，跳过", pathId);
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的路径ID: '{}'", pathIdStr);
                }
            }
        }

        // 确保至少有"0"
        if (newPathIds.isEmpty() || !newPathIds.contains("0")) {
            newPathIds.add(0, "0");
        }

        // 重建路径字符串，末尾加逗号
        return String.join(",", newPathIds) + ",";
    }

    /**
     * 验证处理结果
     */
    private void validateProcessedData(List<HmsCategory> categories) {
        Set<Long> idSet = new HashSet<>();
        Set<Integer> sortSet = new HashSet<>();
        int duplicateIdCount = 0;
        int duplicateSortCount = 0;
        int invalidPStringCount = 0;
        int invalidIdCount = 0;
        int invalidSortCount = 0;

        for (HmsCategory category : categories) {
            // 检查重复id
            if (!idSet.add(category.getId())) {
                duplicateIdCount++;
                log.error("发现重复的id: {}, name={}", category.getId(), category.getName());
            }

            // 检查重复sort
            if (!sortSet.add(category.getSort())) {
                duplicateSortCount++;
                log.error("发现重复的sort: {}, id={}, name={}",
                        category.getSort(), category.getId(), category.getName());
            }

            // 验证id从3开始
            if (category.getId() < 3) {
                invalidIdCount++;
                log.error("新id不符合要求（应从3开始）: id={}, name={}",
                        category.getId(), category.getName());
            }

            // 验证sort从3开始
            if (category.getSort() < 3) {
                invalidSortCount++;
                log.error("新sort不符合要求（应从3开始）: sort={}, id={}, name={}",
                        category.getSort(), category.getId(), category.getName());
            }

            // 验证p_string格式（应该是"数字,数字,...数字,"的格式）
            String pString = category.getPString();
            if (StrUtil.isNotBlank(pString)) {
                if (!pString.matches("^(\\d+,)+$")) {
                    invalidPStringCount++;
                    log.error("p_string格式不正确: id={}, name={}, p_string='{}'",
                            category.getId(), category.getName(), pString);
                }

                // 验证p_string中的id都存在
                String[] pathIds = pString.split(",");
                for (String pathIdStr : pathIds) {
                    if (StrUtil.isNotBlank(pathIdStr) && !"0".equals(pathIdStr)) {
                        Long pathId = Long.valueOf(pathIdStr);
                        boolean exists = categories.stream()
                                .anyMatch(c -> c.getId().equals(pathId));
                        if (!exists && pathId >= 3) { // 排除0和小于3的id
                            log.warn("p_string中引用了不存在的id: {}, 在记录 id={}, name={} 中",
                                    pathId, category.getId(), category.getName());
                        }
                    }
                }
            }
        }

        if (duplicateIdCount == 0 && duplicateSortCount == 0 &&
                invalidPStringCount == 0 && invalidIdCount == 0 && invalidSortCount == 0) {
            log.info("数据验证通过：无重复id和sort，p_string格式正确，id和sort都从3开始");
        } else {
            String errorMsg = String.format(
                    "数据验证失败：重复id=%d，重复sort=%d，p_string格式错误=%d，无效id=%d，无效sort=%d",
                    duplicateIdCount, duplicateSortCount, invalidPStringCount, invalidIdCount, invalidSortCount);
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }


    /**
     * 按层级重新分配sort值
     * 1. 按层级分组
     * 2. 同层级内按原sort值排序
     * 3. 从层级1开始，重新分配连续的sort值（从3开始，与ID保持一致）
     */
    private List<HmsCategory> reassignSortByLevel(List<HmsCategory> categories) {
        log.info("开始重新分配sort值，当前数据量：{}", categories.size());
        
        // 重置全局sort计数器（从3开始，与ID分配保持一致）
        synchronized (sortLock) {
            globalSortCounter = 3;
        }
        
        // 按层级分组
        Map<Integer, List<HmsCategory>> levelGroups = categories.stream()
                .collect(Collectors.groupingBy(
                        category -> category.getLevel() != null ? category.getLevel() : 0
                ));
        
        // 按层级顺序处理（从1开始）
        List<HmsCategory> sortedCategories = new ArrayList<>();
        List<Integer> sortedLevels = levelGroups.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
                
        for (Integer level : sortedLevels) {
            List<HmsCategory> levelCategories = levelGroups.get(level);
            
            // 同层级内按原sort值排序
            levelCategories.sort(Comparator.comparing(
                    category -> category.getSort() != null ? category.getSort() : 0
            ));
            
            // 为当前层级的所有分类分配新的sort值
            synchronized (sortLock) {
                for (HmsCategory category : levelCategories) {
                    int oldSort = category.getSort() != null ? category.getSort() : 0;
                    category.setSort(globalSortCounter);
                    
                    log.debug("层级 {} - 分类 '{}': 原sort={} -> 新sort={}", 
                            level, category.getName(), oldSort, globalSortCounter);
                    
                    globalSortCounter++;
                }
            }
            
            sortedCategories.addAll(levelCategories);
            log.info("层级 {} 处理完成，包含 {} 个分类，当前全局sort计数器：{}", 
                    level, levelCategories.size(), globalSortCounter - 1);
        }
        
        log.info("sort值重新分配完成，总计 {} 个分类，最终sort范围：3-{}", 
                sortedCategories.size(), globalSortCounter - 1);
        
        return sortedCategories;
    }

    // --------------------------------------------多线程迁移方法-------------------------------------------------------

    /**
     * 执行栏目迁移（多线程处理）
     */
    private void migrateCategories(List<HmsCategory> categories) {
        // 分批处理
        List<List<HmsCategory>> batches = divideTasks(categories, threadCount);

        // 创建 CompletableFuture 列表
        List<CompletableFuture<List<StationCategory2>>> futures = new ArrayList<>();

        // 提交任务到线程池
        for (int i = 0; i < batches.size(); i++) {
            final int batchIndex = i;
            final List<HmsCategory> batch = batches.get(i);

            CompletableFuture<List<StationCategory2>> future = CompletableFuture
                    .supplyAsync(() -> {
                        log.info("开始处理批次 {}, 包含 {} 个栏目", batchIndex + 1, batch.size());
                        return processCategoryBatch(batch);
                    }, executor)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            log.error("批次 {} 处理失败", batchIndex + 1, throwable);
                        } else {
                            log.info("批次 {} 处理完成", batchIndex + 1);
                        }
                    });

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        try {
            List<StationCategory2> results = futures.stream()
                    .map(CompletableFuture::join)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            log.info("所有批次处理完成，共迁移 {} 个栏目", results.size());
        } catch (CompletionException e) {
            log.error("任务执行过程中发生异常", e.getCause());
            throw e;
        }
    }

    /**
     * 处理单个批次的栏目数据
     */
    private List<StationCategory2> processCategoryBatch(List<HmsCategory> batch) {
        List<StationCategory2> batchResults = new ArrayList<>();

        try {
            // 重要：在子线程中重新设置数据源（解决ThreadLocal问题）
            DynamicDataSource.changeBuildDynamicDataSource();
            
            for (HmsCategory sourceCategory : batch) {
                try {
                    StationCategory2 targetCategory = convertCategory(sourceCategory);
                    if (targetCategory != null) {
                        // 插入到目标数据源
                        stationCategoryMapper2.insert(targetCategory);
                        batchResults.add(targetCategory);
                        log.debug("成功迁移栏目：{} -> {}", sourceCategory.getName(), targetCategory.getId());
                    }
                } catch (Exception e) {
                    log.error("迁移栏目失败：{}", sourceCategory.getName(), e);
                }
            }
        } finally {
            // 恢复默认数据源（可选，其实可以没有）
            DynamicDataSource.changeDefaultDataSource();
        }

        return batchResults;
    }


    /**
     * 将任务列表分割成多个批次
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int batchCount) {
        List<List<T>> batches = new ArrayList<>();
        int taskCount = tasks.size();
        int batchSize = Math.max(1, (taskCount + batchCount - 1) / batchCount);

        for (int i = 0; i < taskCount; i += batchSize) {
            int end = Math.min(i + batchSize, taskCount);
            batches.add(tasks.subList(i, end));
        }

        return batches;
    }

    // -------------------------------------迁移数据转换方法-------------------------------------------

    /**
     * 转换栏目数据格式
     * 完全重写的字段映射逻辑，按照新旧数据库字段对应关系
     */
    private StationCategory2 convertCategory(HmsCategory source) {
        if (source == null) {
            return null;
        }

        StationCategory2 target = new StationCategory2();

        // 1. 一比一迁移字段
        // id: Long → Integer 转换（安全转换）
        target.setId(safeLongToInteger(source.getId()));

        // site_id: int → int
        target.setSiteId(source.getSiteId());

        // pid: int → int
        target.setPid(source.getPid());

        // level: int → int
        target.setLevel(source.getLevel());

        // sort → sort_level: int → int
        target.setSortLevel(source.getSort());

        // name: String → String
        target.setName(source.getName());

        // type: int → int
        target.setType(source.getType());

        // status → state: int → int
        target.setState(source.getStatus());

        // info: String → String (一比一复制)
        target.setInfo(source.getInfo());

        // model_id: Integer → Integer (通过映射表转换)
        target.setModelId(convertModelId(source.getModelId()));

        // is_show: Integer → Integer (一比一复制)
        target.setIsShow(source.getIsShow());

        // 2. 格式转换字段
        // p_string → parent_string: "0,111,119," → "[0,111,119]"
        String parentString = convertPStringToParentString(source.getPString());
        target.setParentString(parentString);

        // url → uri: "yygk_yyjj" → "/yygk_yyjj"
        String uri = convertUrlToUri(source.getUrl());
        target.setUri(uri);

        // 3. 时间字段设置
        // create_time和update_time：设置为当前时间戳(Double类型)
        Double currentTimestamp = (double) System.currentTimeMillis();
        target.setCreateTime(currentTimestamp);
        target.setUpdateTime(currentTimestamp);

        return target;
    }

    /**
     * 转换p_string格式到parent_string格式
     * 从 "0,111,119," 转换为 "[0,111,119]"
     */
    private String convertPStringToParentString(String pString) {
        if (StrUtil.isBlank(pString)) {
            return "[0]";  // 默认值
        }

        // 移除末尾的逗号，然后用[]包裹
        String trimmed = pString.trim();
        if (trimmed.endsWith(",")) {
            trimmed = trimmed.substring(0, trimmed.length() - 1);
        }

        return "[" + trimmed + "]";
    }

    /**
     * 转换url格式到uri格式
     * 在原url前面加上"/"
     */
    private String convertUrlToUri(String url) {
        if (StrUtil.isBlank(url)) {
            return "/";  // 默认值
        }

        // 如果已经以"/"开头，直接返回
        if (url.startsWith("/")) {
            return url;
        }

        // 在前面加上"/"
        return "/" + url;
    }

        /**
     * 转换model_id
     * 通过映射表将旧model_id转换为新model_id
     */
    private Integer convertModelId(Integer oldModelId) {
        if (oldModelId == null) {
            return null;
        }
        
        // 通过映射表转换
        Integer newModelId = MODEL_ID_MAPPING.get(oldModelId);
        if (newModelId != null) {
            return newModelId;
        }
        
        // 如果映射表中没有对应关系，记录警告并返回原值
        log.warn("未找到model_id映射: oldModelId={}, 保持原值", oldModelId);
        return oldModelId;
    }
    
    /**
     * 安全的Long到Integer转换
     * 防止溢出问题
     */
    private Integer safeLongToInteger(Long longValue) {
        if (longValue == null) {
            return null;
        }
        
        // 检查是否超出Integer范围
        if (longValue > Integer.MAX_VALUE || longValue < Integer.MIN_VALUE) {
            log.error("Long值超出Integer范围，发生溢出: {}, 最大值: {}, 最小值: {}", 
                     longValue, Integer.MAX_VALUE, Integer.MIN_VALUE);
            throw new IllegalArgumentException("Long值超出Integer范围: " + longValue);
        }
        
        return longValue.intValue();
    }

}
