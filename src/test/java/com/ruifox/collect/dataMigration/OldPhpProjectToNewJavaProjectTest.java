package com.ruifox.collect.dataMigration;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.dao.mapper.tbl.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.module.entity.tbl.*;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class OldPhpProjectToNewJavaProjectTest {
    @Autowired
    private TblCNewsMapper tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapper tblCNewsDataMapper;
    @Autowired
    private TblCManDataMxfyMapper tblCManDataMxfyMapper;
    @Autowired
    private TblCManMapper tblCManMapper;
    @Autowired
    private StationMArticleMapper stationMArticleMapper;
    @Autowired
    private StationMArticleDataMapper stationMArticleDataMapper;
    @Autowired
    private StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    private StationMDoctorDataMapper stationMDoctorDataMapper;
    @Autowired
    private StationMTeacherMapper stationMTeacherMapper;
    @Autowired
    private StationMTeacherDataMapper stationMTeacherDataMapper;

    @Autowired
    private StationCategoryMapper stationCategoryMapper;
    @Autowired
    private TblCategoryMapper tblCategoryMapper;
    @Autowired
    private TblCPictureMapper tblCPictureMapper;
    @Autowired
    private TblCPictureDataMapper tblCPictureDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Autowired
    private StationMImageDataMapper stationMImageDataMapper;

    @Autowired
    private StationMNewspaperMapper stationMNewspaperMapper;
    @Autowired
    private StationMNewspaperDataMapper stationMNewspaperDataMapper;

    @Autowired
    private StationMVideoMapper stationMVideoMapper;
    @Autowired
    private StationMVideoDataMapper stationMVideoDataMapper;
    @Autowired
    private TblCVideoMapper tblCVideoMapper;
    @Autowired
    private TblCVideoDataMapper tblCVideoDataMapper;

    //下载时的来源页面
    private static String refer = "http://lsz120.web24.foxtest.net/";
    //下载文件存储地址
    private static String downloadPath = "C:\\Collect_Data\\lszph";

    //判断是否为内链的host
    private static String localhost = "cd3hospital";


    @Test
    public void moveNewsJava() {
        Map<Integer, Integer> map = new HashMap<>();
        map.put(16, 281);
        Integer id = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<TblCNews> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCNews::getCatid, originCatId)
                .eq(TblCNews::getState, 0)
                .eq(TblCNews::getStatus, 99)
                .orderByAsc(TblCNews::getListorder);
        List<TblCNews> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 3;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMArticle, StationMArticleData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCNews>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMArticle, StationMArticleData> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMArticle, StationMArticleData> mergedMap = new LinkedHashMap<>();
        for (Map<StationMArticle, StationMArticleData> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMArticle targetNew : mergedMap.keySet()) {
            StationMArticleData targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            stationMArticleDataMapper.insert(targetNewData);
            // 引用数据引用资源数据id
            targetNew.setDataId(targetNewData.getDataId());
            // 加入引用数据
            stationMArticleMapper.insert(targetNew);
            // 根据id更新排序
            targetNew.setSortLevel(targetNew.getId());
            stationMArticleMapper.updateById(targetNew);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMArticle, StationMArticleData> processTaskChunk(
            List<TblCNews> chunk, Integer targetCatId) {
        Map<StationMArticle, StationMArticleData> chunkResult = new LinkedHashMap<>();
        try {
            for (TblCNews originNew : chunk) {
                // 新的article
                StationMArticle stationMArticle = new StationMArticle();
                stationMArticle.setCatId(targetCatId);
                stationMArticle.setPublishUserId(1);
                stationMArticle.setCreateTime(Double.valueOf(originNew.getInputtime() + "000"));
                stationMArticle.setUpdateTime(Double.valueOf(originNew.getUpdatetime() + "000"));
                stationMArticle.setState(99);
                stationMArticle.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMArticle.setViews(originNew.getViews());
                stationMArticle.setIsTop(0);
                stationMArticle.setIsLock(0);

                String linkUrl = originNew.getUrl();
                int isLink = 1;
                //根据url判断是否为外链
                if (linkUrl.contains(localhost)) {
                    isLink = 0;
                }

                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblCNewsData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCNewsData::getId, originNew.getDataid());
                TblCNewsData originNewsData = tblCNewsDataMapper.selectOne(wrapper1);

                // 新的articleData
                StationMArticleData stationMArticleData = new StationMArticleData();
                stationMArticleData.setUuid(UUID.randomUUID().toString());
                stationMArticleData.setTitle(originNewsData.getTitle());
                // 处理作者
                String author = originNewsData.getAuthor();
                author = changeSpecialChat(author);
                if (!author.equals("0") && !author.isEmpty())
                    stationMArticleData.setAuthor(originNewsData.getAuthor());

                // 缩略图处理 （只有一张图片）
                String thumb = originNewsData.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMArticleData.setThumb(thumb);

                // 处理正文,摘要
                String content = originNewsData.getContent();
                String description = originNewsData.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getId());
                    }
                    content = document.toString();
                } else {
                    // 是链接的话,content为空
                    content = linkUrl;
                    description = linkUrl;
                }
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                // 处理来源
                stationMArticleData.setComefrom(originNewsData.getComefrom());
                // 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                // 处理发布时间
                stationMArticleData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }

        return chunkResult;
    }

    @Test
    public void moveDoctorJava() {
        Integer originCatId = 38;
        Integer targetCatId = 246;

        LambdaQueryWrapper<TblCMan> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCMan::getCatid, originCatId)
                .eq(TblCMan::getState, 0)
                .eq(TblCMan::getStatus, 99)
                .orderByAsc(TblCMan::getListorder);
        List<TblCMan> originMans = tblCManMapper.selectList(wrapper);

        List<TblCategory> tblCategories = tblCategoryMapper.selectList(null);
        Map<Integer, String> catMap = new HashMap<>();
        for (TblCategory tblCategory : tblCategories) {
            catMap.put(tblCategory.getCatid(), tblCategory.getCatname());
        }

        // 定义线程数量
        int threadCount = 6;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMDoctor, StationMDoctorData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, new HashMap<>())));

        // 分割任务
        List<List<TblCMan>> taskChunks = divideTasks(originMans, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMDoctor, StationMDoctorData> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex), targetCatId, catMap);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMDoctor, StationMDoctorData> mergedMap = new LinkedHashMap<>();
        for (Map<StationMDoctor, StationMDoctorData> map : resultMaps) {
            if (map != null) { // 添加空值检查
                mergedMap.putAll(map);
            }
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMDoctor targetMan : mergedMap.keySet()) {
            StationMDoctorData targetManData = mergedMap.get(targetMan);
            // 先加入资源数据
            stationMDoctorDataMapper.insert(targetManData);
            // 引用数据引用资源数据id
            targetMan.setDataId(targetManData.getDataId());
            // 加入引用数据
            stationMDoctorMapper.insert(targetMan);
            // 根据id更新排序
            targetMan.setSortLevel(targetMan.getId());
            stationMDoctorMapper.updateById(targetMan);
        }
    }

    private Map<StationMDoctor, StationMDoctorData> processTaskChunk2(
            List<TblCMan> chunk, Integer targetCatId, Map<Integer, String> catMap) {
        Map<StationMDoctor, StationMDoctorData> chunkResult = new LinkedHashMap<>();

        try {
            for (TblCMan originMan : chunk) {
                DynamicDataSource.changeDefaultDataSource();
                // 新的doctor
                StationMDoctor stationMDoctor = new StationMDoctor();
                stationMDoctor.setCatId(targetCatId);
                stationMDoctor.setPublishUserId(1);
                stationMDoctor.setCreateTime(Double.valueOf(originMan.getInputtime() + "000"));
                stationMDoctor.setUpdateTime(Double.valueOf(originMan.getUpdatetime() + "000"));
                stationMDoctor.setState(99);
                stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMDoctor.setViews(originMan.getViews());
                stationMDoctor.setIsTop(0);
                stationMDoctor.setIsLock(0);

                // 找到修改前的manData并修改后存入新的doctorData
                LambdaQueryWrapper<TblCManDataMxfy> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCManDataMxfy::getId, originMan.getDataid());
                TblCManDataMxfy originManData = tblCManDataMxfyMapper.selectOne(wrapper1);

                // 新的articleData
                StationMDoctorData stationMDoctorData = new StationMDoctorData();
                stationMDoctorData.setUuid(UUID.randomUUID().toString());
                stationMDoctorData.setTitle(originManData.getTitle());

                // 缩略图处理 （只有一张图片）
                String thumb = originManData.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMDoctorData.setThumb(thumb);

                // 处理正文,摘要
                String content = originManData.getContent();
                content = changeSpecialChat(content);
                Document document = Jsoup.parse(content);
                Elements imgList = document.select("img");
                Elements linkList = document.select("a");
                Elements videoList = document.select("video");
                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(imgUrl)) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("图片下载失败,是资源id为:" + originManData.getId());
                }

                // 链接路径集合处理
                try {
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNotBlank(attachUrl)) {
                            attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(attachUrl)) {
                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                element.attr("href", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("链接下载失败,是资源id为:" + originManData.getId());
                }

                // 视频路径集合处理
                try {
                    for (Element element : videoList) {
                        String videoUrl = element.attr("src");
                        if (StringUtils.isNotBlank(videoUrl)) {
                            videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(videoUrl)) {
                                String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                element.attr("src", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("视频下载失败,是资源id为:" + originManData.getId());
                }
                content = document.toString();

                stationMDoctorData.setContent(content);

                // 处理医师职称
                String protit = originManData.getProtit();
                if (!protit.isEmpty()) {
                    Map<Integer, Integer> docPositionMap = getDocPositionMap();
                    stationMDoctorData.setDocPosition(docPositionMap.get(Integer.valueOf(protit)));
                }

                // 处理擅长
                stationMDoctorData.setGoodat(originManData.getGoodat());
                // 处理科室
                //TODO 获取以前科室名称
                String depart = originManData.getDepart();
                if (!depart.isEmpty()) {
                    String[] departs = depart.split(",");
                    List<String> departNames = new ArrayList<>();
                    List<Integer> newDepartIds = new ArrayList<>();
                    for (int i = 0; i < departs.length; i++) {
                        departNames.add(catMap.get(Integer.valueOf(departs[i])));
                    }
                    //通过科室名称匹配现在科室id
                    DynamicDataSource.changeDynamicDataSource();
                    LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
                    wrapper.in(StationCategory::getName, departNames);
                    List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
                    for (StationCategory stationCategory : stationCategories) {
                        newDepartIds.add(stationCategory.getId());
                    }
                    stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
                }

                // 处理发布时间
                stationMDoctorData.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));

                chunkResult.put(stationMDoctor, stationMDoctorData);
            }
        } catch (Exception e) {
            log.error("数据处理出错", e);
        }

        return chunkResult;
    }

//    private Map<StationMDoctor, StationMDoctorData> processTaskChunk2XBQ(
//            List<TblCMan> chunk, Integer targetCatId, Map<Integer, String> catMap) {
//        Map<StationMDoctor, StationMDoctorData> chunkResult = new LinkedHashMap<>();
//        try {
//            for (TblCMan originMan : chunk) {
//                DynamicDataSource.changeDefaultDataSource();
//                // 新的doctor
//                StationMDoctor stationMDoctor = new StationMDoctor();
//                stationMDoctor.setCatId(targetCatId);
//                stationMDoctor.setPublishUserId(1);
//                stationMDoctor.setCreateTime(Double.valueOf(originMan.getInputtime() + "000"));
//                stationMDoctor.setUpdateTime(Double.valueOf(originMan.getUpdatetime() + "000"));
//                stationMDoctor.setState(99);
//                stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
//                stationMDoctor.setViews(originMan.getViews());
//                stationMDoctor.setIsTop(0);
//                stationMDoctor.setIsLock(0);
//
//                // 找到修改前的manData并修改后存入新的doctorData
//                LambdaQueryWrapper<TblCManDataXbq> wrapper1 = new LambdaQueryWrapper<>();
//                wrapper1.eq(TblCManDataXbq::getId, originMan.getDataid());
//                TblCManDataXbq originManData = tblCManDataXbqMapper.selectOne(wrapper1);
//
//                // 新的articleData
//                StationMDoctorData stationMDoctorData = new StationMDoctorData();
//                stationMDoctorData.setUuid(UUID.randomUUID().toString());
//                stationMDoctorData.setTitle(originManData.getTitle());
//
//                // 缩略图处理 （只有一张图片）
//                String thumb = originManData.getThumb();
//                // 调用接口
//                if (!thumb.isBlank()) {
//                    thumb = CrawlerManager.DownLoad(thumb, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
//                }
//                stationMDoctorData.setThumb(thumb);
//
//                // 处理正文,摘要
//                String content = originManData.getContent();
//                content = changeSpecialChat(content);
//                Document document = Jsoup.parse(content);
//                Elements imgList = document.select("img");
//                Elements linkList = document.select("a");
//                Elements videoList = document.select("video");
//                // 图片路径集合处理
//                try {
//                    for (Element element : imgList) {
//                        String imgUrl = element.attr("src");
//                        if (StringUtils.isNotBlank(imgUrl)) {
//                            imgUrl = CrawlerManager.DownLoad(imgUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(imgUrl)) {
//                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
//                                element.attr("src", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("图片下载失败,是资源id为:" + originManData.getId());
//                }
//
//                // 链接路径集合处理
//                try {
//                    for (Element element : linkList) {
//                        String attachUrl = element.attr("href");
//                        if (StringUtils.isNotBlank(attachUrl)) {
//                            attachUrl = CrawlerManager.DownLoad(attachUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(attachUrl)) {
//                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
//                                element.attr("href", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("链接下载失败,是资源id为:" + originManData.getId());
//                }
//
//                // 视频路径集合处理
//                try {
//                    for (Element element : videoList) {
//                        String videoUrl = element.attr("src");
//                        if (StringUtils.isNotBlank(videoUrl)) {
//                            videoUrl = CrawlerManager.DownLoad(videoUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(videoUrl)) {
//                                String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
//                                element.attr("src", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("视频下载失败,是资源id为:" + originManData.getId());
//                }
//                content = document.toString();
//
//                stationMDoctorData.setContent(content);
//
//                // 处理医师职称
//                String docPosition = originManData.getDocPosition();
//                if (!docPosition.isEmpty()) {
//                    stationMDoctorData.setDocPosition(Integer.valueOf(docPosition));
//                }
//
//                // 处理擅长
//                stationMDoctorData.setGoodat(originManData.getGoodat());
//                // 处理科室
//                //TODO 获取以前科室名称
//                String depart = originManData.getDepart();
//                if (!depart.isEmpty()) {
//                    String[] departs = depart.split(",");
//                    List<String> departNames = new ArrayList<>();
//                    List<Integer> newDepartIds = new ArrayList<>();
//                    for (int i = 0; i < departs.length; i++) {
//                        departNames.add(catMap.get(Integer.valueOf(departs[i])));
//                    }
//                    //通过科室名称匹配现在科室id
//                    DynamicDataSource.changeDynamicDataSource();
//                    LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
//                    wrapper.in(StationCategory::getName, departNames);
//                    List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
//                    for (StationCategory stationCategory : stationCategories) {
//                        newDepartIds.add(stationCategory.getId());
//                    }
//                    stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
//                }
//
//                // 处理发布时间
//                stationMDoctorData.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));
//
//                chunkResult.put(stationMDoctor, stationMDoctorData);
//            }
//        } catch (Exception e) {
//            log.error("解析数据失败", e);
//        }
//
//        return chunkResult;
//    }

    @Test
    public void moveNewsToTeacherJava() {
        Integer originCatId = 3843;
        Integer targetCatId = 254;

        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<TblCNews> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCNews::getCatid, originCatId)
                .eq(TblCNews::getState, 0)
                .eq(TblCNews::getStatus, 99)
                .orderByAsc(TblCNews::getListorder);
        List<TblCNews> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        Map<StationMTeacher, StationMTeacherData> newTeacherDataMap = new LinkedHashMap<>();
        for (TblCNews originNew : originNews) {
            // 新的teacher
            StationMTeacher stationMTeacher = new StationMTeacher();
            stationMTeacher.setCatId(targetCatId);
            stationMTeacher.setPublishUserId(1);
            stationMTeacher.setCreateTime(Double.valueOf(originNew.getInputtime() + "000"));
            stationMTeacher.setUpdateTime(Double.valueOf(originNew.getUpdatetime() + "000"));
            stationMTeacher.setState(99);
            stationMTeacher.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMTeacher.setViews(originNew.getViews());
            stationMTeacher.setIsTop(0);
            stationMTeacher.setIsLock(0);


            // 找到修改前的newsData并修改后存入新的teacherData
            LambdaQueryWrapper<TblCNewsData> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TblCNewsData::getId, originNew.getDataid());
            TblCNewsData originNewsData = tblCNewsDataMapper.selectOne(wrapper1);

            // 新的articleData
            StationMTeacherData stationMTeacherData = new StationMTeacherData();
            stationMTeacherData.setUuid(UUID.randomUUID().toString());
            stationMTeacherData.setTitle(originNewsData.getTitle());

            // 缩略图处理 （只有一张图片）
            String thumb = originNewsData.getThumb();
            // 调用接口
            if (!thumb.isBlank()) {
                thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                thumb = CrawlerManager.changeFileUrl(new File(thumb));
            }
            stationMTeacherData.setThumb(thumb);

            // 处理培养研究生学校、指导研究生层次、研究方向、邮箱
            String description = originNewsData.getDescription();
            description = changeSpecialChat(description);
            description = description.replaceAll("</br>", "");
            String[] split = description.split("\n");
            String school = "";
            String level = "";
            String direction = "";
            String mail = "";
            for (String s : split) {
                if (s.contains("联合培养研究生学校")) {
                    school = s.split("：")[1];
                } else if (s.contains("当前指导研究生层次")) {
                    level = s.split("：")[1];
                } else if (s.contains("研究方向")) {
                    direction = s.split("：")[1];
                } else if (s.contains("邮箱")) {
                    mail = s.split("：")[1];
                }
            }
            stationMTeacherData.setSchool(school);
            stationMTeacherData.setCurrentLevel(level);
            stationMTeacherData.setResearchDirection(direction);
            stationMTeacherData.setEmail(mail);

            // 处理发布时间
            stationMTeacherData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

            newTeacherDataMap.put(stationMTeacher, stationMTeacherData);
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMTeacher targetTeacher : newTeacherDataMap.keySet()) {
            StationMTeacherData targetTeacherData = newTeacherDataMap.get(targetTeacher);
            // 先加入资源数据
            stationMTeacherDataMapper.insert(targetTeacherData);
            // 引用数据引用资源数据id
            targetTeacher.setDataId(targetTeacherData.getDataId());
            // 加入引用数据
            stationMTeacherMapper.insert(targetTeacher);
            // 根据id更新排序
            targetTeacher.setSortLevel(targetTeacher.getId());
            stationMTeacherMapper.updateById(targetTeacher);
        }
    }

//    @Test
//    public void moveImagesJava() {
//        Map<Integer, Integer> map = new HashMap<>();
//        map.put(3613, 562);
//        map.put(3357, 706);
//        map.put(2505, 896);
//        map.put(2640, 1024);
//        Integer id = 0;
//        for (Integer originCatId : map.keySet()) {
//            try {
//                moveImagesToTeacherJava(originCatId, map.get(originCatId));
//            } catch (Exception e) {
//                log.error("moveNewsJavaMethod error:" + originCatId, e);
//                id = originCatId;
//                break;
//            }
//        }
//        if (id != 0) {
//            log.info("运行到id为: " + id + " 报错");
//        }
//    }
//
//    public void moveImagesToTeacherJava(Integer originCatId, Integer targetCatId) {
//        DynamicDataSource.changeDefaultDataSource();
//        LambdaQueryWrapper<TblCPicture> wrapper = new LambdaQueryWrapper<>();
//        // 找到所有存在的信息
//        wrapper.eq(TblCPicture::getCatid, originCatId)
//                .eq(TblCPicture::getState, 0)
//                .eq(TblCPicture::getStatus, 99)
//                .orderByAsc(TblCPicture::getListorder);
//        List<TblCPicture> originPictures = tblCPictureMapper.selectList(wrapper);
//        if (originPictures.isEmpty()) {
//            return;
//        }
//
//        Map<StationMImage, StationMImageData> newImageDataMap = new LinkedHashMap<>();
//        for (TblCPicture originPicture : originPictures) {
//            // 新的teacher
//            StationMImage stationMImage = new StationMImage();
//            stationMImage.setCatId(targetCatId);
//            stationMImage.setPublishUserId(1);
//            stationMImage.setCreateTime(Double.valueOf(originPicture.getInputtime() + "000"));
//            stationMImage.setUpdateTime(Double.valueOf(originPicture.getUpdatetime() + "000"));
//            stationMImage.setState(99);
//            stationMImage.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
//            stationMImage.setViews(originPicture.getViews());
//            stationMImage.setIsTop(0);
//            stationMImage.setIsLock(0);
//
//
//            // 找到修改前的newsData并修改后存入新的teacherData
//            LambdaQueryWrapper<TblCPictureData> wrapper1 = new LambdaQueryWrapper<>();
//            wrapper1.eq(TblCPictureData::getId, originPicture.getDataid());
//            TblCPictureData originPicturesData = tblCPictureDataMapper.selectOne(wrapper1);
//
//            // 新的articleData
//            StationMImageData stationMImageData = new StationMImageData();
//            stationMImageData.setUuid(UUID.randomUUID().toString());
//            stationMImageData.setTitle(originPicturesData.getTitle());
//
//            // 图片处理 （只有一张图片）
//            String thumb = originPicturesData.getThumb();
//            String description = originPicturesData.getDescription();
//            List<Map<String, Object>> imagesList = new ArrayList<>();
//            // 调用接口
//            if (!thumb.isBlank()) {
//                thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
//                thumb = CrawlerManager.changeFileUrl(new File(thumb));
//                long nowTime = System.currentTimeMillis();
//                Map<String, Object> map = new LinkedHashMap<>();
//                map.put("id", nowTime);
//                map.put("url", thumb);
//                map.put("title", originPicturesData.getTitle());
//                map.put("description", description != null ? description : "");
//                imagesList.add(map);
//            }
//            stationMImageData.setImages(JSONObject.toJSONString(imagesList));
//            //来源处理
//            String comefrom = originPicturesData.getComefrom();
//            if (comefrom != null)
//                stationMImageData.setComefrom(comefrom);
//            //简介处理
//            if (description != null)
//                stationMImageData.setDescription(description);
//
//            // 处理发布时间
//            stationMImageData.setPublishTime(Double.valueOf(originPicture.getInputtime() + "000"));
//
//            newImageDataMap.put(stationMImage, stationMImageData);
//        }
//
//        // 切换到总院的数据源
//        DynamicDataSource.changeDynamicDataSource();
//        // 处理合并后的结果
//        for (StationMImage targetImage : newImageDataMap.keySet()) {
//            StationMImageData targetImageData = newImageDataMap.get(targetImage);
//            // 先加入资源数据
//            stationMImageDataMapper.insert(targetImageData);
//            // 引用数据引用资源数据id
//            targetImage.setDataId(targetImageData.getDataId());
//            // 加入引用数据
//            stationMImageMapper.insert(targetImage);
//            // 根据id更新排序
//            targetImage.setSortLevel(targetImage.getId());
//            stationMImageMapper.updateById(targetImage);
//        }
//    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }



    @Test
    public void movePicturesJava() {
        Map<Integer, Integer> map = new HashMap<>();
        // 添加您的栏目映射关系，格式：原栏目ID -> 目标栏目ID
        map.put(24, 30);

        Integer id = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                movePicturesToNewspaperJava(originCatId, map.get(originCatId));
            } catch (Exception e) {
                log.error("movePicturesJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void movePicturesToNewspaperJava(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<TblCPicture> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的图片信息
        wrapper.eq(TblCPicture::getCatid, originCatId)
                .eq(TblCPicture::getState, 0)
                .eq(TblCPicture::getStatus, 99)
                .orderByAsc(TblCPicture::getListorder);
        List<TblCPicture> originPictures = tblCPictureMapper.selectList(wrapper);
        if (originPictures.isEmpty()) {
            return;
        }

        Map<StationMNewspaper, StationMNewspaperData> newNewspaperDataMap = new LinkedHashMap<>();
        for (TblCPicture originPicture : originPictures) {
            // 新的newspaper
            StationMNewspaper stationMNewspaper = new StationMNewspaper();
            stationMNewspaper.setCatId(targetCatId);
            stationMNewspaper.setPublishUserId(1);
            stationMNewspaper.setCreateTime(Double.valueOf(originPicture.getInputtime() + "000"));
            stationMNewspaper.setUpdateTime(Double.valueOf(originPicture.getUpdatetime() + "000"));
            stationMNewspaper.setState(99);
            stationMNewspaper.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMNewspaper.setViews(originPicture.getViews());
            stationMNewspaper.setIsTop(originPicture.getIstop());
            stationMNewspaper.setIsLock(0);

            // 找到对应的pictureData
            LambdaQueryWrapper<TblCPictureData> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TblCPictureData::getId, originPicture.getId());
            TblCPictureData originPictureData = tblCPictureDataMapper.selectOne(wrapper1);

            // 新的newspaperData
            StationMNewspaperData stationMNewspaperData = new StationMNewspaperData();
            stationMNewspaperData.setUuid(UUID.randomUUID().toString());
            stationMNewspaperData.setTitle(originPicture.getTitle());

            // 处理图片数据 - 简化格式，只包含图片信息，article为空数组
            String imagesStr = originPictureData.getImages();
            String newspaperJson = convertImagesToSimpleNewspaperJson(imagesStr, originPicture.getTitle());
            stationMNewspaperData.setNewspaper(newspaperJson);

            // 来源处理
            String comefrom = originPicture.getComefrom();
            if (comefrom != null && !comefrom.isEmpty()) {
                stationMNewspaperData.setComefrom(comefrom);
            }

            // 缩略图处理
            String thumb = originPicture.getThumb();
            if (thumb != null && !thumb.isEmpty()) {
                // 下载并处理缩略图
                try {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    stationMNewspaperData.setThumb(thumb);
                } catch (Exception e) {
                    log.error("处理缩略图失败: " + thumb, e);
                }
            }

            // 内容处理
            if (originPictureData.getContent() != null) {
                String content = changeSpecialChat(originPictureData.getContent());
                stationMNewspaperData.setContent(content);
            }

            // 处理发布时间
            stationMNewspaperData.setPublishTime(Double.valueOf(originPicture.getInputtime() + "000"));

            // 处理发布年份
            Date publishDate = new Date(Long.valueOf(originPicture.getInputtime() + "000"));
            SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
            stationMNewspaperData.setPublishYear(Integer.valueOf(yearFormat.format(publishDate)));

            newNewspaperDataMap.put(stationMNewspaper, stationMNewspaperData);
        }

        // 切换到目标数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMNewspaper targetNewspaper : newNewspaperDataMap.keySet()) {
            StationMNewspaperData targetNewspaperData = newNewspaperDataMap.get(targetNewspaper);
            // 先加入资源数据
            stationMNewspaperDataMapper.insert(targetNewspaperData);
            // 引用数据引用资源数据id
            targetNewspaper.setDataId(targetNewspaperData.getDataId());
            // 加入引用数据
            stationMNewspaperMapper.insert(targetNewspaper);
            // 根据id更新排序
            targetNewspaper.setSortLevel(Math.toIntExact(targetNewspaper.getId()));
            stationMNewspaperMapper.updateById(targetNewspaper);
        }
    }

    /**
     * 将PHP数组格式的images转换为简化的newspaper JSON格式（图片类型，无文章）
     */
    private String convertImagesToSimpleNewspaperJson(String imagesStr, String title) {
        try {
            // 解析PHP数组格式的images
            List<Map<String, Object>> imagesList = parsePHPArrayImages(imagesStr);

            List<Map<String, Object>> newspaperList = new ArrayList<>();

            for (Map<String, Object> imageInfo : imagesList) {
                String imageUrl = (String) imageInfo.get("url");

                if (imageUrl != null && !imageUrl.isEmpty()) {
                    // 下载并处理图片
                    try {
                        String downloadedUrl = CrawlerManager.DownLoad(imageUrl, downloadPath, refer);
                        String processedUrl = CrawlerManager.changeFileUrl(new File(downloadedUrl));

                        // 简化的newspaper格式 - 只包含图片信息，article为空
                        Map<String, Object> newspaperItem = new LinkedHashMap<>();
                        newspaperItem.put("title", title);
                        newspaperItem.put("url", processedUrl);
                        newspaperItem.put("fileUrl", "");
                        newspaperItem.put("article", new ArrayList<>()); // 空的article数组

                        newspaperList.add(newspaperItem);
                    } catch (Exception e) {
                        log.error("处理图片失败: " + imageUrl, e);
                    }
                }
            }

            return JSONObject.toJSONString(newspaperList);
        } catch (Exception e) {
            log.error("转换images到newspaper格式失败", e);
            return "[]";
        }
    }

    /**
     * 解析PHP数组格式的images字符串
     */
    private List<Map<String, Object>> parsePHPArrayImages(String imagesStr) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 简单的PHP数组解析逻辑
            if (imagesStr != null && imagesStr.contains("array")) {
                // 使用正则表达式提取URL
                Pattern urlPattern = Pattern.compile("'url'\\s*=>\\s*'([^']*)'");
                Pattern altPattern = Pattern.compile("'alt'\\s*=>\\s*'([^']*)'");

                Matcher urlMatcher = urlPattern.matcher(imagesStr);
                Matcher altMatcher = altPattern.matcher(imagesStr);

                while (urlMatcher.find()) {
                    Map<String, Object> imageInfo = new HashMap<>();
                    imageInfo.put("url", urlMatcher.group(1));

                    if (altMatcher.find()) {
                        imageInfo.put("alt", altMatcher.group(1));
                    } else {
                        imageInfo.put("alt", "");
                    }

                    result.add(imageInfo);
                }
            }
        } catch (Exception e) {
            log.error("解析PHP数组格式的images失败", e);
        }

        return result;
    }


    @Test
    public void moveVideosJava() {
        Map<Integer, Integer> map = new HashMap<>();
        // 添加您的栏目映射关系，格式：原栏目ID -> 目标栏目ID
        map.put(21, 31);
        // 添加更多映射关系...

        Integer id = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveVideosToStationVideoJava(originCatId, map.get(originCatId));
            } catch (Exception e) {
                log.error("moveVideosJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void moveVideosToStationVideoJava(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<TblCVideo> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的视频信息
        wrapper.eq(TblCVideo::getCatid, originCatId)
                .eq(TblCVideo::getState, 0)
                .eq(TblCVideo::getStatus, 99)
                .orderByAsc(TblCVideo::getListorder);
        List<TblCVideo> originVideos = tblCVideoMapper.selectList(wrapper);
        if (originVideos.isEmpty()) {
            return;
        }

        Map<StationMVideo, StationMVideoData> newVideoDataMap = new LinkedHashMap<>();
        for (TblCVideo originVideo : originVideos) {
            // 新的video
            StationMVideo stationMVideo = new StationMVideo();
            stationMVideo.setCatId(targetCatId);
            stationMVideo.setPublishUserId(1);
            stationMVideo.setCreateTime(Double.valueOf(originVideo.getInputtime() + "000"));
            stationMVideo.setUpdateTime(Double.valueOf(originVideo.getUpdatetime() + "000"));
            stationMVideo.setState(99);
            stationMVideo.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMVideo.setViews(originVideo.getViews());
            stationMVideo.setIsTop(originVideo.getIstop());
            stationMVideo.setIsLock(0);

            // 找到对应的videoData
            LambdaQueryWrapper<TblCVideoData> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TblCVideoData::getId, originVideo.getId());
            TblCVideoData originVideoData = tblCVideoDataMapper.selectOne(wrapper1);

            // 新的videoData
            StationMVideoData stationMVideoData = new StationMVideoData();
            stationMVideoData.setUuid(UUID.randomUUID().toString());
            stationMVideoData.setTitle(originVideo.getTitle());

            // 处理视频文件 - 构建完整的视频URL
            String videoFile = originVideo.getVideo();
            if (videoFile != null && !videoFile.isEmpty()) {
                // 假设视频文件存储在特定目录下，需要构建完整URL
                // 这里需要根据实际情况修改视频文件的基础路径
                String videoBaseUrl = "https://upload.lsz120.cn/video/"; // 请根据实际情况修改
                String fullVideoUrl = videoBaseUrl + videoFile;

                try {
                    // 下载并处理视频文件（如果需要迁移到新的存储服务器）
                    String downloadedVideo = CrawlerManager.DownLoad(fullVideoUrl, downloadPath, refer);
                    String processedVideoUrl = CrawlerManager.changeFileUrl(new File(downloadedVideo));
                    stationMVideoData.setVideo(processedVideoUrl);
                } catch (Exception e) {
                    log.error("处理视频文件失败: " + fullVideoUrl, e);
                    // 如果下载失败，可以直接使用原URL或跳过
                    stationMVideoData.setVideo(fullVideoUrl);
                }
            }

            // 处理缩略图
            String thumb = originVideo.getThumb();
            if (thumb != null && !thumb.isEmpty()) {
                try {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    stationMVideoData.setThumb(thumb);
                } catch (Exception e) {
                    log.error("处理缩略图失败: " + thumb, e);
                }
            }

            // 来源处理
            String comefrom = originVideo.getComefrom();
            if (comefrom != null && !comefrom.isEmpty()) {
                stationMVideoData.setComefrom(comefrom);
            }

            // 描述处理
            String description = originVideo.getDescription();
            if (description != null && !description.isEmpty()) {
                stationMVideoData.setDescription(description);
            }

            // 内容处理
            if (originVideoData != null && originVideoData.getContent() != null) {
                String content = changeSpecialChat(originVideoData.getContent());
                stationMVideoData.setContent(content);
            }

            // 处理发布时间
            stationMVideoData.setPublishTime(Double.valueOf(originVideo.getInputtime() + "000"));

            newVideoDataMap.put(stationMVideo, stationMVideoData);
        }

        // 切换到目标数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMVideo targetVideo : newVideoDataMap.keySet()) {
            StationMVideoData targetVideoData = newVideoDataMap.get(targetVideo);
            // 先加入资源数据
            stationMVideoDataMapper.insert(targetVideoData);
            // 引用数据引用资源数据id
            targetVideo.setDataId(targetVideoData.getDataId());
            // 加入引用数据
            stationMVideoMapper.insert(targetVideo);
            // 根据id更新排序
            targetVideo.setSortLevel(Math.toIntExact(targetVideo.getId()));
            stationMVideoMapper.updateById(targetVideo);
        }
    }




    @Test
    public void test() {
        String s = CrawlerManager.DownLoad("http://xbqrmyy.hms.org.cn/20220805/151600291.jpg", downloadPath, refer);
        String s1 = CrawlerManager.changeFileUrl(new File(s));
        log.info(s);
        log.info(s1);
        System.out.println(s);
    }

    @Test
    public void test2() {
        String a = "姓名：徐俊波&#305/br>\n" +
                "联合培养研究生学校：西南交通大学、川北医学院&#305/br>\n" +
                "当前指导研究生层次：硕士研究生&#305/br>\n" +
                "研究方向：高血压，心力衰竭等心血管病的药物器械治疗&#300医院管理&#305/br>\n" +
                "邮箱：<EMAIL>";
        a = changeSpecialChat(a);
        a = a.replaceAll("</br>", "");
        String[] split = a.split("\n");
        String name = "";
        String school = "";
        String level = "";
        String direction = "";
        String mail = "";
        for (String s : split) {
            if (s.contains("姓名")) {
                name = s.split("：")[1];
            } else if (s.contains("联合培养研究生学校")) {
                school = s.split("：")[1];
            } else if (s.contains("当前指导研究生层次")) {
                level = s.split("：")[1];
            } else if (s.contains("研究方向")) {
                direction = s.split("：")[1];
            } else if (s.contains("邮箱")) {
                mail = s.split("：")[1];
            }
        }
        System.out.println(name);
        System.out.println(school);
        System.out.println(level);
        System.out.println(direction);
        System.out.println(mail);
    }

    private Map<Integer, Integer> getDocPositionMap() {
        Map<Integer, Integer> titleLevelMap = new HashMap<>();

        // 医师系列
        titleLevelMap.put(99, 1);  // 主任医师
        titleLevelMap.put(89, 2);  // 副主任医师
        titleLevelMap.put(79, 3);  // 主治医师
        titleLevelMap.put(69, 12); // 医师
        titleLevelMap.put(68, 12); // 住院医师
        titleLevelMap.put(59, 13); // 医士

        // 心理咨询师系列
        titleLevelMap.put(96, 14); // 一级心理咨询师
        titleLevelMap.put(86, 15); // 二级心理咨询师
        titleLevelMap.put(76, 16); // 三级心理咨询师

        // 护师系列
        titleLevelMap.put(95, 6);  // 主任护师
        titleLevelMap.put(85, 7);  // 副主任护师
        titleLevelMap.put(75, 8);  // 主管护师
        titleLevelMap.put(65, 17); // 护师
        titleLevelMap.put(55, 18); // 护士

        // 技师系列
        titleLevelMap.put(94, 19); // 主任技师
        titleLevelMap.put(84, 20); // 副主任技师
        titleLevelMap.put(74, 21); // 主管技师
        titleLevelMap.put(64, 22); // 技师
        titleLevelMap.put(54, 23); // 技士

        // 药师系列
        titleLevelMap.put(93, 4);  // 主任药师
        titleLevelMap.put(83, 5);  // 副主任药师
        titleLevelMap.put(73, 24); // 主管药师
        titleLevelMap.put(63, 25); // 药师
        titleLevelMap.put(53, 26); // 药士

        return titleLevelMap;
    }
}
