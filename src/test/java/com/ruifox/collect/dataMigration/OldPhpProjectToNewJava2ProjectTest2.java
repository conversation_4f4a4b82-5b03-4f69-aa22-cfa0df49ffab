package com.ruifox.collect.dataMigration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.dao.mapper.tbl2.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.*;
import com.ruifox.collect.module.entity.tbl2.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class OldPhpProjectToNewJava2ProjectTest2 {
    @Autowired
    private StationCategoryMapper2 stationCategoryMapper;

    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private TblCVideoMapper2 tblCVideoMapper;
    @Autowired
    private TblCVideoDataMapper2 tblCVideoDataMapper;
    @Autowired
    private StationMVideoDataMapper2 stationMVideoDataMapper;



    //成都第五人民医院
    private String downloadPath = "C:\\Collect_Data\\cdd5";
    private String refer = "https://www.cd5120.cn/";


    // 导入视频
    @Test
    public void moveVideoJava() {
        Map<Integer, Integer> map = new HashMap<>();

        map.put(21,20);  //医院视频

        Integer id = 0;

        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveVideoJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveVideoJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("运行成功的任务ID有：");
        for (Integer i : success) {
            System.out.print(i+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start = end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    public void moveVideoJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCVideo2> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCVideo2::getCatid, originCatId)
                .eq(TblCVideo2::getState, 0)
                .eq(TblCVideo2::getStatus, 99)
                .orderByAsc(TblCVideo2::getListorder);
        List<TblCVideo2> originVideo = tblCVideoMapper.selectList(wrapper);
        if (originVideo.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 3;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMVideoData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCVideo2>> taskChunks = divideTasks(originVideo, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMVideoData2> chunkResult =
                            processTaskChunk3(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMVideoData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMVideoData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMVideoData2 targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetNew);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMVideoDataMapper.insert(targetNewData);
            //落库 -> folder_resource
            new FolderResource();
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetNewData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetNewData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetNew.setDataId(Long.valueOf(folderResource.getId()));
            targetNew.setSortLevel(Math.toIntExact(targetNew.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetNew);
        }
    }



    private Map<StationMReference, StationMVideoData2> processTaskChunk3(
            List<TblCVideo2> chunk, Integer targetCatId) {
        Map<StationMReference, StationMVideoData2> chunkResult = new LinkedHashMap<>();
        try {
            for (TblCVideo2 originNew : chunk) {
                // 新的article
                StationMReference stationMVideo = new StationMReference();
                stationMVideo.setCatId(targetCatId);
                stationMVideo.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));
                stationMVideo.setState(99);
                stationMVideo.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMVideo.setViews(originNew.getViews());
                stationMVideo.setIsTop(0);


                String linkUrl = originNew.getUrl();
                int isLink = 1;
                // TODO 根据url判断是否为外链(具体网站具体分析)
                if (linkUrl.contains("myzyy")) {
                    isLink = 0;
                }

                DynamicDataSource.changeOldPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblCVideoData2> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCVideoData2::getId, originNew.getId());
                TblCVideoData2 originVideoData = tblCVideoDataMapper.selectOne(wrapper1);

                // 新的articleData
                StationMVideoData2 stationMVideoData = new StationMVideoData2();
                stationMVideoData.setUuid(UUID.randomUUID().toString());
                stationMVideoData.setTitle(originNew.getTitle());
                stationMVideoData.setCreateUserId(1);
                stationMVideoData.setUpdateUserId(1);
                stationMVideoData.setCreateTime(Double.valueOf(originNew.getInputtime()+"000"));
                stationMVideoData.setUpdateTime(Double.valueOf(originNew.getUpdatetime()+"000"));
                stationMVideoData.setState(2);
                // 处理作者
//                String input = originNew.getAuthor();
                // TODO 根据实际处理
//                Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]+");
//                Matcher matcher = pattern.matcher(input);
//                StringBuilder author = new StringBuilder();
//                while (matcher.find()){
//                    author.append(matcher.group());
//                }
//                System.out.println(author);

//                author = changeSpecialChat(author);
//                if (!author.equals("0") && !author.toString().isEmpty())
////                    stationMVideoData.setAuthor(originVideoData.getAuthor());
//                    stationMVideoData.setAuthor(author.toString());

                // 缩略图处理 （只有一张图片）
                String thumb = originNew.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    if (!thumb.isBlank()){
                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    }
                }
                stationMVideoData.setThumb(thumb);

                // 处理正文,摘要
                String content = originVideoData.getContent();
                String description = originNew.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                    //没有头像就选择第一张图片为头像
                                    if (stationMVideoData.getThumb().isBlank()){
                                        stationMVideoData.setThumb(url);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originVideoData.getId());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originVideoData.getId());
                    }

                    // 视频路径集合处理
                    String video = "";
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    video = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                }
                                element.remove();
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originVideoData.getId());
                    }
                    stationMVideoData.setVideo(video);
                    content = document.toString();
                } else {
                    // 是链接的话,content为空
                    content = linkUrl;
                }
                //转义
                content = changeContent(content);

                stationMVideoData.setContent(content);
                stationMVideoData.setDescription(description);

                // 处理来源
                stationMVideoData.setComefrom(originNew.getComefrom());
                // 处理是否为外联
//                stationMVideoData.setIsLink(isLink);
                // 处理发布时间
//                stationMVideoData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

                chunkResult.put(stationMVideo, stationMVideoData);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }

        return chunkResult;
    }


    //手动转义
    public String changeContent(String text) {
        return text.replace("&quot;", "\"");
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }

    @Test
    public void testChangeChat(){
        String test = "&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302line-height:15.96px&#300&#302>我院现对下列项目征集相关市场调研资料，请符合相关项目要求且具有合法合格资质的公司或厂家积极参加本次调研。&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>一、调研项目清单：&#305span style&#304&#302color:#ff0000&#300&#302>详见调研报名网站&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>二、注意事项&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>1.请参与调研的供应商或厂家于&#305span style&#304&#302color:#ff0000&#300&#302>2025年7月29&#305/span>日前，按报名操作手册（见附件1）进行线上报名，并线上填写信息采集内容。请各供应商或厂家准确填写相关信息，并对所填信息的真实有效性负责,如发现所填报信息虚假，则此次调研报名作无效处理。&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>2.产品彩页&#305span style&#304&#302color:#ff0000&#300&#302>：上传彩页请根据挂网调研设备名称逐一命名，保持与挂网调研设备名称一致&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302color:#ff0000&#300&#302>3.产品技术参数表（附件2）：请下载公告附件3模板按要求如实填写，此表必须上传PDF盖章版以及可编辑的word版，共两种文档&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302color:#ff0000&#300&#302>4.市场同类产品参数对照表（附件3）：请下载公告附件4模板如实填写并上传&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>三、联系人&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>医学工程部 &nbsp&#300 沈老师 &nbsp&#300 028-62346182&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>四、报名方式&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>1、线上报名&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>2、报名链接：&#305a href&#304&#302https://mk.cd5120.com:28083/admin/login&#302 data_ue_src&#304&#302https://mk.cd5120.com:28083/admin/login&#302>&#305span style&#304&#302color:#0000ff&#300&#302>https://mk.cd5120.com:28083/admin/login&#305/span>&#305/a> &#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>3、报名操作手册：详见附件2&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305br />&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20250722/134226259.docx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20250722/134226259.docx&#302>调研项目清单.docx&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240130/101429228.pptx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240130/101429228.pptx&#302>附件1：供应商报名操作手册.pptx&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240521/104956502.doc&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240521/104956502.doc&#302>附件2：产品技术参数表.doc&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240521/104956616.xlsx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240521/104956616.xlsx&#302>附件3：市场同类产品参数对照表.xlsx&#305/a>&#305/p>&#305p>&#305br />&#305/p>";
        System.out.println(changeSpecialChat(test));
    }

    @Test
    public void test() {
        String Refer = "http://jrha.lan24.foxtest.net/";
        String s = CrawlerManager.DownLoad("http://jrha.1024199.foxtest.net/oss/20241203/115322689_2000_0_50.jpg", downloadPath, Refer);
        String s1 = CrawlerManager.changeFileUrl(new File(s));
        log.info(s);
        log.info(s1);
        System.out.println(s);
    }

    @Test
    public void test2() {
        String a = "姓名：徐俊波&#305/br>\n" +
                "联合培养研究生学校：西南交通大学、川北医学院&#305/br>\n" +
                "当前指导研究生层次：硕士研究生&#305/br>\n" +
                "研究方向：高血压，心力衰竭等心血管病的药物器械治疗&#300医院管理&#305/br>\n" +
                "邮箱：<EMAIL>";
        a = changeSpecialChat(a);
        a = a.replaceAll("</br>", "");
        String[] split = a.split("\n");
        String name = "";
        String school = "";
        String level = "";
        String direction = "";
        String mail = "";
        for (String s : split) {
            if (s.contains("姓名")) {
                name = s.split("：")[1];
            } else if (s.contains("联合培养研究生学校")) {
                school = s.split("：")[1];
            } else if (s.contains("当前指导研究生层次")) {
                level = s.split("：")[1];
            } else if (s.contains("研究方向")) {
                direction = s.split("：")[1];
            } else if (s.contains("邮箱")) {
                mail = s.split("：")[1];
            }
        }
        System.out.println(name);
        System.out.println(school);
        System.out.println(level);
        System.out.println(direction);
        System.out.println(mail);
    }

    public String leaveChinese(String input){
        input = input.replaceAll("[^\\u4e00-\\u9fa5,、；; （/）]", "");
        while (input.charAt(input.length()-1)==','||input.charAt(input.length()-1)=='、'||input.charAt(input.length()-1)=='；'||input.charAt(input.length()-1)==';'){
            input=input.substring(0,input.length()-1);
        }
        return input;
    }
    @Test
    public void testFilter(){
        String input = "array (\n" +
                "  0 => ' 陈静)',\n" +
                "  1 => ' ',\n" +
                "  2 => ' ',\n" +
                "  3 => ' ',\n" +
                "  4 => ' ',\n" +
                "  5 => ' ',\n" +
                "  6 => ' ',\n" +
                "  7 => ' ',\n" +
                "  8 => ' ',\n" +
                "  9 => ' ',\n" +
                "  10 => ' ',\n" +
                "  11 => ' ',\n" +
                "  12 => ' ',\n" +
                ")";
        String input2 = "array &#303\n" +
                "  0 &#304> &#301伍黎黎&#301,\n" +
                "  1 &#304> &#301刘小娟&#301,\n" +
                "  2 &#304> &#301江咏梅&#301,\n" +
                ")";
        String input3 = "&#301李知默&#301刘小娟&#301江咏梅、&#301刘小娟&#301";
        String input4 = "&#305p style&#304&#302text-align:left&#300text-indent:2em&#300&#302>&#305span style&#304&#302color:#333333&#300letter-spacing:0px&#300font-style:normal&#300background:#ffffff&#300&#302>&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302> &#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>又到粽叶飘香时，端午节应时而来。为进一步弘扬传统文化，丰富&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>退休&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>职工的&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>精神&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>文化生活&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>，传递组织关爱，&#305/span>2025年5月28日，医院人力资源部退休办公室代表医院组织举办了退休职工端午主题活动，本次活动共计参与人数200余人&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>医院共组织10余位志愿者为退休职工贴心服务，本次主题活动精心为退休职工准备了“书香阅读角”，发放《离退休工作简报》《枫华》《心系下一代》等相关杂志，让老师们了解国家、学校退休工作的新动向，分享退休生活心得，传递读书的喜悦。医院退休办公室精心编撰的《退休一本通》凝聚了医院的关爱，小礼品国风团扇也为退休老师们送来了缕缕夏日清风和对传统节日美好心思的寄托。&#305/p>&#305p style&#304&#302text-align:center&#302>&#305img src&#304&#302https://oss.motherchildren.com/20250529/154902303.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/154902303.png&#302 title&#304&#3025ddf839e6ad9a0d47798bad3fd2e193&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305img src&#304&#302https://oss.motherchildren.com/20250529/155013977.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155013977.png&#302 title&#304&#302986a08bef941191ead36debe4a2cbdd&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305/p>&#305p style&#304&#302text-align:center&#302>&#305img src&#304&#302https://oss.motherchildren.com/20250529/155014881.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155014881.png&#302 title&#304&#3025789ddb8722c7da3278e3e25c6cb5d8&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305img src&#304&#302https://oss.motherchildren.com/20250529/155013769.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155013769.png&#302 title&#304&#30289ca6a03218f59695317ea1cbbc910f&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>端午临仲夏，时清日复长。活动当天，老朋友、老同事们欢乐相聚拍照留念，或座谈共话安康，或读书细品书香。退休办公室张静主任代表医院领导向大家表达了医院对退休职工的关心关爱，祝福大家&#305span style&#304&#302color:#000000&#300&#302>老有所乐，共享幸福！并传达了近期&#305/span>学校、医院工作重点，及时让退休职工了解学校、医院建设发展的新动向。&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>退休老师们感谢医院组织端午主题活动，既传承了端午传统文化，又增进了老同志之间的交流与情谊，大家都深切感受到组织的温暖关怀，归属感和幸福感进一步提升，表示期待下一次的相聚！&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>&#305br style&#304&#302text-indent:2em&#300text-align:left&#300&#302 />&#305/p>";

        input = changeSpecialChat(input);
        input2 = changeSpecialChat(input2);
        input3 = changeSpecialChat(input3);
        input4 = changeSpecialChat(input4);
        // TODO 根据实际处理
//        input = leaveChinese(input);
//        input2 = leaveChinese(input2);
//        System.out.println(input);
//        System.out.println(input2);
//        System.out.println(input3);
        System.out.println(input4);
//        System.out.println(leaveChinese(input3));
    }

    private Map<Integer, Integer> getDocPositionMap() {
        Map<Integer, Integer> titleLevelMap = new HashMap<>();

        // 医师系列  TODO 根据实际调整
        titleLevelMap.put(20, 1);  // 主任医师
        titleLevelMap.put(16, 2);  // 副主任医师
        titleLevelMap.put(12, 5);  // 主治医师
        titleLevelMap.put(8, 15); // 医师
//        titleLevelMap.put(8, 12); // 住院医师
        titleLevelMap.put(4, 13); // 医士

        // 心理咨询师系列
        titleLevelMap.put(96, 14); // 一级心理咨询师
        titleLevelMap.put(86, 15); // 二级心理咨询师
        titleLevelMap.put(76, 16); // 三级心理咨询师

        // 护师系列
        titleLevelMap.put(19, 8);  // 主任护师
        titleLevelMap.put(15, 9);  // 副主任护师
        titleLevelMap.put(11, 10);  // 主管护师
        titleLevelMap.put(7, 17); // 护师
        titleLevelMap.put(3, 18); // 护士

        // 技师系列
        titleLevelMap.put(17, 0); // 主任技师
        titleLevelMap.put(13, 20); // 副主任技师
        titleLevelMap.put(9, 21); // 主管技师
        titleLevelMap.put(5, 22); // 技师
        titleLevelMap.put(1, 23); // 技士

        // 药师系列
        titleLevelMap.put(18, 6);  // 主任药师
        titleLevelMap.put(14, 7);  // 副主任药师
        titleLevelMap.put(10, 24); // 主管药师
        titleLevelMap.put(6, 25); // 药师
        titleLevelMap.put(2, 26); // 药士

        return titleLevelMap;
    }




    public void motherChildren(){
        //栏目1   新闻中心
//        map.put(10,22);   // 焦点新闻
//        map.put(13,23);   // 综合新闻
//        map.put(2341,24);   // 临床新闻
//        map.put(2342,25);   // 教学新闻
//        map.put(1846,26);   // 学术新闻
//        map.put(1991,27);   // 媒体聚焦


//        //栏目2    医院信息-招标前技术调研
//        map.put(2150,30);   // 专业设备物资及服务采购前市场调研  0
//        map.put(2151,31);   // 通用设备物资及服务采购前市场调研  0
//        map.put(2544,32);   // 信息类物资及服务采购市场调研  0
//        map.put(2152,33);   // 招标信息  0
//        map.put(1129,35);   // 招生信息  0
//        map.put(1130,36);   // 通知信息
//        map.put(12,37);   // 学术信息


//        //栏目3    视频中心
//        map.put(2091,44);   // 先进典型
//
//
//        //栏目4    就医指南
//        map.put(2308,11);   // 就诊重要通知
//        map.put(1829,14);   // 便民门诊
//
//
//        //栏目6    医联体
//        map.put(1488,92);   //最新动态
//
//
//        //栏目5    科室导航-院本部-党群部门-党委办公室
//        map.put(313,58);   // 部门动态
//        map.put(314,59);   // 规章制度
//        map.put(316,60);   // 通知通告
//        map.put(318,62);   // 重要文件
//        map.put(1965,63);   // 学习安排


        //        map.put(153,114);   // 星耀二院
//        map.put(154,115);   // 人文二院
//        map.put(156,116);   // 医暖二院

        //栏目7 特色医疗
//        map.put(1484,118);   // 特色医疗-儿科
//        map.put(1485,119);   // 特色医疗-妇产科
//      //栏目8 信息公开
//        map.put(36,123);   // 信息公开目录
//        map.put(37,124);   // 信息公开实施办法
//        map.put(40,126);   // 信息公开制度文件






        //栏目未创建(华西2)

//        map.put(94,58);   // 部门动态
//        map.put(248,61);   // 办事流程
//        map.put(250,58);   // 部门动态
//        map.put(253,61);   // 办事流程
//        map.put(255,58);   // 部门动态
//        map.put(258,61);   // 办事流程
//        map.put(260,58);   // 部门动态
//        map.put(263,61);   // 办事流程
//        map.put(265,58);   // 部门动态
//        map.put(270,58);   // 部门动态
//        map.put(275,58);   // 部门动态
//        map.put(288,61);   // 办事流程
//        map.put(290,58);   // 部门动态
//        map.put(295,58);   // 部门动态
//        map.put(300,58);   // 部门动态
//        map.put(303,59);   // 规章制度
//        map.put(305,58);   // 部门动态
//        map.put(310,58);   // 部门动态
//        map.put(322,58);   // 部门动态
//        map.put(326,61);   // 办事流程
//        map.put(328,60);   // 通知通告
//        map.put(331,58);   // 部门动态
//        map.put(333,63);   // 学习安排
//        map.put(335,61);   // 办事流程
//        map.put(340,58);   // 部门动态
//        map.put(349,58);   // 部门动态
//        map.put(353,61);   // 办事流程
//        map.put(369,61);   // 办事流程
//        map.put(383,61);   // 办事流程
//        map.put(385,58);   // 部门动态
//        map.put(392,58);   // 部门动态
//        map.put(399,58);   // 部门动态
//        map.put(433,58);   // 部门动态
//        map.put(663,58);   // 部门动态
//        map.put(670,58);   // 部门动态
//        map.put(677,58);   // 部门动态
//        map.put(693,61);   // 办事流程
//        map.put(852,61);   // 办事流程
//        map.put(880,61);   // 办事流程
//        map.put(882,58);   // 部门动态
//        map.put(969,61);   // 办事流程
//        map.put(1014,58);   // 部门动态
//        map.put(1025,60);   // 通知通告
//        map.put(1192,60);   // 通知通告
//        map.put(1193,60);   // 通知通告
//        map.put(1194,60);   // 通知通告
//        map.put(1200,60);   // 通知通告
//        map.put(1201,60);   // 通知通告
//        map.put(1202,60);   // 通知通告
//        map.put(1204,60);   // 通知通告
//        map.put(1205,60);   // 通知通告
//        map.put(1206,60);   // 通知通告
//        map.put(1207,60);   // 通知通告
//        map.put(1211,60);   // 通知通告
//        map.put(1214,60);   // 通知通告
//        map.put(1216,60);   // 通知通告
//        map.put(1218,60);   // 通知通告
//        map.put(1220,60);   // 通知通告
//        map.put(1226,60);   // 通知通告
//        map.put(1230,60);   // 通知通告
//        map.put(1234,60);   // 通知通告
//        map.put(1239,60);   // 通知通告
//        map.put(1244,60);   // 通知通告
//        map.put(1323,58);   // 部门动态
//        map.put(1324,61);   // 办事流程
//        map.put(1327,60);   // 通知通告
//        map.put(1334,58);   // 部门动态
//        map.put(1347,58);   // 部门动态
//        map.put(1348,33);   // 招标信息
//        map.put(1349,60);   // 通知通告
//        map.put(1356,58);   // 部门动态
//        map.put(1357,60);   // 通知通告
//        map.put(1643,60);   // 通知通告
//        map.put(1644,59);   // 规章制度
//        map.put(1732,23);   // 综合新闻
//        map.put(1864,58);   // 部门动态
//        map.put(1867,63);   // 学习安排
//        map.put(1869,60);   // 通知通告
//        map.put(1873,60);   // 通知通告
//        map.put(1874,60);   // 通知通告
//        map.put(1877,58);   // 部门动态
//        map.put(1878,60);   // 通知通告
//        map.put(1881,61);   // 办事流程
//        map.put(1893,58);   // 部门动态
//        map.put(1894,60);   // 通知通告
//        map.put(1895,61);   // 办事流程
//        map.put(2058,60);   // 通知通告
//        map.put(2113,60);   // 通知通告
//        map.put(2134,58);   // 部门动态
//        map.put(2153,33);   // 招标信息
//        map.put(2165,58);   // 部门动态
//        map.put(2222,59);   // 规章制度
//        map.put(2228,58);   // 部门动态
//        map.put(2266,58);   // 部门动态
//        map.put(2268,60);   // 通知通告
//        map.put(2275,58);   // 部门动态
//        map.put(2292,60);   // 通知通告
//        map.put(2335,58);   // 部门动态
//        map.put(2336,60);   // 通知通告
//        map.put(2360,58);   // 部门动态
//        map.put(2363,61);   // 办事流程
//        map.put(2371,60);   // 通知通告
//        map.put(4112,60);   // 通知通告
    }


    /**
     * @description:成都第五栏目
     * @author: RXH
     * @date: 2025/8/1 15:42
     * @param:
     * @return:
     **/
    public void cd5120(){
        //        map.put(18,17);   // 院务公开  0
//        map.put(2404,44);   // 伦理委员会动态  0
//        map.put(1538,40);   // 科研动态  0
//        map.put(55,36);   // 廉政建设  0
//        map.put(57,37);   // 青春风采  0
//        map.put(2389,20);   // 招贤纳士  0
//        map.put(1544,26);   // 患者须知  0
//        map.put(1567,38);   // 职工之家  0
//        map.put(2391,32);   // 体检套餐  0
//        map.put(2406,48);   // 机构动态
//        map.put(2426,39);   // 劳模风采
//        map.put(2540,6);   // 省政府信息
//        map.put(2550,57);   // 住培专栏
//        map.put(2551,411);   // 进修申请
//        map.put(2533,18);   // 招标采购
//        map.put(19,16);   // 医院动态
//        map.put(20,19);   // 媒体报道
//        getDepartNewsID(map);

//        //最后四个栏目:
//        map.put(2405,45);   //伦理委员会-文件制度
//        map.put(2263,49);   //国家药物临床试验机构-文件制度
//        map.put(2401,50);   //科研教学-文件制度
//        map.put(88,33);     //健康促进-健康科普
    }
}
