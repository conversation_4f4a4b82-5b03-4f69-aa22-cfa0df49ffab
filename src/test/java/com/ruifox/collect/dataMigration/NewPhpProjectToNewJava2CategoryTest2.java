package com.ruifox.collect.dataMigration;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.hms.HmsModelMapper;
import com.ruifox.collect.dao.mapper.station2.StationCategoryMapper2;
import com.ruifox.collect.dao.mapper.station2.StationModelMapper2;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.hms.HmsModel;
import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.module.entity.station2.StationModel2;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class NewPhpProjectToNewJava2CategoryTest2 {

    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;

    @Autowired
    private StationCategoryMapper2 stationCategoryMapper2;

    private ExecutorService executor;
    private int threadCount;

    // Model ID映射表：旧model_id -> 新model_id (根据用户提供的新旧model表对应关系)
    // convertModelId方法使用
    private static final Map<Integer, Integer> MODEL_ID_MAPPING = new HashMap<>();

    // 全局sort计数器和锁，确保sort值唯一性和线程安全（从8开始）
    private volatile int globalSortCounter = 8;
    private final Object sortLock = new Object();

    static {
        // 根据用户提供的SQL数据建立model_id映射关系
        MODEL_ID_MAPPING.put(0, 0);    // 保持不变
        MODEL_ID_MAPPING.put(1, 20);   // 文章: hms_model.id=1 -> model.id=20
        MODEL_ID_MAPPING.put(2, 19);   // 医生: hms_model.id=2 -> model.id=19  
        MODEL_ID_MAPPING.put(3, 30);   // 单页: hms_model.id=3 -> model.id=30
        MODEL_ID_MAPPING.put(5, 29);   // 领导: hms_model.id=5 -> model.id=29
        MODEL_ID_MAPPING.put(6, 41);   // 图集: hms_model.id=6 -> model.id=41
        MODEL_ID_MAPPING.put(8, 42);   // 院报: hms_model.id=8 -> model.id=42
        MODEL_ID_MAPPING.put(9, 54);   // 招标: hms_model.id=9 -> model.id=54
        MODEL_ID_MAPPING.put(10, 53);  // 下载: hms_model.id=10 -> model.id=53
        MODEL_ID_MAPPING.put(11, 40);  // 视频: hms_model.id=11 -> model.id=40
        // 注意：hms_model中的4(导师)、7(药剂)、12(招聘)在新系统中没有对应项
    }

    private static Map<Integer, Integer> MODEL_ID_MAPPING2 = new HashMap<>();
    @Autowired
    private HmsModelMapper hmsModelMapper;
    @Autowired
    private StationModelMapper2 stationModelMapper2;

    public void getNewIdToOldIdMap() {
        // 1. 获取旧模型的 Name -> ID 映射
        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<HmsModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HmsModel::getSiteId, 1);
        List<HmsModel> oldModels = hmsModelMapper.selectList(queryWrapper);

        // 创建从 name 到 id 的映射
        Map<String, Integer> oldNameToIdMap = oldModels.stream()
                .collect(Collectors.toMap(HmsModel::getName, hmsModel -> Math.toIntExact(hmsModel.getId()), (existing, replacement) -> existing));
        // (existing, replacement) -> existing 用于处理重名情况

        // 2. 获取新模型列表
        DynamicDataSource.changeResourceDynamicDataSource();
        List<StationModel2> newModels = stationModelMapper2.selectList(null); // 查询所有

        // 3. 创建 新ID -> 旧ID 的映射
        MODEL_ID_MAPPING2 = newModels.stream()
                .filter(newModel -> oldNameToIdMap.containsKey(newModel.getName())) // 只保留在新旧列表中都存在的
                .collect(Collectors.toMap(
                        StationModel2::getId, // Key: 新模型的ID
                        newModel -> oldNameToIdMap.get(newModel.getName()) // Value: 通过新模型名字找到的旧模型ID
                ));

        // 恢复数据源
         DynamicDataSource.changeDefaultDataSource();

    }


    @Before
    public void setUp() {
        // 根据CPU核心数动态配置线程数
        this.threadCount = Math.min(Runtime.getRuntime().availableProcessors(), 6);
        this.executor = Executors.newFixedThreadPool(threadCount);
        log.info("测试线程池初始化完成，线程数：{}", threadCount);
    }

    @PreDestroy
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 主要的栏目迁移测试方法
     */
    @Test
    public void migrateCategoryTest() {
        try {
            log.info("开始执行栏目数据迁移...");
//            获取栏目id的对应关系
//            getNewIdToOldIdMap();

            // 1. 切换到默认数据源，获取源数据
            DynamicDataSource.changeDefaultDataSource();

            List<HmsCategory> sourceCategories;
            LambdaQueryWrapper<HmsCategory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HmsCategory::getSiteId, 1)
                    .isNull(HmsCategory::getDeletedAt)
                    .orderByAsc(HmsCategory::getSort)
                    .orderByAsc(HmsCategory::getId);
            sourceCategories = hmsCategoryMapper.selectList(wrapper);

            if (sourceCategories.isEmpty()) {
                log.warn("没有找到需要迁移的栏目数据");
                return;
            }

            log.info("找到 {} 个栏目需要迁移", sourceCategories.size());

            // 1.2 处理url前缀
            fixUrlPrefixForChildren(sourceCategories);

            // 2. 处理旧数据库数据（ID从3开始）
            List<HmsCategory> processedCategories = processAndReassignIds(sourceCategories);

            // 3. 按层级排序（纯内存操作，不涉及数据库）
            List<HmsCategory> sortedCategories = processedCategories.stream()
                    .sorted(Comparator.comparing(HmsCategory::getLevel, Comparator.nullsFirst(Integer::compareTo))
                            .thenComparing(HmsCategory::getSort, Comparator.nullsFirst(Integer::compareTo)))
                    .collect(Collectors.toList());

            // 4. 按层级重新分配sort值（从3开始）
            List<HmsCategory> finalCategories = reassignSortByLevel(sortedCategories);

            // 5. 验证最终处理结果
            validateProcessedData(finalCategories);

            // 特殊处理：处理站点分类和删除特定栏目
            finalCategories = handleSiteClassificationAndDeletion(finalCategories);

            // 6. 切换到新Java2数据源，执行迁移
            DynamicDataSource.changeBuildDynamicDataSource();
            try {
                migrateCategories(finalCategories);
            } finally {
                DynamicDataSource.changeDefaultDataSource();
            }

            log.info("栏目数据迁移完成！");

        } catch (Exception e) {
            log.error("栏目数据迁移失败", e);
            throw e;
        }
    }

    //----------------------------------------- url特殊要求 -----------------------------------------------------

    /**
     * 处理·指定版本·子栏目uri前缀
     */
    private void fixUrlPrefixForChildren(List<HmsCategory> categories) {
        // 1. 找到公众版和员工/学生版顶级栏目
        Integer gzbRootId = null;
        Integer ygxsbRootId = null;
        for (HmsCategory c : categories) {
            if ("公众版".equals(c.getName())) gzbRootId = Math.toIntExact(c.getId());
            if ("员工/学生版".equals(c.getName())) ygxsbRootId = Math.toIntExact(c.getId());
        }
        // 2. 构建id->children映射
            Map<Integer, List<HmsCategory>> childrenMap = new HashMap<>();
        for (HmsCategory c : categories) {
            childrenMap.computeIfAbsent(c.getPid() == null ? 0 : c.getPid(), k -> new ArrayList<>()).add(c);
        }
        // 3. 递归收集所有子孙栏目
        Set<Integer> gzbChildren = collectAllDescendantIds(gzbRootId, childrenMap);
        Set<Integer> ygxsbChildren = collectAllDescendantIds(ygxsbRootId, childrenMap);

        // 4. 处理uri前缀
        for (HmsCategory c : categories) {
            // 将 gzbChildren 和 ygxsbChildren 的条件合并检查
            // 正确的代码
            if ((gzbChildren.contains(Math.toIntExact(c.getId())) || ygxsbChildren.contains(Math.toIntExact(c.getId()))) && c.getUrl() != null && c.getUrl().contains("_")) {
                String url = c.getUrl();

                // 1. 找到第一个下划线的位置
                int firstUnderscoreIndex = url.indexOf('_');

                // 2. 截取第一个下划线之后的所有字符
                //    例如 "gw_yygk_yyjj" -> "yygk_yyjj"
                String processedUrl = url.substring(firstUnderscoreIndex + 1);
                log.info("栏目[{}] url修正: {} -> {}", c.getName(), c.getUrl(), processedUrl);
                c.setUrl(processedUrl);
            }
        }
    }

    /**
     * 递归收集所有子孙栏目id
     */
    private Set<Integer> collectAllDescendantIds(Integer rootId, Map<Integer, List<HmsCategory>> childrenMap) {
        Set<Integer> result = new HashSet<>();
        if (rootId == null) return result;
        Queue<Integer> queue = new LinkedList<>();
        queue.add(rootId);
        while (!queue.isEmpty()) {
            Integer current = queue.poll();
            List<HmsCategory> children = childrenMap.get(current);
            if (children != null) {
                for (HmsCategory child : children) {
                    if (result.add(Math.toIntExact(child.getId()))) {
                        queue.add(Math.toIntExact(child.getId()));
                    }
                }
            }
        }
        return result;
    }


    // ------------------------------------------旧数据处理方法------------------------------------------------------

    /**
     * 处理并重新分配ID的综合方法
     * 1. 重新分配id（从8开始，连续递增）
     * 2. 更新pid、p_string、sort等关联字段
     */
    private List<HmsCategory> processAndReassignIds(List<HmsCategory> sourceCategories) {
        log.info("开始处理分类数据...");

        // 1. 按原始id排序
        List<HmsCategory> filteredCategories = sourceCategories.stream()
                .sorted(Comparator.comparing(HmsCategory::getId))
                .collect(Collectors.toList());

//        log.info("过滤后剩余 {} 条记录", filteredCategories.size());

        if (filteredCategories.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 创建id映射关系（旧id -> 新id，从8开始连续分配）
        Map<Long, Long> idMapping = new HashMap<>();
        idMapping.put(0L, 0L); // 添加0的映射，用于处理顶级分类

        Long newId = 8L; // 从3开始
        for (HmsCategory category : filteredCategories) {
            Long oldId = category.getId();
            idMapping.put(oldId, newId);
            log.debug("ID映射: {} -> {}", oldId, newId);
            newId++;
        }

        log.info("创建id映射关系完成，共 {} 个映射（不含0）", idMapping.size() - 1);

        // 3. 处理每条记录
        List<HmsCategory> processedCategories = new ArrayList<>();

        for (HmsCategory oldCategory : filteredCategories) {
            try {
                // 创建新对象并复制属性
                HmsCategory newCategory = new HmsCategory();
                BeanUtil.copyProperties(oldCategory, newCategory);

                Long oldId = oldCategory.getId();
                Long newMappedId = idMapping.get(oldId);

                // 更新id
                newCategory.setId(newMappedId);

                // 保持原sort值，用于后续按层级排序（不在这里分配新sort值）
                newCategory.setSort(oldCategory.getSort());

                // 更新pid
                Integer oldPid = oldCategory.getPid();
                if (oldPid == null || oldPid == 0) {
                    newCategory.setPid(0); // 顶级分类
                } else {
                    Long newPid = idMapping.get(Long.valueOf(oldPid));
                    if (newPid != null) {
                        newCategory.setPid(newPid.intValue());
                    } else {
                        // 父级不在过滤后的数据中（可能是引用了其他site_id的数据），设为顶级
                        newCategory.setPid(0);
                        log.warn("记录 '{}' (原id={}) 的父级id={} 不存在于当前数据集中，设置为顶级",
                                newCategory.getName(), oldId, oldPid);
                    }
                }

                // 更新p_string路径
                String newPString = rebuildPString(oldCategory.getPString(), idMapping);
                newCategory.setPString(newPString);

                processedCategories.add(newCategory);

                log.debug("处理记录完成: '{}' 原id={} -> 新id={}, 新pid={}, 新p_string={}",
                        newCategory.getName(), oldId, newMappedId,
                        newCategory.getPid(), newCategory.getPString());

            } catch (Exception e) {
                log.error("处理记录失败: id={}, name={}", oldCategory.getId(), oldCategory.getName(), e);
                throw new RuntimeException("处理记录失败", e);
            }
        }

        log.info("分类数据处理完成，共处理 {} 条记录", processedCategories.size());
        return processedCategories;
    }

    /**
     * 重建p_string路径
     */
    private String rebuildPString(String oldPString, Map<Long, Long> idMapping) {
        if (StrUtil.isBlank(oldPString)) {
            return "0,";
        }

        // 解析原路径，格式如: "0,111,119,147,"
        String[] pathIds = oldPString.split(",");
        List<String> newPathIds = new ArrayList<>();

        for (String pathIdStr : pathIds) {
            if (StrUtil.isNotBlank(pathIdStr)) {
                try {
                    Long pathId = Long.valueOf(pathIdStr.trim());

                    if (pathId == 0) {
                        newPathIds.add("0");
                    } else {
                        Long newPathId = idMapping.get(pathId);
                        if (newPathId != null) {
                            newPathIds.add(newPathId.toString());
                        } else {
                            log.warn("路径中的id={} 不存在于映射中，可能是引用了其他数据，跳过", pathId);
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的路径ID: '{}'", pathIdStr);
                }
            }
        }

        // 确保至少有"0"
        if (newPathIds.isEmpty() || !newPathIds.contains("0")) {
            newPathIds.add(0, "0");
        }

        // 重建路径字符串，末尾加逗号
        return String.join(",", newPathIds) + ",";
    }

    /**
     * 验证处理结果
     */
    private void validateProcessedData(List<HmsCategory> categories) {
        Set<Long> idSet = new HashSet<>();
        Set<Integer> sortSet = new HashSet<>();
        int duplicateIdCount = 0;
        int duplicateSortCount = 0;
        int invalidPStringCount = 0;
        int invalidIdCount = 0;
        int invalidSortCount = 0;

        for (HmsCategory category : categories) {
            // 检查重复id
            if (!idSet.add(category.getId())) {
                duplicateIdCount++;
                log.error("发现重复的id: {}, name={}", category.getId(), category.getName());
            }

            // 检查重复sort
            if (!sortSet.add(category.getSort())) {
                duplicateSortCount++;
                log.error("发现重复的sort: {}, id={}, name={}",
                        category.getSort(), category.getId(), category.getName());
            }

            // 验证id从3开始
            if (category.getId() < 8) {
                invalidIdCount++;
                log.error("新id不符合要求（应从8开始）: id={}, name={}",
                        category.getId(), category.getName());
            }

            // 验证sort从3开始
            if (category.getSort() < 8) {
                invalidSortCount++;
                log.error("新sort不符合要求（应从8开始）: sort={}, id={}, name={}",
                        category.getSort(), category.getId(), category.getName());
            }

            // 验证p_string格式（应该是"数字,数字,...数字,"的格式）
            String pString = category.getPString();
            if (StrUtil.isNotBlank(pString)) {
                if (!pString.matches("^(\\d+,)+$")) {
                    invalidPStringCount++;
                    log.error("p_string格式不正确: id={}, name={}, p_string='{}'",
                            category.getId(), category.getName(), pString);
                }

                // 验证p_string中的id都存在
                String[] pathIds = pString.split(",");
                for (String pathIdStr : pathIds) {
                    if (StrUtil.isNotBlank(pathIdStr) && !"0".equals(pathIdStr)) {
                        Long pathId = Long.valueOf(pathIdStr);
                        boolean exists = categories.stream()
                                .anyMatch(c -> c.getId().equals(pathId));
                        if (!exists && pathId >= 8) { // 排除0和小于8的id
                            log.warn("p_string中引用了不存在的id: {}, 在记录 id={}, name={} 中",
                                    pathId, category.getId(), category.getName());
                        }
                    }
                }
            }
        }

        if (duplicateIdCount == 0 && duplicateSortCount == 0 &&
                invalidPStringCount == 0 && invalidIdCount == 0 && invalidSortCount == 0) {
            log.info("数据验证通过：无重复id和sort，p_string格式正确，id和sort都从3开始");
        } else {
            String errorMsg = String.format(
                    "数据验证失败：重复id=%d，重复sort=%d，p_string格式错误=%d，无效id=%d，无效sort=%d",
                    duplicateIdCount, duplicateSortCount, invalidPStringCount, invalidIdCount, invalidSortCount);
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    /**
     * 按层级重新分配sort值
     * 1. 按层级分组
     * 2. 同层级内按原sort值排序
     * 3. 从层级1开始，重新分配连续的sort值（从3开始，与ID保持一致）
     */
    private List<HmsCategory> reassignSortByLevel(List<HmsCategory> categories) {
        log.info("开始重新分配sort值，当前数据量：{}", categories.size());

        // 重置全局sort计数器（从8开始，与ID分配保持一致）
        synchronized (sortLock) {
            globalSortCounter = 8;
        }

        // 按层级分组
        Map<Integer, List<HmsCategory>> levelGroups = categories.stream()
                .collect(Collectors.groupingBy(
                        category -> category.getLevel() != null ? category.getLevel() : 0
                ));

        // 按层级顺序处理（层级从1开始）
        List<HmsCategory> sortedCategories = new ArrayList<>();
        List<Integer> sortedLevels = levelGroups.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        for (Integer level : sortedLevels) {
            List<HmsCategory> levelCategories = levelGroups.get(level);

            // 同层级内按原sort值排序
            levelCategories.sort(Comparator.comparing(
                    category -> category.getSort() != null ? category.getSort() : 0
            ));

            // 为当前层级的所有分类分配新的sort值
            synchronized (sortLock) {
                for (HmsCategory category : levelCategories) {
                    int oldSort = category.getSort() != null ? category.getSort() : 0;
                    category.setSort(globalSortCounter);

                    log.debug("层级 {} - 分类 '{}': 原sort={} -> 新sort={}",
                            level, category.getName(), oldSort, globalSortCounter);

                    globalSortCounter++;
                }
            }

            sortedCategories.addAll(levelCategories);
            log.info("层级 {} 处理完成，包含 {} 个分类，当前全局sort计数器：{}",
                    level, levelCategories.size(), globalSortCounter - 1);
        }

        log.info("sort值重新分配完成，总计 {} 个分类，最终sort范围：8-{}",
                sortedCategories.size(), globalSortCounter - 1);

        return sortedCategories;
    }

    // --------------------------------------------多线程迁移方法-------------------------------------------------------

    /**
     * 执行栏目迁移（多线程处理）
     */
    private void migrateCategories(List<HmsCategory> categories) {
        // 分批处理
        List<List<HmsCategory>> batches = divideTasks(categories, threadCount);

        // 创建 CompletableFuture 列表
        List<CompletableFuture<List<StationCategory2>>> futures = new ArrayList<>();

        // 提交任务到线程池
        for (int i = 0; i < batches.size(); i++) {
            final int batchIndex = i;
            final List<HmsCategory> batch = batches.get(i);

            CompletableFuture<List<StationCategory2>> future = CompletableFuture
                    .supplyAsync(() -> {
                        log.info("开始处理批次 {}, 包含 {} 个栏目", batchIndex + 1, batch.size());
                        return processCategoryBatch(batch);
                    }, executor)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            log.error("批次 {} 处理失败", batchIndex + 1, throwable);
                        } else {
                            log.info("批次 {} 处理完成", batchIndex + 1);
                        }
                    });

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        try {
            List<StationCategory2> results = futures.stream()
                    .map(CompletableFuture::join)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            log.info("所有批次处理完成，共迁移 {} 个栏目", results.size());
        } catch (CompletionException e) {
            log.error("任务执行过程中发生异常", e.getCause());
            throw e;
        }
    }

    /**
     * 处理单个批次的栏目数据
     */
    private List<StationCategory2> processCategoryBatch(List<HmsCategory> batch) {
        List<StationCategory2> batchResults = new ArrayList<>();

        try {
            // 重要：在子线程中重新设置数据源（解决ThreadLocal问题）
            DynamicDataSource.changeBuildDynamicDataSource();

            for (HmsCategory sourceCategory : batch) {
                try {
                    StationCategory2 targetCategory = convertCategory(sourceCategory);
                    if (targetCategory != null) {
                        // 插入到目标数据源
                        stationCategoryMapper2.insert(targetCategory);
                        batchResults.add(targetCategory);
                        log.debug("成功迁移栏目：{} -> {}", sourceCategory.getName(), targetCategory.getId());
                    }
                } catch (Exception e) {
                    log.error("迁移栏目失败：{}", sourceCategory.getName(), e);
                }
            }
        } finally {
            // 恢复默认数据源（可选）
            DynamicDataSource.changeDefaultDataSource();
        }

        return batchResults;
    }

    /**
     * 将任务列表分割成多个批次
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int batchCount) {
        List<List<T>> batches = new ArrayList<>();
        int taskCount = tasks.size();
        int batchSize = Math.max(1, (taskCount + batchCount - 1) / batchCount);

        for (int i = 0; i < taskCount; i += batchSize) {
            int end = Math.min(i + batchSize, taskCount);
            batches.add(tasks.subList(i, end));
        }

        return batches;
    }

    // -------------------------------------迁移数据转换方法-------------------------------------------

    /**
     * 转换栏目数据格式
     * 完全重写的字段映射逻辑，按照新旧数据库字段对应关系
     */
    private StationCategory2 convertCategory(HmsCategory source) {
        if (source == null) {
            return null;
        }

        StationCategory2 target = new StationCategory2();

        // 1. 一比一迁移字段
        // id: Long → Integer 转换（安全转换）
        target.setId(safeLongToInteger(source.getId()));

        // site_id: int → int
        target.setSiteId(source.getSiteId());

        // pid: int → int
        target.setPid(source.getPid());

        // level: int → int
        target.setLevel(source.getLevel());

        // sort → sort_level: int → int
        target.setSortLevel(source.getSort());

        // name: String → String
        target.setName(source.getName());

        // type: int → int
        target.setType(source.getType());

        // info: String → String (一比一复制)
        target.setInfo(source.getInfo());

        // model_id: Integer → Integer (通过映射表转换)
        target.setModelId(convertModelId(source.getModelId()));

        // is_show: Integer → Integer (一比一复制)
        target.setIsShow(source.getIsShow());

        // is_enable: 0 -> 1,1 -> 0
        target.setState(source.getIsEnable() ^ 1);

        // 2. 格式转换字段
        // p_string → parent_string: "0,111,119," → "[0,111,119]"
        String parentString = convertPStringToParentString(source.getPString());
        target.setParentString(parentString);

        // url → uri: "yygk_yyjj" → "/yygk_yyjj"
        String uri = convertUrlToUri(source.getUrl());
        target.setUri(uri);

        // 3. 时间字段设置
        // create_time和update_time：设置为当前时间戳(Double类型)
        Double currentTimestamp = (double) System.currentTimeMillis();
        target.setCreateTime(currentTimestamp);
        target.setUpdateTime(currentTimestamp);

        return target;
    }

    /**
     * 转换p_string格式到parent_string格式
     * 从 "0,111,119," 转换为 "[0,111,119]"
     */
    private String convertPStringToParentString(String pString) {
        if (StrUtil.isBlank(pString)) {
            return "[0]";  // 默认值
        }

        // 移除末尾的逗号，然后用[]包裹
        String trimmed = pString.trim();
        if (trimmed.endsWith(",")) {
            trimmed = trimmed.substring(0, trimmed.length() - 1);
        }

        return "[" + trimmed + "]";
    }

    /**
     * 转换url格式到uri格式
     * 在原url前面加上"/"
     */
    private String convertUrlToUri(String url) {
        if (StrUtil.isBlank(url)) {
            return "/";  // 默认值
        }

        // 如果已经以"/"开头，直接返回
        if (url.startsWith("/")) {
            return url;
        }

        // 在前面加上"/"
        return "/" + url;
    }

    /**
     * 转换model_id
     * 通过映射表将旧model_id转换为新model_id
     */
    private Integer convertModelId(Integer oldModelId) {
        if (oldModelId == null) {
            return 0; // 返回默认值0而不是null
        }

        // 通过映射表转换
        Integer newModelId = MODEL_ID_MAPPING.get(oldModelId);
        if (newModelId != null) {
            return newModelId;
        }

        // 如果映射表中没有对应关系，记录警告并返回默认值0
        log.warn("未找到model_id映射: oldModelId={}, 使用默认值0", oldModelId);
        return 0;
    }

    /**
     * 安全的Long到Integer转换
     * 防止溢出问题
     */
    private Integer safeLongToInteger(Long longValue) {
        if (longValue == null) {
            return null;
        }

        // 检查是否超出Integer范围
        if (longValue > Integer.MAX_VALUE || longValue < Integer.MIN_VALUE) {
            log.error("Long值超出Integer范围，发生溢出: {}, 最大值: {}, 最小值: {}",
                    longValue, Integer.MAX_VALUE, Integer.MIN_VALUE);
            throw new IllegalArgumentException("Long值超出Integer范围: " + longValue);
        }

        return longValue.intValue();
    }

    // ------------------------------------特殊处理方法--------------------------------------

    /**
     * 处理站点分类和删除特定栏目的复杂逻辑
     * 1. 根据name设置site_id
     * 2. 删除"院庆版"、"老年版"、"安全生产版"栏目
     * 3. 调整被删除栏目的子项目：pid改为0，p_string删除相关id，level减1
     */
    private List<HmsCategory> handleSiteClassificationAndDeletion(List<HmsCategory> categories) {
        log.info("开始执行站点分类和栏目删除的特殊处理，当前数据量：{}", categories.size());

        // 第一步：根据name设置site_id
        setSiteIdByName(categories);

        // 第二步：找到需要删除的栏目
        Set<Long> toDeleteIds = findCategoriesToDelete(categories);

        // 第三步：调整被删除栏目的子项目
        adjustChildrenOfDeletedCategories(categories, toDeleteIds);

        // 第四步：从列表中移除被删除的栏目
        List<HmsCategory> filteredCategories = categories.stream()
                .filter(category -> !toDeleteIds.contains(category.getId()))
                .collect(Collectors.toList());

        log.info("特殊处理完成，删除了 {} 个栏目，剩余数据量：{}",
                toDeleteIds.size(), filteredCategories.size());

        return filteredCategories;
    }

    /**
     * 根据栏目名称设置site_id
     */
    private void setSiteIdByName(List<HmsCategory> categories) {
        log.info("开始根据栏目名称设置site_id...");

        // 找到顶级栏目并建立映射关系
        Map<String, Integer> siteMapping = new HashMap<>();
        siteMapping.put("公众版", 1);
        siteMapping.put("员工/学生版", 2);
        siteMapping.put("ENGLISH", 3);

        // 建立栏目层级关系映射，用于向下传播site_id
        Map<Long, List<HmsCategory>> childrenMap = new HashMap<>();
        for (HmsCategory category : categories) {
            Long pid = Long.valueOf(category.getPid() != null ? category.getPid() : 0);
            childrenMap.computeIfAbsent(pid, k -> new ArrayList<>()).add(category);
        }

        // 遍历所有栏目，设置site_id
        for (HmsCategory category : categories) {
            String name = category.getName();
            if (name != null && siteMapping.containsKey(name)) {
                // 这是顶级站点栏目，设置对应的site_id
                Integer siteId = siteMapping.get(name);
                category.setSiteId(siteId);
                log.info("设置顶级栏目 '{}' 的site_id为 {}", name, siteId);

                // 递归设置所有子栏目的site_id
                propagateSiteId(category, siteId, childrenMap);
            } else if (category.getSiteId() == null || category.getSiteId() == 0) {
                // 如果还没有设置site_id，设置为默认值1（公众版）
                category.setSiteId(1);
            }
        }

        log.info("site_id设置完成");
    }

    /**
     * 递归向下传播site_id到所有子栏目
     */
    private void propagateSiteId(HmsCategory parent, Integer siteId, Map<Long, List<HmsCategory>> childrenMap) {
        List<HmsCategory> children = childrenMap.get(parent.getId());
        if (children != null) {
            for (HmsCategory child : children) {
                child.setSiteId(siteId);
                log.debug("设置子栏目 '{}' 的site_id为 {}", child.getName(), siteId);
                // 递归处理子栏目的子栏目
                propagateSiteId(child, siteId, childrenMap);
            }
        }
    }

    /**
     * 找到需要删除的栏目ID
     */
    private Set<Long> findCategoriesToDelete(List<HmsCategory> categories) {
        Set<Long> toDeleteIds = new HashSet<>();
        Set<String> deleteNames = Set.of("公众版", "员工/学生版", "ENGLISH");

        for (HmsCategory category : categories) {
            String name = category.getName();
            if (name != null && deleteNames.contains(name)) {
                toDeleteIds.add(category.getId());
                log.info("标记删除栏目：id={}, name='{}'", category.getId(), name);
            }
        }

        log.info("共找到 {} 个需要删除的栏目", toDeleteIds.size());
        return toDeleteIds;
    }

    /**
     * 调整被删除栏目的子项目
     */
    private void adjustChildrenOfDeletedCategories(List<HmsCategory> categories, Set<Long> toDeleteIds) {
        log.info("开始调整被删除栏目的子项目...");

        AtomicInteger adjustedCount = new AtomicInteger(0);
        for (HmsCategory category : categories) {
            // 跳过要删除的栏目本身
            if (toDeleteIds.contains(category.getId())) {
                continue;
            }

            boolean needAdjust = false;

            // 检查pid是否指向被删除的栏目
            if (category.getPid() != null && toDeleteIds.contains(Long.valueOf(category.getPid()))) {
                log.info("调整栏目 '{}' (id={}) 的pid从 {} 改为 0",
                        category.getName(), category.getId(), category.getPid());
                category.setPid(0);
                needAdjust = true;
            }

            // 检查并调整p_string
            String originalPString = category.getPString();
            String adjustedPString = adjustPString(originalPString, toDeleteIds);
            if (!Objects.equals(originalPString, adjustedPString)) {
                category.setPString(adjustedPString);
                log.debug("调整栏目 '{}' (id={}) 的p_string从 '{}' 改为 '{}'",
                        category.getName(), category.getId(), originalPString, adjustedPString);
                needAdjust = true;
            }

            // 调整level（如果p_string发生了变化，说明层级结构改变了）
            if (needAdjust && category.getLevel() != null && category.getLevel() > 1) {
                int oldLevel = category.getLevel();
                category.setLevel(oldLevel - 1);
                log.debug("调整栏目 '{}' (id={}) 的level从 {} 改为 {}",
                        category.getName(), category.getId(), oldLevel, category.getLevel());
            }

            if (needAdjust) {
                adjustedCount.incrementAndGet();
            }
        }

        log.info("子项目调整完成，共调整了 {} 个栏目", adjustedCount);
    }

    /**
     * 调整p_string，删除其中被删除栏目的ID
     */
    private String adjustPString(String pString, Set<Long> toDeleteIds) {
        if (StrUtil.isBlank(pString)) {
            return pString;
        }

        // 解析p_string，格式如: "0,111,119,147,"
        String[] pathIds = pString.split(",");
        List<String> newPathIds = new ArrayList<>();

        for (String pathIdStr : pathIds) {
            if (StrUtil.isNotBlank(pathIdStr)) {
                try {
                    Long pathId = Long.valueOf(pathIdStr.trim());
                    // 如果这个ID不在删除列表中，保留它
                    if (!toDeleteIds.contains(pathId)) {
                        newPathIds.add(pathIdStr.trim());
                    }
                } catch (NumberFormatException e) {
                    // 保留无效的路径ID（虽然理论上不应该出现）
                    newPathIds.add(pathIdStr.trim());
                }
            }
        }

        // 确保至少有"0"
        if (newPathIds.isEmpty() || !newPathIds.contains("0")) {
            newPathIds.add(0, "0");
        }

        // 重建路径字符串，末尾加逗号
        return String.join(",", newPathIds) + ",";
    }
}
