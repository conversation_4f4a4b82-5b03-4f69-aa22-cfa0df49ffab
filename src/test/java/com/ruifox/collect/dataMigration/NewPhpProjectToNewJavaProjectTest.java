package com.ruifox.collect.dataMigration;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class NewPhpProjectToNewJavaProjectTest {
    @Autowired
    private HmsCNewsMapper hmsCNewsMapper;
    @Autowired
    private HmsCNewsDataMapper hmsCNewsDataMapper;
    @Autowired
    private HmsCMansMapper hmsCMansMapper;
    @Autowired
    private HmsCImageMapper hmsCImageMapper;
    @Autowired
    private HmsCImageDataMapper hmsCImageDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Autowired
    private StationMImageDataMapper stationMImageDataMapper;
    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;
    @Autowired
    private HmsCMansDataMapper hmsCMansDataMapper;
    @Autowired
    private StationMArticleMapper stationMArticleMapper;
    @Autowired
    private StationMArticleDataMapper stationMArticleDataMapper;
    @Autowired
    private StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    private StationMDoctorDataMapper stationMDoctorDataMapper;
    @Autowired
    private StationCategoryMapper stationCategoryMapper;

    //下载时的来源页面
    private static String refer = "http://lsz120.web24.foxtest.net/";
    //下载文件存储地址
    private static String downloadPath = "C:\\Collect_Data\\lsz120";

    @Test
    public void moveNewsJava() {
        Map<Integer, Integer> map = new HashMap<>();
        map.put(465,119);
        Integer id = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<HmsCNews> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息与判断是否为发布状态
        wrapper.eq(HmsCNews::getCatid, originCatId)
                .eq(HmsCNews::getState, 0)
                .eq(HmsCNews::getStatus, 99)
                .orderByAsc(HmsCNews::getListorder);


        List<HmsCNews> originNews = hmsCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMArticle, StationMArticleData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCNews>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMArticle, StationMArticleData> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMArticle, StationMArticleData> mergedMap = new LinkedHashMap<>();
        for (Map<StationMArticle, StationMArticleData> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 切换到java的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMArticle targetNew : mergedMap.keySet()) {
            StationMArticleData targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            stationMArticleDataMapper.insert(targetNewData);
            // 引用数据引用资源数据id
            targetNew.setDataId(targetNewData.getDataId());
            // 加入引用数据
            stationMArticleMapper.insert(targetNew);
            // 根据id更新排序
            targetNew.setSortLevel(targetNew.getId());
            stationMArticleMapper.updateById(targetNew);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMArticle, StationMArticleData> processTaskChunk(
            List<HmsCNews> chunk, Integer targetCatId) {
        Map<StationMArticle, StationMArticleData> chunkResult = new LinkedHashMap<>();
        try {
            for (HmsCNews originNew : chunk) {
                // 新的news
                StationMArticle stationMArticle = new StationMArticle();
                stationMArticle.setCatId(targetCatId);
                stationMArticle.setPublishUserId(1);
                stationMArticle.setCreateTime(Double.valueOf(originNew.getInputTime() + "000"));
                stationMArticle.setUpdateTime(Double.valueOf(originNew.getUpdateTime() + "000"));
                stationMArticle.setState(99);
                stationMArticle.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMArticle.setIsTop(0);
                stationMArticle.setIsLock(0);

                String linkUrl = originNew.getIslink();
                int isLink = 0;
                if (linkUrl!=null && !linkUrl.isEmpty()) {
                    isLink = 1;
                }

                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<HmsCNewsData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCNewsData::getDid, originNew.getDataid());
                HmsCNewsData originNewsData = hmsCNewsDataMapper.selectOne(wrapper1);

                //处理访问量
                stationMArticle.setViews(originNewsData.getViews());
                // 新的articleData
                StationMArticleData stationMArticleData = new StationMArticleData();
                stationMArticleData.setUuid(UUID.randomUUID().toString());
                stationMArticleData.setTitle(originNewsData.getTitle());
                // 处理作者
                String author = originNewsData.getAuthor();
                author = changeSpecialChat(author);
                if (!author.equals("0") && !author.isEmpty())
                    stationMArticleData.setAuthor(originNewsData.getAuthor());
                if(author.equals("[]")){
                    stationMArticleData.setAuthor("");
                }
                // 缩略图处理 （只有一张图片）
                String thumb = originNewsData.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMArticleData.setThumb(thumb);
                // 处理正文,摘要
                String content = originNewsData.getContent();
                String description = originNewsData.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getDid());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getDid());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, "https://www.cd3hospital.com/");
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getDid());
                    }
                    content = document.body().children().toString();
                } else {
                    // 是链接的话,content为链接
                    content = linkUrl;
                    description = linkUrl;
                }
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                //TODO 处理来源
                stationMArticleData.setComefrom(originNewsData.getComefrom());
                //TODO 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                //TODO 处理发布时间
                stationMArticleData.setPublishTime(Double.valueOf(originNew.getPublishTime() + "000"));

                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }


        return chunkResult;
    }

    @Test
    public void moveDoctorJava() {
        Integer originCatId = 3;
        Integer targetCatId = 12;

        LambdaQueryWrapper<HmsCMans> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(HmsCMans::getCatid, originCatId)
                .eq(HmsCMans::getState, 0)
                .eq(HmsCMans::getStatus, 99)
                .orderByAsc(HmsCMans::getListorder)
                .orderByDesc(HmsCMans::getSort);
        List<HmsCMans> originMans = hmsCMansMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 6;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMDoctor, StationMDoctorData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCMans>> taskChunks = divideTasks(originMans, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMDoctor, StationMDoctorData> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMDoctor, StationMDoctorData> mergedMap = new LinkedHashMap<>();
        for (Map<StationMDoctor, StationMDoctorData> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMDoctor targetMan : mergedMap.keySet()) {
            StationMDoctorData targetManData = mergedMap.get(targetMan);
            // 先加入资源数据
            stationMDoctorDataMapper.insert(targetManData);
            // 引用数据引用资源数据id
            targetMan.setDataId(targetManData.getDataId());
            // 加入引用数据
            stationMDoctorMapper.insert(targetMan);
            // 根据id更新排序
            targetMan.setSortLevel(targetMan.getId());
            stationMDoctorMapper.updateById(targetMan);
        }
    }

    private Map<StationMDoctor, StationMDoctorData> processTaskChunk2(
            List<HmsCMans> chunk, Integer targetCatId) {
        Map<StationMDoctor, StationMDoctorData> chunkResult = new LinkedHashMap<>();

        List<HmsCategory> HmsCategories =hmsCategoryMapper.selectList(null);
        Map<Integer, String> catMap = new HashMap<>();
        for (HmsCategory hmsCategory : HmsCategories ) {
            catMap.put(hmsCategory.getId().intValue(), hmsCategory.getName());
        }

        for (HmsCMans originMan : chunk) {
            DynamicDataSource.changeDefaultDataSource();
            // 新的doctor
            StationMDoctor stationMDoctor = new StationMDoctor();
            stationMDoctor.setCatId(targetCatId);
            stationMDoctor.setPublishUserId(1);
            stationMDoctor.setCreateTime(Double.valueOf(originMan.getInputTime() + "000"));
            stationMDoctor.setUpdateTime(Double.valueOf(originMan.getUpdateTime() + "000"));
            stationMDoctor.setState(99);
            stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMDoctor.setIsTop(0);
            stationMDoctor.setIsLock(0);

            // 找到修改前的manData并修改后存入新的doctorData
            LambdaQueryWrapper<HmsCMansData> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(HmsCMansData::getDid, originMan.getDataid());
            HmsCMansData originManData = hmsCMansDataMapper.selectOne(wrapper1);

            // 新的articleData
            StationMDoctorData stationMDoctorData = new StationMDoctorData();
            stationMDoctorData.setUuid(UUID.randomUUID().toString());
            stationMDoctorData.setTitle(originManData.getTitle());

            // 缩略图处理 （只有一张图片）
            String thumb = originManData.getThumb();
            // 调用接口
            if (!thumb.isBlank()) {
                thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                thumb = CrawlerManager.changeFileUrl(new File(thumb));
            }
            stationMDoctorData.setThumb(thumb);

            // 处理正文,摘要
            String content = originManData.getContent();
            content = changeSpecialChat(content);
            Document document = Jsoup.parse(content);
            Elements imgList = document.select("img");
            Elements linkList = document.select("a");
            Elements videoList = document.select("video");
            // 图片路径集合处理
            try {
                for (Element element : imgList) {
                    String imgUrl = element.attr("src");
                    if (StringUtils.isNotBlank(imgUrl)) {
                        imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                        if (StringUtils.isNotBlank(imgUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("图片下载失败,是资源id为:" + originManData.getDid());
            }

            // 链接路径集合处理
            try {
                for (Element element : linkList) {
                    String attachUrl = element.attr("href");
                    if (StringUtils.isNotBlank(attachUrl)) {
                        attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                        if (StringUtils.isNotBlank(attachUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                            element.attr("href", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("链接下载失败,是资源id为:" + originManData.getDid());
            }

            // 视频路径集合处理
            try {
                for (Element element : videoList) {
                    String videoUrl = element.attr("src");
                    if (StringUtils.isNotBlank(videoUrl)) {
                        videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                        if (StringUtils.isNotBlank(videoUrl)) {
                            String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("视频下载失败,是资源id为:" + originManData.getDid());
            }
            content = document.toString();

            stationMDoctorData.setContent(content);

            //TODO  处理医师职称
            String protit = originManData.getDocPosition();
            if (protit!=null&&!protit.isEmpty()) {
                stationMDoctorData.setDocPosition(Integer.valueOf(protit));
            }
            //TODO 处理教务职称
            String eduPosition = originManData.getEduPosition();
            if (eduPosition!=null&&!eduPosition.isEmpty()) {
                stationMDoctorData.setEduPosition(Integer.valueOf(eduPosition));
            }
            //TODO 处理教务岗位
            String eduPost = originManData.getEduPost();
            if (eduPost!=null&&!eduPost.isEmpty()) {
                stationMDoctorData.setEduPost(Integer.valueOf(eduPost));
            }

            //TODO 处理擅长
            stationMDoctorData.setGoodat(originManData.getGoodat());
            // 处理科室
            //TODO 获取以前科室名称
            String depart = originManData.getDepart();
            if (!depart.isEmpty()) {
                String[] departs = depart.split(",");
                List<String> departNames = new ArrayList<>();
                List<Integer> newDepartIds = new ArrayList<>();
                for (int i = 0; i < departs.length; i++) {
                    departNames.add(catMap.get(Integer.parseInt(departs[i])));
                }
                //TODO 通过科室名称匹配现在科室id
                DynamicDataSource.changeDynamicDataSource();
                LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(StationCategory::getName, departNames);
                List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
                for (StationCategory stationCategory : stationCategories) {
                    newDepartIds.add(stationCategory.getId());
                }
                stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
            }

            //TODO 处理观看数
            stationMDoctor.setViews(originManData.getViews());
            //TODO  处理发布时间
            stationMDoctorData.setPublishTime(Double.valueOf(originMan.getPublishTime() + "000"));

            chunkResult.put(stationMDoctor, stationMDoctorData);
        }

        return chunkResult;
    }
    @Test
    public void moveImageJava(){
        DynamicDataSource.changeDefaultDataSource();
        Integer originCatId=116;
        Integer targetCatId=23;
        LambdaQueryWrapper<HmsCImage> wrapper =new LambdaQueryWrapper<>();
        //TODO 找到所有存在的图集消息
        wrapper.eq(HmsCImage::getCatid, originCatId)
                .eq(HmsCImage::getState, 0)
                .eq(HmsCImage::getStatus,  99)
                .orderByAsc(HmsCImage::getListorder);
        List<HmsCImage> originImages = hmsCImageMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMImage, StationMImageData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCImage>> taskChunks = divideTasks(originImages, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMImage, StationMImageData> chunkResult =
                            processTaskChunk3(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMImage, StationMImageData> mergedMap = new LinkedHashMap<>();
        for (Map<StationMImage, StationMImageData> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMImage targetImage : mergedMap.keySet()) {
            StationMImageData targetImageData = mergedMap.get(targetImage);
            // 先加入资源数据
            stationMImageDataMapper.insert(targetImageData);
            // 引用数据引用资源数据id
            targetImage.setDataId(targetImageData.getDataId());
            // 加入引用数据
            stationMImageMapper.insert(targetImage);
            // 根据id更新排序
            targetImage.setSortLevel(targetImage.getId().intValue());
            stationMImageMapper.updateById(targetImage);
        }
    }
    private Map<StationMImage, StationMImageData> processTaskChunk3(
            List<HmsCImage> chunk, Integer targetCatId){
        Map<StationMImage, StationMImageData> chunkResult=new LinkedHashMap<>();

        try {
            for (HmsCImage originImage : chunk) {
                //TODO 新的image
                StationMImage stationMImage = new StationMImage();
                stationMImage.setCatId(targetCatId);
                stationMImage.setPublishUserId(1);
                stationMImage.setCreateTime(Double.valueOf(originImage.getInputTime()+"000"));
                stationMImage.setUpdateTime(Double.valueOf(originImage.getUpdateTime()+"000"));
                stationMImage.setState(99);
                stationMImage.setUri("/"+ CrawlerManager.randomCharacterGenerator()+".html");
                stationMImage.setIsTop(0);
                stationMImage.setIsLock(0);

                //TODO 找到修改前的imageData并修改存入新的imageData
                LambdaQueryWrapper<HmsCImageData> wrapper1=new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCImageData::getDid, originImage.getDataid());
                HmsCImageData originImageData=hmsCImageDataMapper.selectOne(wrapper1);

                //TODO 新的imageData
                StationMImageData stationMImageData = new StationMImageData();
                stationMImageData.setUuid(UUID.randomUUID().toString());
                stationMImageData.setTitle(originImageData.getTitle());

                //TODO 处理访问量
                stationMImage.setViews(originImageData.getViews());
                //TODO 处理作者
                String author =originImageData.getAuthor();
                author = changeSpecialChat(author);
                if(author != null &&!author.equals("[]")&&!author.isEmpty()){
                    stationMImageData.setAuthor(author);
                }
                //TODO 处理摄影师
                String photographer =originImageData.getPhotographer();
                if(photographer  != null &&!photographer.equals("[]")&&!photographer.isEmpty()){
                    photographer = changeSpecialChat(photographer);
                    stationMImageData.setPhotographer(photographer);
                }
                //TODO 处理来自
                String comeFrom =originImageData.getComefrom();
                if(comeFrom != null &&!comeFrom.isEmpty()){
                    comeFrom = changeSpecialChat(comeFrom);
                    stationMImageData.setComefrom(comeFrom);
                }
                //TODO 处理images
                String images =originImageData.getImages();
                JSONArray jsonArray =  JSONArray.parseArray(images);
                if(jsonArray!=null&&!jsonArray.isEmpty()){
                    //TODO 单图片的图集
                    if(jsonArray.size()==1){
                        JSONObject item =jsonArray.getJSONObject(0);
                        //TODO 设置图集时间戳
                        long currentTimeMillis = System.currentTimeMillis();
                        item.put("id",currentTimeMillis);
                        //TODO 获取url图片进行替换
                        String url = item.getString("url");
                        if(url!=null&&!url.isBlank()){
                            url=CrawlerManager.DownLoad(url,downloadPath,refer);
                            url=CrawlerManager.changeFileUrl(new File(url));
                        }
                        item.put("url", url);
                        //TODO 设置图片标题
                        item.put("title",originImageData.getTitle()+".jpg");
                        //TODO 获取description并解码Unicode转义字符
                        String description=item.getString("description");
                        if(description!=null&&!description.isEmpty()){
                            String decodedDescription = decodeUnicode(description);
                            decodedDescription=StringUtils.abbreviate(decodedDescription, 11);
                            item.put("description", decodedDescription);
                        }
                    }
                    JSONObject  dbItem=jsonArray.getJSONObject(0);
                    JSONArray jsonArray1=new JSONArray();
                    jsonArray1.add(dbItem);
                    stationMImageData.setImages(jsonArray1.toJSONString());
                }
                //TODO 处理发布时间
                stationMImageData.setPublishTime(Double.valueOf(originImage.getPublishTime()+"000"));

                chunkResult.put(stationMImage, stationMImageData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }

        return chunkResult;
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }
    /**
     * 解码 Unicode 转义字符，如 \u5e74 -> 年
     */
    public static String decodeUnicode(String input) {
        int length = input.length();
        StringBuilder out = new StringBuilder();

        for (int i = 0; i < length; i++) {
            char c = input.charAt(i);
            if (c == '\\' && i + 5 < length && input.charAt(i + 1) == 'u') {
                String hex = input.substring(i + 2, i + 6);
                try {
                    out.append((char) Integer.parseInt(hex, 16));
                    i += 5;
                } catch (NumberFormatException e) {
                    out.append(c);
                }
            } else {
                out.append(c);
            }
        }

        return out.toString();
    }
}
