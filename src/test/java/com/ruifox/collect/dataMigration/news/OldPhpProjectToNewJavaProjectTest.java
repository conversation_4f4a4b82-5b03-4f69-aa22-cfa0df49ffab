package com.ruifox.collect.dataMigration.news;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.station.*;
import com.ruifox.collect.dao.mapper.tbl.*;
import com.ruifox.collect.dao.mapper.tbl_old.TblCNewsDataMapperOld;
import com.ruifox.collect.dao.mapper.tbl_old.TblCNewsMapperOld;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.station.*;
import com.ruifox.collect.module.entity.tbl.*;
import com.ruifox.collect.module.entity.tbl_old.TblCNewsDataOld;
import com.ruifox.collect.module.entity.tbl_old.TblCNewsOld;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class OldPhpProjectToNewJavaProjectTest {
    @Autowired
    private TblCNewsMapperOld tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapperOld tblCNewsDataMapper;
    @Autowired
    private TblCManDataMxfyMapper tblCManDataMxfyMapper;
    @Autowired
    private TblCManMapper tblCManMapper;
    @Autowired
    private StationMArticleMapper stationMArticleMapper;
    @Autowired
    private StationMArticleDataMapper stationMArticleDataMapper;
    @Autowired
    private StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    private StationMDoctorDataMapper stationMDoctorDataMapper;
    @Autowired
    private StationMTeacherMapper stationMTeacherMapper;
    @Autowired
    private StationMTeacherDataMapper stationMTeacherDataMapper;
    @Autowired
    private StationCategoryMapper stationCategoryMapper;
    @Autowired
    private TblCategoryMapper tblCategoryMapper;
    @Autowired
    private TblCPictureMapper tblCPictureMapper;
    @Autowired
    private TblCPictureDataMapper tblCPictureDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Autowired
    private StationMImageDataMapper stationMImageDataMapper;

    private static String refer = "http://jrha.lan24.foxtest.net/";
    private static String downloadPath = "C:\\Collect_Data\\cd3hospital";



    @Test
    public void moveNewsJava() {
        Map<Integer, Integer> map = new HashMap<>();
//        map.put(95,108);//招聘信息
//        map.put(94,107);
//        map.put(19,28);
//        map.put(20,29);
        map.put(99,109);
//        map.put(102,110);
        Integer id = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCNewsOld> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCNewsOld::getCatid, originCatId)
                .eq(TblCNewsOld::getState, 0)
                .eq(TblCNewsOld::getStatus, 99)
                .orderByAsc(TblCNewsOld::getListorder);
        List<TblCNewsOld> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 60;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMArticle, StationMArticleData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCNewsOld>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMArticle, StationMArticleData> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMArticle, StationMArticleData> mergedMap = new LinkedHashMap<>();
        for (Map<StationMArticle, StationMArticleData> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 切换到总院的数据源
        DynamicDataSource.changeOldJavaDynamicDataSource();
        // 处理合并后的结果
        for (StationMArticle targetNew : mergedMap.keySet()) {
            StationMArticleData targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            stationMArticleDataMapper.insert(targetNewData);
            // 引用数据引用资源数据id
            targetNew.setDataId(targetNewData.getDataId());
            // 加入引用数据
            stationMArticleMapper.insert(targetNew);
            // 根据id更新排序
            targetNew.setSortLevel(targetNew.getId());
            stationMArticleMapper.updateById(targetNew);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start=end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMArticle, StationMArticleData> processTaskChunk(
            List<TblCNewsOld> chunk, Integer targetCatId) {
        Map<StationMArticle, StationMArticleData> chunkResult = new LinkedHashMap<>();
        try {
            for (TblCNewsOld originNew : chunk) {
                DynamicDataSource.changeOldPhpDynamicDataSource();
                // 新的article
                StationMArticle stationMArticle = new StationMArticle();
                stationMArticle.setCatId(targetCatId);
                stationMArticle.setPublishUserId(1);
                stationMArticle.setCreateTime(Double.valueOf(originNew.getInputtime() + "000"));
//                stationMArticle.setUpdateTime(Double.valueOf(originNew.getUpdatetime() + "000"));
                stationMArticle.setState(99);
                stationMArticle.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMArticle.setViews(originNew.getViews());
                stationMArticle.setIsTop(0);
                stationMArticle.setIsLock(0);

                String linkUrl = originNew.getUrl();
                int isLink = originNew.getIslink();
                //根据url判断是否为外链
                if (linkUrl.contains("lszph")) {
                    isLink = 0;
                }

                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblCNewsDataOld> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCNewsDataOld::getId, originNew.getId());
                TblCNewsDataOld originNewsData = tblCNewsDataMapper.selectOne(wrapper1);

                // 新的articleData
                StationMArticleData stationMArticleData = new StationMArticleData();
                stationMArticleData.setUuid(UUID.randomUUID().toString());
                stationMArticleData.setTitle(originNew.getTitle());
//                // 处理作者
//                String author = originNew.getAuthor();
//                author = changeSpecialChat(author);
//                if (!author.equals("0") && !author.isEmpty())
//                    stationMArticleData.setAuthor(originNewsData.getAuthor());

                // 缩略图处理 （只有一张图片）
                String originThumb = originNew.getThumb();
                String thumb = originNew.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMArticleData.setThumb(thumb);

                // 处理正文,摘要
                String content = originNewsData.getContent();
                String description = originNew.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getId());
                    }
                    content = document.toString();
                } else {
                    // 是链接的话,content为空
                    content = linkUrl;
                    description = linkUrl;
                }
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                // 处理来源
                stationMArticleData.setComefrom(originNew.getComefrom());
                // 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                // 处理发布时间

                stationMArticleData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }

        return chunkResult;
    }

    @Test
    public void moveDoctorJava() {
        Integer originCatId = 38;
        Integer targetCatId = 246;

        LambdaQueryWrapper<TblCMan> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCMan::getCatid, originCatId)
                .eq(TblCMan::getState, 0)
                .eq(TblCMan::getStatus, 99)
                .orderByAsc(TblCMan::getListorder);
        List<TblCMan> originMans = tblCManMapper.selectList(wrapper);

        List<TblCategory> tblCategories = tblCategoryMapper.selectList(null);
        Map<Integer, String> catMap = new HashMap<>();
        for (TblCategory tblCategory : tblCategories) {
            catMap.put(tblCategory.getCatid(), tblCategory.getCatname());
        }

        // 定义线程数量
        int threadCount = 6;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMDoctor, StationMDoctorData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, new HashMap<>())));

        // 分割任务
        List<List<TblCMan>> taskChunks = divideTasks(originMans, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMDoctor, StationMDoctorData> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex), targetCatId, catMap);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMDoctor, StationMDoctorData> mergedMap = new LinkedHashMap<>();
        for (Map<StationMDoctor, StationMDoctorData> map : resultMaps) {
            if (map != null) { // 添加空值检查
                mergedMap.putAll(map);
            }
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMDoctor targetMan : mergedMap.keySet()) {
            StationMDoctorData targetManData = mergedMap.get(targetMan);
            // 先加入资源数据
            stationMDoctorDataMapper.insert(targetManData);
            // 引用数据引用资源数据id
            targetMan.setDataId(targetManData.getDataId());
            // 加入引用数据
            stationMDoctorMapper.insert(targetMan);
            // 根据id更新排序
            targetMan.setSortLevel(targetMan.getId());
            stationMDoctorMapper.updateById(targetMan);
        }
    }

    private Map<StationMDoctor, StationMDoctorData> processTaskChunk2(
            List<TblCMan> chunk, Integer targetCatId, Map<Integer, String> catMap) {
        Map<StationMDoctor, StationMDoctorData> chunkResult = new LinkedHashMap<>();

        try {
            for (TblCMan originMan : chunk) {
                DynamicDataSource.changeDefaultDataSource();
                // 新的doctor
                StationMDoctor stationMDoctor = new StationMDoctor();
                stationMDoctor.setCatId(targetCatId);
//                stationMDoctor.setPublishUserId(1);
//                stationMDoctor.setCreateTime(Double.valueOf(originMan.getInputtime() + "000"));
//                stationMDoctor.setUpdateTime(Double.valueOf(originMan.getUpdatetime() + "000"));
                stationMDoctor.setState(99);
                stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMDoctor.setViews(originMan.getViews());
                stationMDoctor.setIsTop(0);
//                stationMDoctor.setIsLock(0);

                // 找到修改前的manData并修改后存入新的doctorData
                LambdaQueryWrapper<TblCManDataMxfy> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCManDataMxfy::getId, originMan.getDataid());
                TblCManDataMxfy originManData = tblCManDataMxfyMapper.selectOne(wrapper1);

                // 新的articleData
                StationMDoctorData stationMDoctorData = new StationMDoctorData();
                stationMDoctorData.setUuid(UUID.randomUUID().toString());
                stationMDoctorData.setTitle(originManData.getTitle());

                // 缩略图处理 （只有一张图片）
                String thumb = originManData.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMDoctorData.setThumb(thumb);

                // 处理正文,摘要
                String content = originManData.getContent();
                content = changeSpecialChat(content);
                Document document = Jsoup.parse(content);
                Elements imgList = document.select("img");
                Elements linkList = document.select("a");
                Elements videoList = document.select("video");
                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(imgUrl)) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("图片下载失败,是资源id为:" + originManData.getId());
                }

                // 链接路径集合处理
                try {
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNotBlank(attachUrl)) {
                            attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(attachUrl)) {
                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                element.attr("href", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("链接下载失败,是资源id为:" + originManData.getId());
                }

                // 视频路径集合处理
                try {
                    for (Element element : videoList) {
                        String videoUrl = element.attr("src");
                        if (StringUtils.isNotBlank(videoUrl)) {
                            videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(videoUrl)) {
                                String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                element.attr("src", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("视频下载失败,是资源id为:" + originManData.getId());
                }
                content = document.toString();

                stationMDoctorData.setContent(content);

                // 处理医师职称
                String protit = originManData.getProtit();
                if (!protit.isEmpty()) {
                    Map<Integer, Integer> docPositionMap = getDocPositionMap();
                    stationMDoctorData.setDocPosition(docPositionMap.get(Integer.valueOf(protit)));
                }

                // 处理擅长
                stationMDoctorData.setGoodat(originManData.getGoodat());
                // 处理科室
                //TODO 获取以前科室名称
                String depart = originManData.getDepart();
                if (!depart.isEmpty()) {
                    String[] departs = depart.split(",");
                    List<String> departNames = new ArrayList<>();
                    List<Integer> newDepartIds = new ArrayList<>();
                    for (int i = 0; i < departs.length; i++) {
                        departNames.add(catMap.get(Integer.valueOf(departs[i])));
                    }
                    //通过科室名称匹配现在科室id
                    DynamicDataSource.changeDynamicDataSource();
                    LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
                    wrapper.in(StationCategory::getName, departNames);
                    List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
                    for (StationCategory stationCategory : stationCategories) {
                        newDepartIds.add(stationCategory.getId());
                    }
                    stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
                }

                // 处理发布时间
                stationMDoctorData.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));

                chunkResult.put(stationMDoctor, stationMDoctorData);
            }
        } catch (Exception e) {
            log.error("数据处理出错", e);
        }

        return chunkResult;
    }

//    private Map<StationMDoctor2, StationMDoctorData2> processTaskChunk2XBQ(
//            List<TblCMan> chunk, Integer targetCatId, Map<Integer, String> catMap) {
//        Map<StationMDoctor2, StationMDoctorData2> chunkResult = new LinkedHashMap<>();
//        try {
//            for (TblCMan originMan : chunk) {
//                DynamicDataSource.changeDefaultDataSource();
//                // 新的doctor
//                StationMDoctor2 stationMDoctor = new StationMDoctor2();
//                stationMDoctor.setCatId(targetCatId);
//                stationMDoctor.setPublishUserId(1);
//                stationMDoctor.setCreateTime(Double.valueOf(originMan.getInputtime() + "000"));
//                stationMDoctor.setUpdateTime(Double.valueOf(originMan.getUpdatetime() + "000"));
//                stationMDoctor.setState(99);
//                stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
//                stationMDoctor.setViews(originMan.getViews());
//                stationMDoctor.setIsTop(0);
//                stationMDoctor.setIsLock(0);
//
//                // 找到修改前的manData并修改后存入新的doctorData
//                LambdaQueryWrapper<TblCManDataXbq> wrapper1 = new LambdaQueryWrapper<>();
//                wrapper1.eq(TblCManDataXbq::getId, originMan.getDataid());
//                TblCManDataXbq originManData = tblCManDataXbqMapper.selectOne(wrapper1);
//
//                // 新的articleData
//                StationMDoctorData2 stationMDoctorData = new StationMDoctorData2();
//                stationMDoctorData.setUuid(UUID.randomUUID().toString());
//                stationMDoctorData.setTitle(originManData.getTitle());
//
//                // 缩略图处理 （只有一张图片）
//                String thumb = originManData.getThumb();
//                // 调用接口
//                if (!thumb.isBlank()) {
//                    thumb = CrawlerManager.DownLoad(thumb, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
//                }
//                stationMDoctorData.setThumb(thumb);
//
//                // 处理正文,摘要
//                String content = originManData.getContent();
//                content = changeSpecialChat(content);
//                Document document = Jsoup.parse(content);
//                Elements imgList = document.select("img");
//                Elements linkList = document.select("a");
//                Elements videoList = document.select("video");
//                // 图片路径集合处理
//                try {
//                    for (Element element : imgList) {
//                        String imgUrl = element.attr("src");
//                        if (StringUtils.isNotBlank(imgUrl)) {
//                            imgUrl = CrawlerManager.DownLoad(imgUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(imgUrl)) {
//                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
//                                element.attr("src", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("图片下载失败,是资源id为:" + originManData.getId());
//                }
//
//                // 链接路径集合处理
//                try {
//                    for (Element element : linkList) {
//                        String attachUrl = element.attr("href");
//                        if (StringUtils.isNotBlank(attachUrl)) {
//                            attachUrl = CrawlerManager.DownLoad(attachUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(attachUrl)) {
//                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
//                                element.attr("href", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("链接下载失败,是资源id为:" + originManData.getId());
//                }
//
//                // 视频路径集合处理
//                try {
//                    for (Element element : videoList) {
//                        String videoUrl = element.attr("src");
//                        if (StringUtils.isNotBlank(videoUrl)) {
//                            videoUrl = CrawlerManager.DownLoad(videoUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(videoUrl)) {
//                                String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
//                                element.attr("src", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("视频下载失败,是资源id为:" + originManData.getId());
//                }
//                content = document.toString();
//
//                stationMDoctorData.setContent(content);
//
//                // 处理医师职称
//                String docPosition = originManData.getDocPosition();
//                if (!docPosition.isEmpty()) {
//                    stationMDoctorData.setDocPosition(Integer.valueOf(docPosition));
//                }
//
//                // 处理擅长
//                stationMDoctorData.setGoodat(originManData.getGoodat());
//                // 处理科室
//                //TODO 获取以前科室名称
//                String depart = originManData.getDepart();
//                if (!depart.isEmpty()) {
//                    String[] departs = depart.split(",");
//                    List<String> departNames = new ArrayList<>();
//                    List<Integer> newDepartIds = new ArrayList<>();
//                    for (int i = 0; i < departs.length; i++) {
//                        departNames.add(catMap.get(Integer.valueOf(departs[i])));
//                    }
//                    //通过科室名称匹配现在科室id
//                    DynamicDataSource.changeDynamicDataSource();
//                    LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
//                    wrapper.in(StationCategory2::getName, departNames);
//                    List<StationCategory2> stationCategories = stationCategoryMapper.selectList(wrapper);
//                    for (StationCategory2 stationCategory : stationCategories) {
//                        newDepartIds.add(stationCategory.getId());
//                    }
//                    stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
//                }
//
//                // 处理发布时间
//                stationMDoctorData.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));
//
//                chunkResult.put(stationMDoctor, stationMDoctorData);
//            }
//        } catch (Exception e) {
//            log.error("解析数据失败", e);
//        }
//
//        return chunkResult;
//    }

    @Test
    public void moveNewsToTeacherJava() {
        Integer originCatId = 3843;
        Integer targetCatId = 254;

        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<TblCNewsOld> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCNewsOld::getCatid, originCatId)
                .eq(TblCNewsOld::getState, 0)
                .eq(TblCNewsOld::getStatus, 99)
                .orderByAsc(TblCNewsOld::getListorder);
        List<TblCNewsOld> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        Map<StationMTeacher, StationMTeacherData> newTeacherDataMap = new LinkedHashMap<>();
        for (TblCNewsOld originNew : originNews) {
            // 新的teacher
            StationMTeacher stationMTeacher = new StationMTeacher();
            stationMTeacher.setCatId(targetCatId);
            stationMTeacher.setPublishUserId(1);
            stationMTeacher.setCreateTime(Double.valueOf(originNew.getInputtime() + "000"));
            stationMTeacher.setUpdateTime(Double.valueOf(originNew.getUpdatetime() + "000"));
            stationMTeacher.setState(99);
            stationMTeacher.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMTeacher.setViews(originNew.getViews());
            stationMTeacher.setIsTop(0);
            stationMTeacher.setIsLock(0);


            // 找到修改前的newsData并修改后存入新的teacherData
            LambdaQueryWrapper<TblCNewsDataOld> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TblCNewsDataOld::getId, originNew.getId());
            TblCNewsDataOld originNewsData = tblCNewsDataMapper.selectOne(wrapper1);

            // 新的articleData
            StationMTeacherData stationMTeacherData = new StationMTeacherData();
            stationMTeacherData.setUuid(UUID.randomUUID().toString());
            stationMTeacherData.setTitle(originNew.getTitle());

            // 缩略图处理 （只有一张图片）
            String thumb = originNew.getThumb();
            // 调用接口
            if (!thumb.isBlank()) {
                thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                thumb = CrawlerManager.changeFileUrl(new File(thumb));
            }
            stationMTeacherData.setThumb(thumb);

            // 处理培养研究生学校、指导研究生层次、研究方向、邮箱
            String description = originNew.getDescription();
            description = changeSpecialChat(description);
            description = description.replaceAll("</br>", "");
            String[] split = description.split("\n");
            String school = "";
            String level = "";
            String direction = "";
            String mail = "";
            for (String s : split) {
                if (s.contains("联合培养研究生学校")) {
                    school = s.split("：")[1];
                } else if (s.contains("当前指导研究生层次")) {
                    level = s.split("：")[1];
                } else if (s.contains("研究方向")) {
                    direction = s.split("：")[1];
                } else if (s.contains("邮箱")) {
                    mail = s.split("：")[1];
                }
            }
            stationMTeacherData.setSchool(school);
            stationMTeacherData.setCurrentLevel(level);
            stationMTeacherData.setResearchDirection(direction);
            stationMTeacherData.setEmail(mail);

            // 处理发布时间
            stationMTeacherData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

            newTeacherDataMap.put(stationMTeacher, stationMTeacherData);
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (StationMTeacher targetTeacher : newTeacherDataMap.keySet()) {
            StationMTeacherData targetTeacherData = newTeacherDataMap.get(targetTeacher);
            // 先加入资源数据
            stationMTeacherDataMapper.insert(targetTeacherData);
            // 引用数据引用资源数据id
            targetTeacher.setDataId(targetTeacherData.getDataId());
            // 加入引用数据
            stationMTeacherMapper.insert(targetTeacher);
            // 根据id更新排序
            targetTeacher.setSortLevel(targetTeacher.getId());
            stationMTeacherMapper.updateById(targetTeacher);
        }
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }

    @Test
    public void testSpecialChat(){
        String content = "&#305html>&#305head>&#305/head>&#305body>&#305p style&#304\\&#302text-indent:43px\\&#302>&#305span style&#304\\&#302&#300font-family:\\&#301Times New Roman\\&#301&#300font-size:21px\\&#302>2025&#305span style&#304\\&#302font-family:仿宋\\&#302>年&#305/span>&#305span style&#304\\&#302font-family:Times New Roman\\&#302>7&#305/span>&#305span style&#304\\&#302font-family:仿宋\\&#302>月&#305/span>&#305/span>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>&#305span style&#304\\&#302font-family:Times New Roman\\&#302>21&#305/span>&#305/span>&#305span style&#304\\&#302&#300font-family:\\&#301Times New Roman\\&#301&#300font-size:21px\\&#302>&#305span style&#304\\&#302font-family:仿宋\\&#302>日&#305/span>&#305/span>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>上午&#305/span>&#305span style&#304\\&#302&#300font-family:\\&#301Times New Roman\\&#301&#300font-size:21px\\&#302>&#305span style&#304\\&#302font-family:仿宋\\&#302>，牛晓宇副院长带领医务部、护理部、门诊部、病案管理部、运营管理与评价部&#305/span>&#305/span>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>、中心手术室&#305/span>&#305span style&#304\\&#302&#300font-family:\\&#301Times New Roman\\&#301&#300font-size:21px\\&#302>&#305span style&#304\\&#302font-family:仿宋\\&#302>等相关职能部门&#305/span>&#305/span>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>与&#305/span>&#305span style&#304\\&#302&#300font-family:\\&#301Times New Roman\\&#301&#300font-size:21px\\&#302>&#305span style&#304\\&#302font-family:仿宋\\&#302>科室赴儿童&#305/span>&#305/span>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>骨科和儿童胸外科&#305/span>&#305span style&#304\\&#302&#300font-family:\\&#301Times New Roman\\&#301&#300font-size:21px\\&#302>&#305span style&#304\\&#302font-family:仿宋\\&#302>开展&#305/span>&#305/span>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>新一轮&#305/span>&#305span style&#304\\&#302&#300font-family:\\&#301Times New Roman\\&#301&#300font-size:21px\\&#302>&#305span style&#304\\&#302font-family:仿宋\\&#302>现场调研，并与科室管理小组和一线医务人员进行充分沟通与交流。&#305/span>&#305/span>&#305/p>&#305p style&#304\\&#302text-indent:43px\\&#302 dir&#304\\&#302ltr\\&#302>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>牛晓宇指出本轮新建专科调研的目的是围绕医院集团化发展部署，同时加强职能部门与临床科室尤其是一线医务人员之间的交流。各相关职能部门和科室针对科室会前提出的需求逐一回复，同时通报了近期医疗管理重点事项和科室运营情况。&#305/span>&#305span style&#304\\&#302&#300font-family:仿宋&#300font-size:21px\\&#302>而后，调研组与科室人员进行了充分沟通，深入讨论了科室日常工作中存在的问题和困难，并要求相关职能部门要举一反三、系统梳理并尽快解决。同时，也听取了科室关于集团化医疗背景下科室业务布局、流程设计及工作设想等方面建议。&#305/span>&#305/p>&#305p style&#304\\&#302text-indent:43px\\&#302>&#305span style&#304\\&#302font-family: 仿宋&#300 font-size: 21px&#300\\&#302>未来，我院将持续践行“走动式”管理思想，积极深入临床一线调研，&#305/span>&#305span style&#304\\&#302font-family: 仿宋&#300 font-size: 21px&#300\\&#302>充分发挥职能部门主动服务科室和排忧解难的工作作风。并&#305/span>&#305span style&#304\\&#302font-family: 仿宋&#300 font-size: 21px&#300\\&#302>坚持问题导向和系统化思维相结合，深挖细掘集团化医疗发展所需，与临床科室齐心并肩、共谋高质量发展之道，推动医院集团化医疗提质增效，不断提升医疗服务水平和患者满意度。&#305/span>&#305/p>&#305p style&#304\\&#302text-indent:43px\\&#302>&#305span style&#304\\&#302font-family: 仿宋&#300 font-size: 21px&#300\\&#302>&#305/span>&#305/p>&#305p style&#304\\&#302text-align: center&#300\\&#302>&#305img src&#304\\&#302https://cmc.motherchildren.com/hx-hosp-media-integration/hhmi/687f494ab7601ed881515f3c.png\\&#302 style&#304\\&#302max-width: 100%\\&#302 title&#304\\&#30226823f65b88f2f783b7c3340646c2fd\\&#302 class&#304\\&#302\\&#302>&#305/p>&#305p style&#304\\&#302text-align: center&#300 line-height: normal&#300\\&#302>&#305img src&#304\\&#302https://cmc.motherchildren.com/hx-hosp-media-integration/hhmi/687f4972b7604c18f8d94951.png\\&#302 style&#304\\&#302max-width: 100%\\&#302 title&#304\\&#30271b21e0b2ed5f05b2437148bdc5af7e\\&#302 class&#304\\&#302\\&#302>&#305/p>&#305p style&#304\\&#302text-indent:43px\\&#302>&#305span style&#304\\&#302font-family: 仿宋&#300 font-size: 21px&#300\\&#302>&#305br>&#305/span>&#305br>&#305/p>&#305p>&#305br>&#305/p>&#305/body>&#305/html>";
        System.out.println(changeSpecialChat(content));
    }

    @Test
    public void test() {
        String Refer = "https://www.cd5120.cn/";
        String localUrl = "https://video.my120.org/10793150106b71f087435017f0e80102/bf581d93f819445fac12ba3e9dfc22d1-e888565b2cc924e7154cadf45dbb234c-hd.mp4";
        String localDownLoadPath = "C:\\Collect_Data\\cd5120_video";
        String s = CrawlerManager.DownLoad(localUrl, localDownLoadPath, Refer);
//        String s1 = CrawlerManager.changeFileUrl(new File(s));
//        log.info(s1);
    }

    @Test
    public void test2() {
        String a = "姓名：徐俊波&#305/br>\n" +
                "联合培养研究生学校：西南交通大学、川北医学院&#305/br>\n" +
                "当前指导研究生层次：硕士研究生&#305/br>\n" +
                "研究方向：高血压，心力衰竭等心血管病的药物器械治疗&#300医院管理&#305/br>\n" +
                "邮箱：<EMAIL>";
        a = changeSpecialChat(a);
        a = a.replaceAll("</br>", "");
        String[] split = a.split("\n");
        String name = "";
        String school = "";
        String level = "";
        String direction = "";
        String mail = "";
        for (String s : split) {
            if (s.contains("姓名")) {
                name = s.split("：")[1];
            } else if (s.contains("联合培养研究生学校")) {
                school = s.split("：")[1];
            } else if (s.contains("当前指导研究生层次")) {
                level = s.split("：")[1];
            } else if (s.contains("研究方向")) {
                direction = s.split("：")[1];
            } else if (s.contains("邮箱")) {
                mail = s.split("：")[1];
            }
        }
        System.out.println(name);
        System.out.println(school);
        System.out.println(level);
        System.out.println(direction);
        System.out.println(mail);
    }

    private Map<Integer, Integer> getDocPositionMap() {
        Map<Integer, Integer> titleLevelMap = new HashMap<>();

        // 医师系列
        titleLevelMap.put(99, 1);  // 主任医师
        titleLevelMap.put(89, 2);  // 副主任医师
        titleLevelMap.put(79, 3);  // 主治医师
        titleLevelMap.put(69, 12); // 医师
        titleLevelMap.put(68, 12); // 住院医师
        titleLevelMap.put(59, 13); // 医士

        // 心理咨询师系列
        titleLevelMap.put(96, 14); // 一级心理咨询师
        titleLevelMap.put(86, 15); // 二级心理咨询师
        titleLevelMap.put(76, 16); // 三级心理咨询师

        // 护师系列
        titleLevelMap.put(95, 6);  // 主任护师
        titleLevelMap.put(85, 7);  // 副主任护师
        titleLevelMap.put(75, 8);  // 主管护师
        titleLevelMap.put(65, 17); // 护师
        titleLevelMap.put(55, 18); // 护士

        // 技师系列
        titleLevelMap.put(94, 19); // 主任技师
        titleLevelMap.put(84, 20); // 副主任技师
        titleLevelMap.put(74, 21); // 主管技师
        titleLevelMap.put(64, 22); // 技师
        titleLevelMap.put(54, 23); // 技士

        // 药师系列
        titleLevelMap.put(93, 4);  // 主任药师
        titleLevelMap.put(83, 5);  // 副主任药师
        titleLevelMap.put(73, 24); // 主管药师
        titleLevelMap.put(63, 25); // 药师
        titleLevelMap.put(53, 26); // 药士

        return titleLevelMap;
    }
}
