package com.ruifox.collect.dataMigration.news;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.hms.*;
import com.ruifox.collect.dao.mapper.station.StationCategoryMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.hms.*;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station.StationCategory;
import com.ruifox.collect.module.entity.station2.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class NewPhpProjectToNewJava2ProjectTest {
    @Autowired
    private HmsCNewsMapper hmsCNewsMapper;
    @Autowired
    private HmsCNewsDataMapper hmsCNewsDataMapper;
    @Autowired
    private HmsCMansMapper hmsCMansMapper;
    @Autowired
    private HmsCImageMapper hmsCImageMapper;
    @Autowired
    private HmsCImageDataMapper hmsCImageDataMapper;
    @Autowired
    private HmsCPagesMapper hmsCPagesMapper;
    @Autowired
    private HmsCPagesDataMapper hmsCPagesDataMapper;

    @Autowired
    private StationMImageDataMapper2 stationMImageDataMapper;
    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;
    @Autowired
    private HmsCMansDataMapper hmsCMansDataMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private StationMDoctorDataMapper2 stationMDoctorDataMapper;
    @Autowired
    private StationCategoryMapper stationCategoryMapper;
    @Autowired
    private StationMPageDataMapper2 stationMPageDataMapper;

    @Autowired
    private StationCategoryMapper2 stationCategoryMapper2;

    @Autowired
    private HmsModelFieldMapper hmsModelFieldMapper;
    @Autowired
    private StationModelFieldMapper2 stationModelFieldMapper2;

    //汉源县中医院
    @Value("${testDownload.config.fileName}")
    private String fileName;
    @Value("${testDownload.config.refer}")
    private String refer;
    private String pattern = "[^\\u4e00-\\u9fa5]";//非中文字符

    @Test
    public void moveNewsJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();

        Map<Integer, Integer> map = new HashMap<>();
        map.put(155,53);   // 党史学习教育
        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i]+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }


    @Test
    public void testGetNews(){
        HashMap<Integer,Integer> map = new HashMap<>();
        getNewsID(map);
        System.out.println("所匹配的栏目数量："+map.size());
    }


    public void getNewsID(Map<Integer, Integer> map){
        DynamicDataSource.changeNewPhpDynamicDataSource();
        //找到所有新闻数据的catID
        LambdaQueryWrapper<HmsCNews> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.select(HmsCNews::getCatid)
                .eq(HmsCNews::getStatus,99)
                .eq(HmsCNews::getState, 0);

        List<Integer> originList = hmsCNewsMapper.selectObjs(wrapper1);
        List<Integer> DistinctList = originList.stream().distinct().collect(Collectors.toList());

        //根据originCatId 找出对应的CategoryName为后续找到targetId做准备
        LambdaQueryWrapper<HmsCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(HmsCategory::getId,DistinctList);
        wrapper.select(HmsCategory::getName,HmsCategory::getId);
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper);

        List<String> success = new ArrayList<>();
        for (HmsCategory hmsCategory : hmsCategories) {

            DynamicDataSource.changeBuildDynamicDataSource();
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getName, hmsCategory.getName())
                    .eq(StationCategory2::getModelId,20);
            List<StationCategory2> stationCategory2 = stationCategoryMapper2.selectList(wrapper2);
            if (stationCategory2 .size()!=1)
                continue;
            map.put(Math.toIntExact(hmsCategory.getId()), stationCategory2.get(0).getId());
            String tmp = "map.put("+hmsCategory.getId()+","+stationCategory2.get(0).getId()+");   // "+hmsCategory.getName();
            success.add(tmp);
        }
        System.out.println("======================================");
        System.out.println("======================================");
        System.out.println("===========匹配成功的栏目总合计:"+success.size()+"条：==============");
        for (String s : success) {
            System.out.println(s);
        }

    }

    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        LambdaQueryWrapper<HmsCNews> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息与判断是否为发布状态
        wrapper.eq(HmsCNews::getCatid, originCatId)
                .eq(HmsCNews::getState, 0)
                .eq(HmsCNews::getStatus, 99)
//                .gt(HmsCNews::getPublishTime,1731600000)    // TODO 临时需要更新新数据
                .orderByAsc(HmsCNews::getListorder);

        List<HmsCNews> originNews = hmsCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 12;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCNews>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMArticleData2> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMArticleData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMArticleData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMArticleData2 targetNewData = mergedMap.get(targetNew);
            try {
                SaveToJava2(targetNew,targetNewData,stationMArticleDataMapper,targetCatId);
            }catch (Exception e){
                System.out.println("==========导入错误=========="+e.getMessage());
            }
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                 start = end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMReference, StationMArticleData2> processTaskChunk(
            List<HmsCNews> chunk, Integer targetCatId) {
        Map<StationMReference, StationMArticleData2> chunkResult = new LinkedHashMap<>();
        try {
            for (HmsCNews originNew : chunk) {
                // 新的news
                StationMReference stationMArticle = StationMReference.builder()
                        .catId(targetCatId)
                        .publishTime(Double.valueOf(originNew.getPublishTime() + "000"))
                        .state(99).uri("/" + CrawlerManager.randomCharacterGenerator() + ".html")
                        .isTop(0).build();

                String linkUrl = originNew.getIslink();
                int isLink = 0;
                if (linkUrl!=null && !linkUrl.isEmpty()) {
                    isLink = 1;
                }

                DynamicDataSource.changeNewPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<HmsCNewsData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCNewsData::getDid, originNew.getDataid());
                HmsCNewsData originNewsData = hmsCNewsDataMapper.selectOne(wrapper1);
                if (originNewsData==null)
                    continue;
                //处理访问量
                stationMArticle.setViews(originNewsData.getViews());
                // 新的articleData
                StationMArticleData2 stationMArticleData = StationMArticleData2.builder()
                        .uuid(UUID.randomUUID().toString())
                        .title(originNewsData.getTitle())
                        .ignoreReason(null).photographer(originNewsData.getPhotographer())
                        .createUserId(1).updateUserId(1)
                        .state(2).build();

                // 处理作者
                String author = originNewsData.getAuthor();
                if (author!=null&&!author.isEmpty()&&!author.isBlank()){
                    author = changeSpecialChat(author);
                    author = author.replaceAll(pattern,"");
                    if (!author.equals("0") && !author.isEmpty())
                        stationMArticleData.setAuthor(author);
                    if(author.equals("[]")){
                        stationMArticleData.setAuthor("");
                    }
                }
                // 缩略图处理 （只有一张图片）
                // TODO 临时特殊处理改url
//                String oss = originNewsData.getThumb();
//                if (oss!=null&&!oss.isBlank()){
//                    // TODO 临时特殊处理改url
////                    String thumb = oss.replace( oss.substring(0, oss.indexOf("oss")),"https://www.jrha.net.cn/");
////                    System.out.println(thumb);
//                    String thumb = originNewsData.getThumb();
//                    // 调用接口
//                    if (!thumb.isBlank()) {
//                        thumb = CrawlerManager.DownLoad(thumb, fileName+"\\news" ,refer);
//                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
//                    }
//                    stationMArticleData.setThumb(thumb);
//                }

                String thumb = originNewsData.getThumb();
                // 调用接口
                if (thumb!=null&&!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb,fileName,refer);
                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMArticleData.setThumb(thumb);
                // 处理正文,摘要
                String content = originNewsData.getContent();
                String description = originNewsData.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String imgUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {

                                imgUrl = CrawlerManager.DownLoad(imgUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                    if (stationMArticleData.getThumb().isEmpty()){
                                        stationMArticleData.setThumb(url);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getDid());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("href");
//                            String attachUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("weixin")){
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));

                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getDid());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String videoUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));

                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getDid());
                    }
                    content = document.body().children().toString();
                } else {
                    // 是链接的话,content为链接
                    content = linkUrl;
                    description = linkUrl;
                }
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                //TODO 处理来源
                stationMArticleData.setComefrom(originNewsData.getComefrom());
                //TODO 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                //TODO 处理发布时间
                stationMArticleData.setCreateTime(Double.valueOf(originNew.getPublishTime() + "000"));
                stationMArticleData.setUpdateTime(Double.valueOf(originNew.getPublishTime() + "000"));
                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }
        return chunkResult;
    }


    //医师职称
    private static HashMap<Integer,String> phpDocPosition = new HashMap<>();
    private static HashMap<String,Integer> java2DocPosition = new HashMap<>();

    //教务职称
    private static HashMap<Integer,String> phpEduPosition = new HashMap<>();
    private static HashMap<String,Integer> java2EduPosition = new HashMap<>();

    //教学岗位
    private static HashMap<Integer,String> phpEduPost = new HashMap<>();
    private static HashMap<String,Integer> java2EduPost = new HashMap<>();

    @Test
    public void moveDoctorJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        Map<Integer, Integer> map = new HashMap<>();

        //获得当前catID以及目标catID
        getDoctorID(map);
//        map.put(3,4); //医生介绍.
//        map.put(246,143);
//        map.put(262,159);
//        map.put(284,180);
//        map.put(287,183);
//        map.put(293,189);


        //获得列表数据
        getPositionList();

        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveDoctorJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveDoctorJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i]+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getDoctorID(Map<Integer, Integer> map){
        // 找到所有医生的的catId及其DepartName
        LambdaQueryWrapper<HmsCategory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(HmsCategory::getModelId,2)
                .eq(HmsCategory::getName,"工作室成员");   //TODO 每次根据实际修改
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper1);
        Integer[] origin = new Integer[hmsCategories.size()];

        String[] DepartName = new String[origin.length];
        for (int i = 0; i < hmsCategories.size(); i++) {
            origin[i]= Math.toIntExact(hmsCategories.get(i).getId());

            LambdaQueryWrapper<HmsCategory> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(HmsCategory::getId,hmsCategories.get(i).getPid());
            DepartName[i]=hmsCategoryMapper.selectOne(wrapper2).getName();
        }

        //根据DepartName一致找到targetID
        DynamicDataSource.changeBuildDynamicDataSource();
        for (int i = 0; i < origin.length; i++) {
            LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationCategory2::getName,DepartName[i]);
            System.out.println(stationCategoryMapper2.selectList(wrapper));
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectList(wrapper).get(0);
            if (stationCategory2==null)
                continue;

            LambdaQueryWrapper<StationCategory2> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(StationCategory2::getPid,stationCategory2.getId())
                    .eq(StationCategory2::getName,"工作室成员");//TODO 每次根据实际修改
            StationCategory2 stationCategory = stationCategoryMapper2.selectOne(wrapper3);
            if (stationCategory==null)
                continue;
            map.put(origin[i],stationCategory.getId());
        }
        System.out.println(map);
    }

    public <k,V> void getPhpList(String filedName, Integer modelId, HashMap<k,V> map){

    }

    public void getPositionList(){
        DynamicDataSource.changeNewPhpDynamicDataSource();
        //获得phpDoc医师职称列表
        //getPhpList("doc_position",2,phpDocPosition);
        LambdaQueryWrapper<HmsModelField> wrapperDoc = new LambdaQueryWrapper<>();
        wrapperDoc.eq(HmsModelField::getModelId,2)
                .eq(HmsModelField::getField,"doc_position");
        HmsModelField PhpDoc = hmsModelFieldMapper.selectOne(wrapperDoc);
        String setting = PhpDoc.getSetting();
        Map mapSetting = JsonUtil.Json2Obj(setting, Map.class);
        List<Map<Object,Object>> options = (List<Map<Object,Object>>)mapSetting.get("options");
        System.out.println(options.get(0).get("value")+" -----  "+options.get(0).get("key"));
        System.out.println(JsonUtil.obj2String(options));

        options.forEach(map1->phpDocPosition.put(Integer.valueOf(map1.get("key").toString()) ,map1.get("value").toString()));

        //获得phpDoc教务职称列表
        LambdaQueryWrapper<HmsModelField> wrapperEdu = new LambdaQueryWrapper<>();
        wrapperEdu.eq(HmsModelField::getModelId,2)
                .eq(HmsModelField::getField,"edu_position");
        HmsModelField PhpEdu = hmsModelFieldMapper.selectOne(wrapperEdu);
        String setting1 = PhpEdu.getSetting();
        Map map1 = JsonUtil.Json2Obj(setting1, Map.class);
        List<Map<Object,Object>> options2 =(List<Map<Object,Object>>)map1.get("options");
        options2.forEach(map3->phpEduPosition.put(  Integer.valueOf(map3.get("key").toString()),map3.get("value").toString()));

        //获得phpDoc教学岗位列表
        LambdaQueryWrapper<HmsModelField> wrapperEduPost = new LambdaQueryWrapper<>();
        wrapperEduPost.eq(HmsModelField::getModelId,2)
                .eq(HmsModelField::getField,"edu_post");
        HmsModelField PhpEduPost = hmsModelFieldMapper.selectOne(wrapperEduPost);
        String setting2 = PhpEduPost.getSetting();
        Map map2 = JsonUtil.Json2Obj(setting2, Map.class);
        List<Map<Object,Object>> options3 =(List<Map<Object,Object>>)map2.get("options");
        options3.forEach(map3->phpEduPost.put(  Integer.valueOf(map3.get("key").toString()),map3.get("value").toString()));

//        --------------------------------------------------------------------------------------------------
        //获得java2.0库数据列表
        DynamicDataSource.changeResourceDynamicDataSource();
        //获得javaDoc医师职称列表
        LambdaQueryWrapper<StationModelField2> wrapperDoc2 = new LambdaQueryWrapper<>();
        wrapperDoc2.eq(StationModelField2::getModelId,19)
                .eq(StationModelField2::getField,"doc_position");
        StationModelField2 PhpDoc2 = stationModelFieldMapper2.selectOne(wrapperDoc2);
        String javaSetting = PhpDoc2.getSetting();
        Map JavaSetting = JsonUtil.Json2Obj(javaSetting, Map.class);
        List<Map<Object,Object>> javaOptions = (List<Map<Object,Object>>)JavaSetting.get("options");
        javaOptions.forEach(map3->java2DocPosition.put(map3.get("label").toString(),Integer.valueOf(map3.get("value").toString())));

        //获得javaDoc教务职称列表
        LambdaQueryWrapper<StationModelField2> wrapperEdu2 = new LambdaQueryWrapper<>();
        wrapperEdu2.eq(StationModelField2::getModelId,19)
                .eq(StationModelField2::getField,"edu_post");
        StationModelField2 PhpEdu2 = stationModelFieldMapper2.selectOne(wrapperEdu2);
        String javaSetting1 = PhpEdu2.getSetting();
        Map JavaMap1 = JsonUtil.Json2Obj(javaSetting1, Map.class);
        List<Map<Object,Object>> javaOptions2 =(List<Map<Object,Object>>)JavaMap1.get("options");
        javaOptions2.forEach(map3->java2EduPosition.put( map3.get("label").toString() ,Integer.valueOf(map3.get("value").toString())));

        //获得javaDoc教学岗位列表
        LambdaQueryWrapper<StationModelField2> wrapperEduPost2 = new LambdaQueryWrapper<>();
        wrapperEduPost2.eq(StationModelField2::getModelId,19)
                .eq(StationModelField2::getField,"edu_post");
        StationModelField2 PhpEduPost2 = stationModelFieldMapper2.selectOne(wrapperEduPost2);
        String javaSetting2 = PhpEduPost2.getSetting();
        Map javaMap2 = JsonUtil.Json2Obj(javaSetting2, Map.class);
        List<Map<Object,Object>> javaOptions3 =(List<Map<Object,Object>>)javaMap2.get("options");
        javaOptions3.forEach(map3->java2EduPost.put( map3.get("label").toString(),Integer.valueOf(map3.get("value").toString())));
    }



    public void moveDoctorJavaMethod(Integer originCatId,Integer targetCatId) {

        DynamicDataSource.changeNewPhpDynamicDataSource();

        LambdaQueryWrapper<HmsCMans> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(HmsCMans::getCatid, originCatId)
                .eq(HmsCMans::getState, 0)
                .eq(HmsCMans::getStatus, 99)
                .orderByAsc(HmsCMans::getListorder)
                .orderByDesc(HmsCMans::getSort);
        List<HmsCMans> originMans = hmsCMansMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 6;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMDoctorData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCMans>> taskChunks = divideTasks(originMans, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMDoctorData2> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                }catch (Exception e){
                    System.err.println("解析错误！"+e.getMessage());
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMDoctorData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMDoctorData2> map : resultMaps) {
            mergedMap.putAll(map);
        }


        // 处理合并后的结果
        for (StationMReference targetMan : mergedMap.keySet()) {
            StationMDoctorData2 targetManData = mergedMap.get(targetMan);

            try {
                SaveToJava2(targetMan,targetManData,stationMDoctorDataMapper,targetCatId);
            }catch (Exception e){
                System.out.println("==========导入错误=========="+e.getMessage());
            }
        }
    }

    private Map<StationMReference, StationMDoctorData2> processTaskChunk2(
            List<HmsCMans> chunk, Integer targetCatId) {


        DynamicDataSource.changeNewPhpDynamicDataSource();

        Map<StationMReference, StationMDoctorData2> chunkResult = new LinkedHashMap<>();

        List<HmsCategory> HmsCategories =hmsCategoryMapper.selectList(null);
        Map<Integer, String> catMap = new HashMap<>();
        for (HmsCategory hmsCategory : HmsCategories ) {
            catMap.put(hmsCategory.getId().intValue(), hmsCategory.getName());
        }

        for (HmsCMans originMan : chunk) {
            DynamicDataSource.changeNewPhpDynamicDataSource();
            // 新的doctor
            StationMReference stationMDoctor = StationMReference.builder()
                    .catId(targetCatId)
                    .publishTime(Double.valueOf(originMan.getPublishTime() + "000"))
                    .state(99).uri("/" + CrawlerManager.randomCharacterGenerator() + ".html")
                    .isTop(0).build();


            // 找到修改前的manData并修改后存入新的doctorData
            LambdaQueryWrapper<HmsCMansData> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(HmsCMansData::getDid, originMan.getDataid());
            System.out.println(originMan);
            HmsCMansData originManData = hmsCMansDataMapper.selectOne(wrapper1);
            if (originManData==null)
                System.out.println("====================不存在===================");

            // 新的articleData
            StationMDoctorData2 stationMDoctorData = StationMDoctorData2.builder()
                    .uuid(UUID.randomUUID().toString()).title(originManData.getTitle())
                    .ignoreReason(null)
                    .createTime((double) System.currentTimeMillis()).updateTime((double) System.currentTimeMillis())
                    .createUserId(1).updateUserId(1)
                    .state(2).build();


            // 缩略图处理 （只有一张图片）
            String thumb = originManData.getThumb();
            // 调用接口
            if (!thumb.isBlank()) {
                thumb = CrawlerManager.DownLoad(thumb, fileName+"\\doctor" ,refer);
                thumb = CrawlerManager.changeFileUrl(new File(thumb));
            }
            stationMDoctorData.setThumb(thumb);

            // 处理正文,摘要
            String content = originManData.getContent();
            content = changeSpecialChat(content);
            Document document = Jsoup.parse(content);
            Elements imgList = document.select("img");
            Elements linkList = document.select("a");
            Elements videoList = document.select("video");
            // 图片路径集合处理
            try {
                for (Element element : imgList) {
                    String imgUrl = element.attr("src");
                    if (StringUtils.isNotBlank(imgUrl)) {
                        imgUrl = CrawlerManager.DownLoad(imgUrl, fileName+"\\doctor" ,refer);
                        if (StringUtils.isNotBlank(imgUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("图片下载失败,是资源id为:" + originManData.getDid());
            }

            // 链接路径集合处理
            try {
                for (Element element : linkList) {
                    String attachUrl = element.attr("href");
                    if (StringUtils.isNotBlank(attachUrl)) {
                        attachUrl = CrawlerManager.DownLoad(attachUrl, fileName+"\\doctor" ,refer);
                        if (StringUtils.isNotBlank(attachUrl)) {
                            String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                            element.attr("href", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("链接下载失败,是资源id为:" + originManData.getDid());
            }

            // 视频路径集合处理
            try {
                for (Element element : videoList) {
                    String videoUrl = element.attr("src");
                    if (StringUtils.isNotBlank(videoUrl)) {
                        videoUrl = CrawlerManager.DownLoad(videoUrl, fileName+"\\doctor" ,refer);
                        if (StringUtils.isNotBlank(videoUrl)) {
                            String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                            element.attr("src", url);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("视频下载失败,是资源id为:" + originManData.getDid());
            }
            content = document.toString();

            stationMDoctorData.setContent(content);

            //TODO  处理医师职称
            String protit = originManData.getDocPosition();
            if (protit!=null&&!protit.isEmpty()) {
                int code = Integer.parseInt(protit);
                stationMDoctorData.setDocPosition(java2DocPosition.get(phpDocPosition.get(code)));
            }
            //TODO 处理教务职称
            String eduPosition = originManData.getEduPosition();
            if (eduPosition!=null&&!eduPosition.isEmpty()) {
                int code = Integer.parseInt(eduPosition);
                stationMDoctorData.setEduPosition(java2EduPosition.get(phpEduPosition.get(code)));
            }
            //TODO 处理教务岗位
            String eduPost = originManData.getEduPost();
            if (eduPost!=null&&!eduPost.isEmpty()) {
                int code = Integer.parseInt(eduPost);
                stationMDoctorData.setEduPost(java2EduPost.get(phpEduPost.get(code)));
            }

            //TODO 处理擅长
            stationMDoctorData.setGoodat(originManData.getGoodat());
            // 处理科室
            //TODO 获取以前科室名称
            String depart = originManData.getDepart();
            if (!depart.isEmpty()) {
                String[] departs = depart.split(",");
                List<String> departNames = new ArrayList<>();
                List<Integer> newDepartIds = new ArrayList<>();
                for (int i = 0; i < departs.length; i++) {
                    departNames.add(catMap.get(Integer.parseInt(departs[i])));
                }
                //TODO 通过科室名称匹配现在科室id
                DynamicDataSource.changeBuildDynamicDataSource();
                LambdaQueryWrapper<StationCategory> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(StationCategory::getName, departNames)
                        .eq(StationCategory::getType,4);
                List<StationCategory> stationCategories = stationCategoryMapper.selectList(wrapper);
                for (StationCategory stationCategory : stationCategories) {
                    newDepartIds.add(stationCategory.getId());
                }
                stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
            }

            //TODO 处理观看数
            stationMDoctor.setViews(originManData.getViews());
            //TODO  处理发布时间
            stationMDoctorData.setCreateTime(Double.valueOf(originMan.getPublishTime() + "000"));
            stationMDoctorData.setUpdateTime(Double.valueOf(originMan.getPublishTime() + "000"));

            chunkResult.put(stationMDoctor, stationMDoctorData);
        }

        return chunkResult;
    }




    @Test
    public void moveImageJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        Map<Integer, Integer> map = new HashMap<>();

        //获得当前catID以及目标catID
//        getImageID(map);
        map.put(1026,924);

        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveImageJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveDoctorJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i]+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getImageID(Map<Integer, Integer> map){
        DynamicDataSource.changeNewPhpDynamicDataSource();

        //找到存在的数据所对应的originCatId
        LambdaQueryWrapper<HmsCImage> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.select(HmsCImage::getCatid)
                .eq(HmsCImage::getStatus,99)
                .eq(HmsCImage::getState,0);
        List<Integer> originList = hmsCImageMapper.selectObjs(wrapper1);
        List<Integer> DistinctList = originList.stream().distinct().collect(Collectors.toList());

        //根据originCatId 找出对应的CategoryName为后续找到targetId做准备
        LambdaQueryWrapper<HmsCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(HmsCategory::getId,DistinctList);
        wrapper.select(HmsCategory::getName,HmsCategory::getId);
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper);

        //根据categoryName找到targetID
        for (HmsCategory hmsCategory : hmsCategories) {
            DynamicDataSource.changeBuildDynamicDataSource();
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getName, hmsCategory.getName());
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);
            if (stationCategory2 != null)
                map.put(Math.toIntExact(hmsCategory.getId()), stationCategory2.getId());
        }

    }



    public void moveImageJavaMethod(Integer originCatId,Integer targetCatId){
        DynamicDataSource.changeNewPhpDynamicDataSource();
//        Integer originCatId=116;
//        Integer targetCatId=23;
        LambdaQueryWrapper<HmsCImage> wrapper =new LambdaQueryWrapper<>();
        //TODO 找到所有存在的图集消息
        wrapper.eq(HmsCImage::getCatid, originCatId)
                .eq(HmsCImage::getState, 0)
                .eq(HmsCImage::getStatus,  99)
                .orderByAsc(HmsCImage::getListorder);
        List<HmsCImage> originImages = hmsCImageMapper.selectList(wrapper);

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMImageData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCImage>> taskChunks = divideTasks(originImages, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMImageData2> chunkResult =
                            processTaskChunk3(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMImageData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMImageData2> map : resultMaps) {
            mergedMap.putAll(map);
        }


        // 处理合并后的结果
        for (StationMReference targetImage : mergedMap.keySet()) {
            StationMImageData2 targetImageData = mergedMap.get(targetImage);
            try {
                SaveToJava2(targetImage,targetImageData,stationMImageDataMapper,targetCatId);
            }catch (Exception e){
                System.out.println("=========导入失败=========="+e.getMessage());
            }
        }
    }
    private Map<StationMReference, StationMImageData2> processTaskChunk3(
            List<HmsCImage> chunk, Integer targetCatId){
        Map<StationMReference, StationMImageData2> chunkResult=new LinkedHashMap<>();

        try {
            for (HmsCImage originImage : chunk) {
                //TODO 新的image
                StationMReference stationMImage = StationMReference.builder()
                            .catId(targetCatId).publishTime(Double.valueOf(originImage.getPublishTime()+"000"))
                            .state(99).uri("/"+ CrawlerManager.randomCharacterGenerator()+".html")
                            .isTop(0).build();


                //TODO 找到修改前的imageData并修改存入新的imageData
                DynamicDataSource.changeNewPhpDynamicDataSource();
                LambdaQueryWrapper<HmsCImageData> wrapper1=new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCImageData::getDid, originImage.getDataid());
                HmsCImageData originImageData=hmsCImageDataMapper.selectOne(wrapper1);

                //TODO 新的imageData
                StationMImageData2 stationMImageData = StationMImageData2.builder()
                                    .uuid(UUID.randomUUID().toString())
                                    .title(originImageData.getTitle())
                                    .createTime(Double.valueOf(originImage.getPublishTime()+"000"))
                                    .updateTime(Double.valueOf(originImage.getPublishTime()+"000"))
                                    .createUserId(1).updateUserId(1)
                                    .state(2).build();


                //TODO 处理访问量
                stationMImage.setViews(originImageData.getViews());
                //TODO 处理作者
                String author =originImageData.getAuthor();
                author = changeSpecialChat(author);
                if(author != null &&!author.equals("[]")&&!author.isEmpty()){
                    stationMImageData.setAuthor(author);
                }
                //TODO 处理摄影师
                String photographer =originImageData.getPhotographer();
                if(photographer  != null &&!photographer.equals("[]")&&!photographer.isEmpty()){
                    photographer = changeSpecialChat(photographer);
                    stationMImageData.setPhotographer(photographer);
                }
                //TODO 处理来自
                String comeFrom =originImageData.getComefrom();
                if(comeFrom != null &&!comeFrom.isEmpty()){
                    comeFrom = changeSpecialChat(comeFrom);
                    stationMImageData.setComefrom(comeFrom);
                }
                //TODO 处理images
                String images =originImageData.getImages();
                JSONArray jsonArray =  JSONArray.parseArray(images);
                if(jsonArray!=null&&!jsonArray.isEmpty()){
                    //TODO 单图片的图集
                    if(jsonArray.size()==1){
                        JSONObject item =jsonArray.getJSONObject(0);
                        //TODO 设置图集时间戳
                        long currentTimeMillis = System.currentTimeMillis();
                        item.put("id",currentTimeMillis);
                        //TODO 获取url图片进行替换
                        String url = item.getString("url");
                        if(url!=null&&!url.isBlank()){
                            url=CrawlerManager.DownLoad(url,"C:\\Collect_Data\\lsz120","http://lsz120.web24.foxtest.net/");
                            url=CrawlerManager.changeFileUrl(new File(url));
                        }
                        item.put("url", url);
                        //TODO 设置图片标题
                        item.put("title",originImageData.getTitle()+".jpg");
                        //TODO 获取description并解码Unicode转义字符
                        String description=item.getString("description");
                        if(description!=null&&!description.isEmpty()){
                            String decodedDescription = decodeUnicode(description);
                            decodedDescription=StringUtils.abbreviate(decodedDescription, 11);
                            item.put("description", decodedDescription);
                        }
                    }
                    JSONObject  dbItem=jsonArray.getJSONObject(0);
                    JSONArray jsonArray1=new JSONArray();
                    jsonArray1.add(dbItem);
                    stationMImageData.setImages(jsonArray1.toJSONString());
                }
                //TODO 处理发布时间
//                stationMImageData.setPublishTime(Double.valueOf(originImage.getPublishTime()+"000"));
                chunkResult.put(stationMImage, stationMImageData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }

        return chunkResult;
    }

    public <T extends BaseMapper> void SaveToJava2(
            StationMReference stationMReference,
            Object data,
            T dataMapper,
            Integer targetCatId) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // 先加入资源数据
        DynamicDataSource.changeBuildDynamicDataSource();
        stationMReferenceMapper.insert(stationMReference);
        LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(StationCategory2::getId,targetCatId);
        StationCategory2 stationCategory2 = stationCategoryMapper2.selectOne(wrapper2);

        //导入resource_data数据
        DynamicDataSource.changeResourceDynamicDataSource();
        dataMapper.insert(data);

        //由于data数据类型不固定，故而通过反射来获取getDataId()这个方法
        Class<?> clazz = data.getClass();
        Method method = clazz.getMethod("getDataId");
        System.out.println(method);
        System.out.println(method.invoke(data));
        Long dataId = Long.valueOf(method.invoke(data).toString());


        //落库 -> folder_resource
        FolderResource folderResource= FolderResource.builder()
                .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                .resourceId(dataId)
                .createTime((double) System.currentTimeMillis())
                .updateTime((double) System.currentTimeMillis())
                .version(1).sort(dataId)
                .listOrder(0).state(2).isDeleted(0).build();
        ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

        // 引用数据引用资源数据id
        stationMReference.setDataId(Long.valueOf(folderResource.getId()));
        stationMReference.setSortLevel(Math.toIntExact(stationMReference.getId()));

        DynamicDataSource.changeBuildDynamicDataSource();
        stationMReferenceMapper.updateById(stationMReference);


    }





    @Test
    public void movePagesJava() {
        DynamicDataSource.changeNewPhpDynamicDataSource();

        Map<Integer, Integer> map = new HashMap<>();

        getDescribePageID(map);


        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                movePagesJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("movePagesJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("成功执行的栏目id：");
        for (int i = 0; i < count; i++) {
            System.out.print(success[i]+" ");
        }
        System.out.println("总计："+success.length+"个栏目");
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }




    public void getDescribePageID(Map<Integer, Integer> map){
        // 找到所有科室的科室简介
        LambdaQueryWrapper<HmsCategory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(HmsCategory::getModelId,3)
                .eq(HmsCategory::getName,"科室简介");
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper1);
        Integer[] origin = new Integer[hmsCategories.size()];

        String[] DepartName = new String[origin.length];
        for (int i = 0; i < hmsCategories.size(); i++) {
            origin[i]= Math.toIntExact(hmsCategories.get(i).getId());

            LambdaQueryWrapper<HmsCategory> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(HmsCategory::getId,hmsCategories.get(i).getPid());
            DepartName[i]=hmsCategoryMapper.selectOne(wrapper2).getName();
        }

        //根据DepartName一致找到targetID
        DynamicDataSource.changeBuildDynamicDataSource();
        for (int i = 0; i < origin.length; i++) {
            LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationCategory2::getName,DepartName[i]);
            System.out.println(stationCategoryMapper2.selectList(wrapper));
            StationCategory2 stationCategory2 = stationCategoryMapper2.selectList(wrapper).get(0);
            if (stationCategory2==null)
                continue;

            LambdaQueryWrapper<StationCategory2> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(StationCategory2::getPid,stationCategory2.getId())
                    .eq(StationCategory2::getName,"科室简介");
            StationCategory2 stationCategory = stationCategoryMapper2.selectOne(wrapper3);
            if (stationCategory==null)
                continue;
            map.put(origin[i],stationCategory.getId());
        }
        System.out.println(map);
    }


    public void movePagesJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeNewPhpDynamicDataSource();
        LambdaQueryWrapper<HmsCPages> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息与判断是否为发布状态
        wrapper.eq(HmsCPages::getCatid, originCatId)
                .eq(HmsCPages::getState, 0)
                .eq(HmsCPages::getStatus, 99)
//                .gt(HmsCPages::getPublishTime,1731600000)    // TODO 临时需要更新新数据
                .orderByAsc(HmsCPages::getListorder);

        List<HmsCPages> originPages = hmsCPagesMapper.selectList(wrapper);
        if (originPages.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 4;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMPageData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<HmsCPages>> taskChunks = divideTasks(originPages, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMPageData2> chunkResult =
                            processTaskChunk4(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMPageData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMPageData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMPageData2 targetNewData = mergedMap.get(targetNew);
            try {
                SaveToJava2(targetNew,targetNewData,stationMPageDataMapper,targetCatId);
            }catch (Exception e){
                System.out.println("==========导入错误=========="+e.getMessage());
            }
        }
    }



    /**
     * 处理一个任务块
     */
    private Map<StationMReference, StationMPageData2> processTaskChunk4(
            List<HmsCPages> chunk, Integer targetCatId) {
        Map<StationMReference, StationMPageData2> chunkResult = new LinkedHashMap<>();
        try {
            for (HmsCPages originNew : chunk) {
                // 新的news
                StationMReference stationMPage = StationMReference.builder()
                        .catId(targetCatId)
                        .publishTime(Double.valueOf(originNew.getPublishTime() + "000"))
                        .state(99).uri("/" + CrawlerManager.randomCharacterGenerator() + ".html")
                        .isTop(0).build();

                String linkUrl = originNew.getIslink();
                int isLink = 0;
                if (linkUrl!=null && !linkUrl.isEmpty()) {
                    isLink = 1;
                }

                DynamicDataSource.changeNewPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<HmsCPagesData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(HmsCPagesData::getDid, originNew.getDataid());
                HmsCPagesData originPagesData = hmsCPagesDataMapper.selectOne(wrapper1);

                //处理访问量
                stationMPage.setViews(originPagesData.getViews());
                // 新的articleData
                StationMPageData2 stationMPageData = StationMPageData2.builder()
                        .uuid(UUID.randomUUID().toString())
                        .title("科室简介")
                        .createUserId(1).updateUserId(1)
                        .state(2).build();

                // 处理正文,摘要
                String content = originPagesData.getContent();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");


                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String imgUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {

                                imgUrl = CrawlerManager.DownLoad(imgUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    if (url.isEmpty())
                                        continue;
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originPagesData.getDid());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("href");
//                            String attachUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("weixin")){
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    if (url.isEmpty())
                                        continue;
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originPagesData.getDid());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            // TODO 临时特殊处理
//                            String url1 = element.attr("src");
//                            String videoUrl = url1.replace( url1.substring(0, url1.indexOf("oss")),"https://www.jrha.net.cn/");

                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, fileName+"\\news" ,refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    if (url.isEmpty())
                                        continue;
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originPagesData.getDid());
                    }
                    content = document.body().children().toString();
                } else {
                    // 是链接的话,content为链接
                    content = linkUrl;
                }
                stationMPageData.setContent(content);

                //TODO 处理来源
                stationMPageData.setComefrom(originPagesData.getComefrom());
                //TODO 处理是否为外联
                stationMPageData.setIsLink(isLink);
                //TODO 处理发布时间
                stationMPageData.setCreateTime(Double.valueOf(originNew.getPublishTime() + "000"));
                stationMPageData.setUpdateTime(Double.valueOf(originNew.getPublishTime() + "000"));
                chunkResult.put(stationMPage, stationMPageData);
            }
        } catch (Exception e) {
            log.error("解析出错", e);
        }
        return chunkResult;
    }






    //切换特殊字符
    public String changeSpecialChat(String text) {
        if (text==null)
            return null;
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }
    /**
     * 解码 Unicode 转义字符，如 \u5e74 -> 年
     */
    public static String decodeUnicode(String input) {
        int length = input.length();
        StringBuilder out = new StringBuilder();

        for (int i = 0; i < length; i++) {
            char c = input.charAt(i);
            if (c == '\\' && i + 5 < length && input.charAt(i + 1) == 'u') {
                String hex = input.substring(i + 2, i + 6);
                try {
                    out.append((char) Integer.parseInt(hex, 16));
                    i += 5;
                } catch (NumberFormatException e) {
                    out.append(c);
                }
            } else {
                out.append(c);
            }
        }

        return out.toString();
    }


    /**
     * @description:
     * @author: RXH
     * @date: 2025/8/5 15:12
     * @param:
     * @return:
     **/

    public void cd5120(){

//        map.put(515,413);   //新闻中心-健康科普


//        map.put(112,10);   // 历史沿革
//        map.put(114,12);   // 医院文化
//        map.put(122,20);   // 招贤纳士


//        map.put(155,53);   // 党史学习教育
//        map.put(156,54);   // 专项治理群众身边“可视”“有感”腐败和作风问题
//        map.put(157,55);   // 成都市医疗质量控制中心
//        map.put(158,56);   // 法治宣传
//        map.put(505,403);   // 监督电话
//        map.put(506,404);   // 医院基本数据
//        map.put(507,405);   // 职工
//        map.put(508,406);   // 科室
//        map.put(509,407);   // 设备环境
//        map.put(514,412);   // 检验检查

        //          oldPhp已经导入
//        map.put(4,6);   // 省政府信息
//        map.put(118,16);   // 医院动态
//        map.put(119,17);   // 院务公开
//        map.put(120,18);   // 招标采购
//        map.put(121,19);   // 媒体报道
//        map.put(128,26);   // 患者须知
//        map.put(138,36);   // 廉政建设
//        map.put(139,37);   // 青春风采

//        map.put(141,39);   // 劳模风采

//        map.put(146,44);   // 伦理委员会动态
//        map.put(150,48);   // 机构动态
//        map.put(159,57);   // 住培专栏
//        map.put(513,411);   // 进修申请

    }

    /**
     * @description:绵阳中医院栏目列表
     * @author: RXH
     * @date: 2025/8/1 10:26
     * @param:
     * @return:
     **/
    public void myzyyNewsId(){
        HashMap<Integer,Integer> map = new HashMap<>();
//        map.put(123,20); //通知公告

        map.put(437,335); //院庆动态
        map.put(436,334); //通知公告

        map.put(126,24); //招聘公告
        map.put(590,488); //招聘岗位
        map.put(583,481); //招聘信息

        map.put(553,451); //学术动态
        map.put(554,452); //科研管理
        map.put(555,453); //科研服务
        map.put(556,454); //GCP

        map.put(591,489); //进修培训
        map.put(594,492); //成人教育

        map.put(592,381); //招募公告
        map.put(444,345); //法律法规
        map.put(443,346); //工作动态
        map.put(1033,931); //沈其霖名医传承工作室-临床医案
        map.put(572,470);//叶海燕名医传承工作室-工作室动态
        map.put(596,494); //医保业务办理流程
    }
}
