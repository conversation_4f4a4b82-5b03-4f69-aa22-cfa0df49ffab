package com.ruifox.collect.dataMigration.news;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station2.StationCategoryMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMArticleDataMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMReferenceMapper;
import com.ruifox.collect.dao.mapper.tbl_special.TblSpecialCDataMapper;
import com.ruifox.collect.dao.mapper.tbl_special.TblSpecialContentMapper;
import com.ruifox.collect.dao.mapper.tbl_special.TblSpecialMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.module.entity.station2.StationMArticleData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.module.entity.tbl_special.TblSpecialCData;
import com.ruifox.collect.module.entity.tbl_special.TblSpecialContent;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class ImportSpecialToJava2Test {
    @Autowired
    private TblSpecialContentMapper tblCNewsMapper;
    @Autowired
    private TblSpecialCDataMapper tblCNewsDataMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationCategoryMapper2 stationCategoryMapper;
    @Autowired
    private TblSpecialMapper tblCategoryMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;

    @Value("${testDownload.config.refer}")
    private String refer;

    @Value("${testDownload.config.fileName}")
    private String downloadPath;

    @Test
    public void moveNewsJava() {
        Integer id = 0;
        Map<Integer, Integer> map = new HashMap<>();
        map.put(29,53);   // 党纪学习教育
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }


    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblSpecialContent> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblSpecialContent::getSpecialid, originCatId)
                .eq(TblSpecialContent::getState, 0)
                .orderByAsc(TblSpecialContent::getListorder);
        List<TblSpecialContent> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 5;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblSpecialContent>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMArticleData2> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMArticleData2> mergedMap = new LinkedHashMap<>(1000);
        for (Map<StationMReference, StationMArticleData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMArticleData2 targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetNew);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMArticleDataMapper.insert(targetNewData);
            //落库 -> folder_resource
            new FolderResource();
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetNewData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetNewData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetNew.setDataId(Long.valueOf(folderResource.getId()));
            targetNew.setSortLevel(Math.toIntExact(targetNew.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetNew);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start=end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMReference, StationMArticleData2> processTaskChunk(
            List<TblSpecialContent> chunk, Integer targetCatId) {
        Map<StationMReference, StationMArticleData2> chunkResult = new LinkedHashMap<>();
        try {
            for (TblSpecialContent originNew : chunk) {
                // 新的article
                StationMReference stationMArticle = new StationMReference();
                stationMArticle.setCatId(targetCatId);
                stationMArticle.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));
                stationMArticle.setState(99);
                stationMArticle.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMArticle.setIsTop(0);


                String linkUrl = originNew.getUrl();
                int isLink = originNew.getIslink();
                // TODO 根据url判断是否为外链(具体网站具体分析)
//                if (linkUrl.contains("myzyy")) {
//                    isLink = 0;
//                }

                DynamicDataSource.changeOldPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblSpecialCData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblSpecialCData::getId, originNew.getId());
                TblSpecialCData originNewsData = tblCNewsDataMapper.selectOne(wrapper1);
                if (originNewsData==null)
                    continue;

                // 新的articleData
                StationMArticleData2 stationMArticleData = new StationMArticleData2();
                stationMArticleData.setUuid(UUID.randomUUID().toString());
                stationMArticleData.setTitle(originNew.getTitle());
                stationMArticleData.setIgnoreReason(null);
                stationMArticleData.setPhotographer(null);
                stationMArticleData.setCreateUserId(1);
                stationMArticleData.setUpdateUserId(1);
                stationMArticleData.setCreateTime(Double.valueOf(originNew.getInputtime()+"000"));
                stationMArticleData.setUpdateTime(Double.valueOf(originNew.getUpdatetime()+"000"));
                stationMArticleData.setState(2);
                // 处理作者
                String input = originNewsData.getAuthor();

                // TODO 根据实际处理
                if (input!=null&&!input.isBlank()&&!input.isEmpty()){
                    input = changeSpecialChat(input);
                    String author = leaveChinese(input);
                    System.out.println(author);
                    if (!author.equals("0") && !author.isEmpty())
//                    stationMArticleData.setAuthor(originNewsData.getAuthor());
                        stationMArticleData.setAuthor(author);
                }

                // 缩略图处理 （只有一张图片）
                String thumb = originNew.getThumb();
                // 调用接口
                if (thumb!=null&&!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    if (!thumb.isBlank()){
                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    }
                }
                stationMArticleData.setThumb(thumb);
                System.out.println("===============处理完头像============");
                // 处理正文,摘要
                String content = originNewsData.getContent();
                content = changeSpecialChat(content);
                String description = originNew.getDescription();
                if (isLink == 0) {
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");
                            if (imgUrl.contains(".com")||imgUrl.contains(".cn")){
                                continue;
                            }
                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                    //没有头像就选择第一张图片为头像
                                    if (stationMArticleData.getThumb().isBlank()){
                                        stationMArticleData.setThumb(url);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getId());
                    }
                    content = document.toString();
                } else {
                    // 是链接的话,content为空
                    content = linkUrl;
                    description = linkUrl;
                }
                System.out.println("===============处理完content============");
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                // 处理来源
                stationMArticleData.setComefrom(originNew.getComefrom());
                // 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                // 处理发布时间
//                stationMArticleData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }

        return chunkResult;
    }




    //手动转义
    public String changeContent(String text) {
        return text.replace("&quot;", "\"");
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }

    @Test
    public void testChangeChat(){
        String test = "&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302line-height:15.96px&#300&#302>我院现对下列项目征集相关市场调研资料，请符合相关项目要求且具有合法合格资质的公司或厂家积极参加本次调研。&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>一、调研项目清单：&#305span style&#304&#302color:#ff0000&#300&#302>详见调研报名网站&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>二、注意事项&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>1.请参与调研的供应商或厂家于&#305span style&#304&#302color:#ff0000&#300&#302>2025年7月29&#305/span>日前，按报名操作手册（见附件1）进行线上报名，并线上填写信息采集内容。请各供应商或厂家准确填写相关信息，并对所填信息的真实有效性负责,如发现所填报信息虚假，则此次调研报名作无效处理。&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>2.产品彩页&#305span style&#304&#302color:#ff0000&#300&#302>：上传彩页请根据挂网调研设备名称逐一命名，保持与挂网调研设备名称一致&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302color:#ff0000&#300&#302>3.产品技术参数表（附件2）：请下载公告附件3模板按要求如实填写，此表必须上传PDF盖章版以及可编辑的word版，共两种文档&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302color:#ff0000&#300&#302>4.市场同类产品参数对照表（附件3）：请下载公告附件4模板如实填写并上传&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>三、联系人&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>医学工程部 &nbsp&#300 沈老师 &nbsp&#300 028-62346182&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>四、报名方式&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>1、线上报名&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>2、报名链接：&#305a href&#304&#302https://mk.cd5120.com:28083/admin/login&#302 data_ue_src&#304&#302https://mk.cd5120.com:28083/admin/login&#302>&#305span style&#304&#302color:#0000ff&#300&#302>https://mk.cd5120.com:28083/admin/login&#305/span>&#305/a> &#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>3、报名操作手册：详见附件2&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305br />&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20250722/134226259.docx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20250722/134226259.docx&#302>调研项目清单.docx&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240130/101429228.pptx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240130/101429228.pptx&#302>附件1：供应商报名操作手册.pptx&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240521/104956502.doc&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240521/104956502.doc&#302>附件2：产品技术参数表.doc&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240521/104956616.xlsx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240521/104956616.xlsx&#302>附件3：市场同类产品参数对照表.xlsx&#305/a>&#305/p>&#305p>&#305br />&#305/p>";
        System.out.println(changeSpecialChat(test));
    }

    @Test
    public void test() {
        String Refer = "http://jrha.lan24.foxtest.net/";
        String s = CrawlerManager.DownLoad("http://jrha.1024199.foxtest.net/oss/20241203/115322689_2000_0_50.jpg", downloadPath, Refer);
        String s1 = CrawlerManager.changeFileUrl(new File(s));
        log.info(s);
        log.info(s1);
        System.out.println(s);
    }

    @Test
    public void test2() {
        String a = "姓名：徐俊波&#305/br>\n" +
                "联合培养研究生学校：西南交通大学、川北医学院&#305/br>\n" +
                "当前指导研究生层次：硕士研究生&#305/br>\n" +
                "研究方向：高血压，心力衰竭等心血管病的药物器械治疗&#300医院管理&#305/br>\n" +
                "邮箱：<EMAIL>";
        a = changeSpecialChat(a);
        a = a.replaceAll("</br>", "");
        String[] split = a.split("\n");
        String name = "";
        String school = "";
        String level = "";
        String direction = "";
        String mail = "";
        for (String s : split) {
            if (s.contains("姓名")) {
                name = s.split("：")[1];
            } else if (s.contains("联合培养研究生学校")) {
                school = s.split("：")[1];
            } else if (s.contains("当前指导研究生层次")) {
                level = s.split("：")[1];
            } else if (s.contains("研究方向")) {
                direction = s.split("：")[1];
            } else if (s.contains("邮箱")) {
                mail = s.split("：")[1];
            }
        }
        System.out.println(name);
        System.out.println(school);
        System.out.println(level);
        System.out.println(direction);
        System.out.println(mail);
    }

    public String leaveChinese(String input){
        input = input.replaceAll("[^\\u4e00-\\u9fa5,、；; （/）]", "");
        while (input.charAt(input.length()-1)==','||input.charAt(input.length()-1)=='、'||input.charAt(input.length()-1)=='；'||input.charAt(input.length()-1)==';'){
            input=input.substring(0,input.length()-1);
        }
        return input;
    }
    @Test
    public void testFilter(){
        String input = "array (\n" +
                "  0 => ' 陈静)',\n" +
                "  1 => ' ',\n" +
                "  2 => ' ',\n" +
                "  3 => ' ',\n" +
                "  4 => ' ',\n" +
                "  5 => ' ',\n" +
                "  6 => ' ',\n" +
                "  7 => ' ',\n" +
                "  8 => ' ',\n" +
                "  9 => ' ',\n" +
                "  10 => ' ',\n" +
                "  11 => ' ',\n" +
                "  12 => ' ',\n" +
                ")";
        String input2 = "array &#303\n" +
                "  0 &#304> &#301伍黎黎&#301,\n" +
                "  1 &#304> &#301刘小娟&#301,\n" +
                "  2 &#304> &#301江咏梅&#301,\n" +
                ")";
        String input3 = "&#301李知默&#301刘小娟&#301江咏梅、&#301刘小娟&#301";
        String input4 = "&#305p style&#304&#302text-align:left&#300text-indent:2em&#300&#302>&#305span style&#304&#302color:#333333&#300letter-spacing:0px&#300font-style:normal&#300background:#ffffff&#300&#302>&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302> &#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>又到粽叶飘香时，端午节应时而来。为进一步弘扬传统文化，丰富&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>退休&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>职工的&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>精神&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>文化生活&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>，传递组织关爱，&#305/span>2025年5月28日，医院人力资源部退休办公室代表医院组织举办了退休职工端午主题活动，本次活动共计参与人数200余人&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>医院共组织10余位志愿者为退休职工贴心服务，本次主题活动精心为退休职工准备了“书香阅读角”，发放《离退休工作简报》《枫华》《心系下一代》等相关杂志，让老师们了解国家、学校退休工作的新动向，分享退休生活心得，传递读书的喜悦。医院退休办公室精心编撰的《退休一本通》凝聚了医院的关爱，小礼品国风团扇也为退休老师们送来了缕缕夏日清风和对传统节日美好心思的寄托。&#305/p>&#305p style&#304&#302text-align:center&#302>&#305img src&#304&#302https://oss.motherchildren.com/20250529/154902303.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/154902303.png&#302 title&#304&#3025ddf839e6ad9a0d47798bad3fd2e193&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305img src&#304&#302https://oss.motherchildren.com/20250529/155013977.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155013977.png&#302 title&#304&#302986a08bef941191ead36debe4a2cbdd&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305/p>&#305p style&#304&#302text-align:center&#302>&#305img src&#304&#302https://oss.motherchildren.com/20250529/155014881.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155014881.png&#302 title&#304&#3025789ddb8722c7da3278e3e25c6cb5d8&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305img src&#304&#302https://oss.motherchildren.com/20250529/155013769.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155013769.png&#302 title&#304&#30289ca6a03218f59695317ea1cbbc910f&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>端午临仲夏，时清日复长。活动当天，老朋友、老同事们欢乐相聚拍照留念，或座谈共话安康，或读书细品书香。退休办公室张静主任代表医院领导向大家表达了医院对退休职工的关心关爱，祝福大家&#305span style&#304&#302color:#000000&#300&#302>老有所乐，共享幸福！并传达了近期&#305/span>学校、医院工作重点，及时让退休职工了解学校、医院建设发展的新动向。&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>退休老师们感谢医院组织端午主题活动，既传承了端午传统文化，又增进了老同志之间的交流与情谊，大家都深切感受到组织的温暖关怀，归属感和幸福感进一步提升，表示期待下一次的相聚！&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>&#305br style&#304&#302text-indent:2em&#300text-align:left&#300&#302 />&#305/p>";

        input = changeSpecialChat(input);
        input2 = changeSpecialChat(input2);
        input3 = changeSpecialChat(input3);
        input4 = changeSpecialChat(input4);
        // TODO 根据实际处理
//        input = leaveChinese(input);
//        input2 = leaveChinese(input2);
//        System.out.println(input);
//        System.out.println(input2);
//        System.out.println(input3);
        System.out.println(input4);

    }

    private Map<Integer, Integer> getDocPositionMap() {
        Map<Integer, Integer> titleLevelMap = new HashMap<>();

        // 医师系列  TODO 根据实际调整
        titleLevelMap.put(20, 1);  // 主任医师
        titleLevelMap.put(16, 2);  // 副主任医师
        titleLevelMap.put(12, 5);  // 主治医师
        titleLevelMap.put(8, 15); // 医师
//        titleLevelMap.put(8, 12); // 住院医师
        titleLevelMap.put(4, 13); // 医士

        // 心理咨询师系列
        titleLevelMap.put(96, 14); // 一级心理咨询师
        titleLevelMap.put(86, 15); // 二级心理咨询师
        titleLevelMap.put(76, 16); // 三级心理咨询师

        // 护师系列
        titleLevelMap.put(19, 8);  // 主任护师
        titleLevelMap.put(15, 9);  // 副主任护师
        titleLevelMap.put(11, 10);  // 主管护师
        titleLevelMap.put(7, 17); // 护师
        titleLevelMap.put(3, 18); // 护士

        // 技师系列
        titleLevelMap.put(17, 0); // 主任技师
        titleLevelMap.put(13, 20); // 副主任技师
        titleLevelMap.put(9, 21); // 主管技师
        titleLevelMap.put(5, 22); // 技师
        titleLevelMap.put(1, 23); // 技士

        // 药师系列
        titleLevelMap.put(18, 6);  // 主任药师
        titleLevelMap.put(14, 7);  // 副主任药师
        titleLevelMap.put(10, 24); // 主管药师
        titleLevelMap.put(6, 25); // 药师
        titleLevelMap.put(2, 26); // 药士

        return titleLevelMap;
    }

}
