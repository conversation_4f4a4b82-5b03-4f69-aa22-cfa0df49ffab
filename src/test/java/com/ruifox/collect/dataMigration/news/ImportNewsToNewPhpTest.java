package com.ruifox.collect.dataMigration.news;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.tbl_special.TblSpecialCDataMapper;
import com.ruifox.collect.dao.mapper.tbl_special.TblSpecialContentMapper;
import com.ruifox.collect.dao.mapper.tbl_special.TblSpecialMapper;
import com.ruifox.collect.dao.mapper.tbl_special.TblTypeMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.hms.HmsCNews;
import com.ruifox.collect.module.entity.hms.HmsCNewsData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.tbl_special.TblSpecial;
import com.ruifox.collect.module.entity.tbl_special.TblSpecialCData;
import com.ruifox.collect.module.entity.tbl_special.TblSpecialContent;
import com.ruifox.collect.module.entity.tbl_special.TblType;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.ElementUtil;
import com.ruifox.collect.util.FileUtil;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import us.codecraft.webmagic.selector.Html;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class ImportNewsToNewPhpTest{
    @Autowired
    private TblSpecialMapper tblSpecialMapper;
    @Autowired
    private TblSpecialCDataMapper tblSpecialCDataMapper;
    @Autowired
    private TblSpecialContentMapper tblSpecialContentMapper;
    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;
    @Autowired
    private HmsCNewsDataMapper hmsCNewsDataMapper;
    @Autowired
    private HmsCNewsMapper hmsCNewsMapper;
    @Autowired
    private TblTypeMapper tblTypeMapper;


    @Test
    public void moveNews(){
        Map<Integer,Integer> map = new HashMap<>();
//        map.put(10,680); //群众路线-工作动态
        getNewsID(map);
        int id = -1;
        for (Integer originCatId : map.keySet()) {
            try {
                HashMap<Integer, Integer> typeID = getTypeID(originCatId, map.get(originCatId));
                for (Integer type : typeID.keySet()) {
                    moveNewsMethod(type,originCatId, typeID.get(type));
                }
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != -1) {
            log.info("运行到id为: " + id + " 报错");
        }

    }


    @Test
    public void testInsert(){
        moveNewsMethod(229,13,693); //优质护理服务示范工程
        moveNewsMethod(252,18,709); //临床医学专业认证

        moveNewsMethod(250,17,707);   //志愿服务在医院-体会

        moveNewsMethod(261,21,671); //不忘初心、牢记使命
        moveNewsMethod(267,22,670); //众志成城 抗击疫情

        moveNewsMethod(271,25,658); //党史学习教育-公告▪新闻
        moveNewsMethod(272,25,660); //党史学习教育-党史百年·天天读

        moveNewsMethod(302,30,586); //党纪学习教育-学习问答
        moveNewsMethod(303,30,590); //党纪学习教育-漫画说纪
        moveNewsMethod(304,30,591); //党纪学习教育-视频解读
        moveNewsMethod(304,30,589); //党纪学习教育-图解法纪


        moveNewsMethod(291,28,645);   //学习贯彻习近平新时代中国特色社会主义思想主题教育-要闻速递
        moveNewsMethod(292,28,646);   //学习贯彻习近平新时代中国特色社会主义思想主题教育-党支部动态
        moveNewsMethod(293,28,647);   //学习贯彻习近平新时代中国特色社会主义思想主题教育-重要著作
        moveNewsMethod(294,28,648);   //学习贯彻习近平新时代中国特色社会主义思想主题教育-媒体聚焦


    }

    public void getNewsID(Map<Integer,Integer> map){
        DynamicDataSource.changeHandleDynamicDataSource();

        //from CNews get catId collection
        LambdaQueryWrapper<TblSpecialContent> catIDWrapper = new LambdaQueryWrapper<>();
        catIDWrapper.select(TblSpecialContent::getSpecialid);
        List<Integer> specialIds = tblSpecialContentMapper.selectObjs(catIDWrapper);
        List<Integer> distinctSpecial = specialIds.stream().distinct().collect(Collectors.toList());

        LambdaQueryWrapper<TblSpecial> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TblSpecial::getId,TblSpecial::getTitle)
                .in(TblSpecial::getId,distinctSpecial);
        List<TblSpecial> tblSpecials = tblSpecialMapper.selectList(wrapper);

        //未找到的targetID（手动加）
        List<Integer> failure = new ArrayList<>();

        DynamicDataSource.changeNewPhpDynamicDataSource();
        for (TblSpecial tblSpecial : tblSpecials) {
            LambdaQueryWrapper<HmsCategory> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(HmsCategory::getName,tblSpecial.getTitle())
                    .select(HmsCategory::getId);
            List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper1);
            if (hmsCategories.size()!=1){
                failure.add(tblSpecial.getId());
                continue;
            }
            map.put(tblSpecial.getId(), Math.toIntExact(hmsCategories.get(0).getId()));
        }
        System.out.println("===============================================");
        System.out.println("未找到的栏目ID：");
        System.out.println(failure);
    }

    //得到typeID - targetId的对应关系
    public HashMap<Integer,Integer> getTypeID(Integer originId,Integer targetId){
        DynamicDataSource.changeHandleDynamicDataSource();
        LambdaQueryWrapper<TblSpecialContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TblSpecialContent::getSpecialid,originId)
                .select(TblSpecialContent::getTypeid);
        List<Integer> typeIds = tblSpecialContentMapper.selectObjs(wrapper);
        List<Integer> collect = typeIds.stream().distinct().collect(Collectors.toList());
        //Find typeName
        LambdaQueryWrapper<TblType> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.select(TblType::getName,TblType::getTypeid)
                .in(TblType::getTypeid,collect);
        List<TblType> tblTypes = tblTypeMapper.selectList(wrapper1);

        //save the undefined(need to make it by hand)
        List<String> failures = new ArrayList<>();


        // According to typeName to find real targetId
        DynamicDataSource.changeNewPhpDynamicDataSource();
        HashMap<Integer,Integer> map = new HashMap<>();
        for (TblType tblType : tblTypes) {
            LambdaQueryWrapper<HmsCategory> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(HmsCategory::getPid,targetId)
                    .eq(HmsCategory::getName,tblType.getName())
                    .select(HmsCategory::getId);
            List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(wrapper2);
            if (hmsCategories.size()!=1){
                String failure = "origin id: "+originId+"  typeId: "+tblType.getName()+"  pic: "+targetId;
                failures.add(failure);
                continue;
            }
            map.put(tblType.getTypeid(), Math.toIntExact(hmsCategories.get(0).getId()));
        }
//        System.out.println(failures);
        return map;
    }

    @Test
    public void TestGetNewsId(){
        Map<Integer,Integer> map = new HashMap<>();
        getNewsID(map);
    }

    @Test
    public void TestGetIds(){
        Map<Integer,Integer> map = new HashMap<>();
        getNewsID(map);
        List<HashMap<Integer, Integer>> res = new ArrayList<>();
        for (Integer originCatId : map.keySet()) {
            HashMap<Integer, Integer> typeID = getTypeID(originCatId, map.get(originCatId));
            res.add(typeID);
        }
        System.out.println(res);
    }


    public void moveNewsMethod(Integer type,Integer originCatId,Integer targetCatId) {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeNewPhpDynamicDataSource();
            // 按栏目ID查询类别

            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(targetCatId);
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入栏目ID:{},当前导入栏目名称:{}",hmsCategory.getId(), hmsCategory.getName());
            }

            //TODO 根据具体情况修改
            String hmsSite = "http://ychcqmu.1024199.foxtest.net/";
            String cloudPathPrefix = hmsSite + "oss/";

            List<Map<String,Object>> maps = new ArrayList<>();

            // TODO 切回为本地数据源
            DynamicDataSource.changeHandleDynamicDataSource();
            //找到CNews
            LambdaQueryWrapper<TblSpecialContent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TblSpecialContent::getSpecialid,originCatId)
                    .eq(TblSpecialContent::getTypeid,type)
                    .eq(TblSpecialContent::getState,0);

            List<TblSpecialContent> tblSpecialContents = tblSpecialContentMapper.selectList(wrapper);
            //找到CNewsData
            for (TblSpecialContent tblSpecialContent : tblSpecialContents) {
                LambdaQueryWrapper<TblSpecialCData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblSpecialCData::getId,tblSpecialContent.getId());
                TblSpecialCData tblSpecialCData = tblSpecialCDataMapper.selectOne(wrapper1);

                if (tblSpecialCData==null)
                    continue;
                HashMap<String,Object> map = new HashMap<>();
                //处理数据
                // TODO 根据采集对象处理数据至对应类型 处理至hms_c_news
                HmsCNews hmsCNews = new HmsCNews();
                hmsCNews.setDataid(-1L);
                hmsCNews.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCNews.setUsername(1);
                hmsCNews.setStatus((byte) 99);
                hmsCNews.setState(0);
                String publish_time = tblSpecialContent.getInputtime().toString();
                if (StringUtils.isBlank(publish_time)) {
                    publish_time = "0";
                }
                hmsCNews.setPublishTime(Integer.parseInt(publish_time));
                hmsCNews.setExpireTime(0);
                hmsCNews.setInputTime(Integer.parseInt(publish_time));
                hmsCNews.setUpdateTime(Integer.parseInt(publish_time));
                hmsCNews.setIslink(tblSpecialContent.getIslink() == 0 ? null :tblSpecialContent.getUrl());

                // 获取正文
                String content = tblSpecialCData.getContent()== null ? "" : tblSpecialCData.getContent();
                Document document;
                try {
                    content = changeSpecialChat(content);
                    document = new Html(content).getDocument();

                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                }


                Elements imgList = document.select("img");
                Elements linkList = document.select("a");

                try {
                    hmsCNews.setContentNum(document.text().length());
                } catch (Exception e) {
                    hmsCNews.setContentNum(0);
                }
                hmsCNews.setContentImageNum(imgList.size());
                hmsCNews.setContentLinkNum(linkList.size());
                hmsCNews.setTop(0L);
                hmsCNews.setListorder(-99L);
                hmsCNews.setSort(1);
                hmsCNews.setOldCatid(null);
                hmsCNews.setEndOperator(1);
                hmsCNews.setIsLocked(false);

                // TODO 处理至hms_c_news_data
                HmsCNewsData hmsCNewsData = new HmsCNewsData();
                hmsCNewsData.setDid(hmsCNews.getId());
                hmsCNewsData.setTitle(StringUtils.trim(changeSpecialChat(tblSpecialContent.getTitle())));
                hmsCNewsData.setTopTitle(null);
                hmsCNewsData.setSubTitle(null);
                hmsCNewsData.setOriginalTitle(null);

                // 缩略图处理 （只有一张图片）
                String filePath = "C:\\Collect_Data\\cqmu";
                //TODO 根据实际调整
                String localPathPrefix = "C:/Collect_Data/cqmu/";

                String thumb = tblSpecialContent.getThumb();
                if (!StringUtils.isBlank(thumb)) {
                    thumb = CrawlerManager.DownLoad(thumb, filePath, "");
                    thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                }else {
                    thumb="";
                }
                hmsCNewsData.setThumb(thumb);

                hmsCNewsData.setComefrom(StringUtils.trim(tblSpecialContent.getComefrom()));

                // 摘要处理
                String description = tblSpecialContent.getDescription();
                description = changeSpecialChat(description);
                if (StringUtils.isBlank(description)) {
                    String text = document.text();
                    text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                hmsCNewsData.setDescription(description);

                // 作者处理
                String author = tblSpecialCData.getAuthor();
                List<String> authors = new ArrayList<>();
                if (!StringUtils.isBlank(author)) {
                    author = author.trim();
                    authors = Arrays.stream(author.split("\\s+")).collect(Collectors.toList());
                }
                hmsCNewsData.setAuthor(JsonUtil.obj2String(authors));


                // 图片路径集合处理
                try {
                    List<String> imgUrls = new ArrayList<>();
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            imgUrl = CrawlerManager.DownLoad(imgUrl, localPathPrefix, "");
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                imgUrl = imgUrl.replace(localPathPrefix, cloudPathPrefix);
                                imgUrls.add(imgUrl);
                                if (hmsCNewsData.getThumb().isEmpty()){
                                    hmsCNewsData.setThumb(imgUrl);
                                }
                                element.attr("src", imgUrl);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                    hmsCNewsData.setContentImage(JsonUtil.obj2String(imgUrls));
                } catch (Exception e) {
                    hmsCNewsData.setContentImage("[]");
                }

                // 链接路径集合处理
                try {
                    List<String> attachUrls = new ArrayList<>();
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNoneBlank(attachUrl)) {
                            if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                continue;
                            }
                            attachUrl=CrawlerManager.DownLoad(attachUrl,localPathPrefix,"");
                            boolean check = FileUtil.localUrlChick(attachUrl);
                            if (check) {
                                attachUrl = attachUrl.replace(localPathPrefix, cloudPathPrefix);
                                attachUrls.add(attachUrl);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                            attachUrl = attachUrl.replace(localPathPrefix, cloudPathPrefix);
                            attachUrls.add(attachUrl);
                            element.attr("href", attachUrl);
                        }
                    }
                    hmsCNewsData.setContentLink(JsonUtil.obj2String(attachUrls));
                } catch (Exception e) {
                    hmsCNewsData.setContentLink("[]");
                }

                content = ElementUtil.getContentRemoveHtml(document);
                // TODO 这里图片处理完了，可以替换掉正文中的文件路径了
                if (StringUtils.isNotBlank(content)) {
                    content = content.replace(localPathPrefix, cloudPathPrefix);
                }


                hmsCNewsData.setContent(content);

                hmsCNewsData.setTopTitle(null);
                hmsCNewsData.setSubTitle(null);
                hmsCNewsData.setOriginalTitle(null);
                hmsCNewsData.setRelatedExpert(null);
                hmsCNewsData.setRelatedDepart(null);
                hmsCNewsData.setPhotographer("[]");
                hmsCNewsData.setRelevantFiles("[]");
                hmsCNewsData.setEditors(null);
                hmsCNewsData.setExecutiveEditor(null);
                hmsCNewsData.setExamineArticle(null);

                map.put("hmsCNews",hmsCNews);
                map.put("hmsCNewsData",hmsCNewsData);
                maps.add(map);
            }
            // TODO 切换为动态数据源
            DynamicDataSource.changeNewPhpDynamicDataSource();
            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (Map<String, Object> finalMap : maps) {
                    HmsCNews hmsCNews = (HmsCNews) finalMap.get("hmsCNews");
                    HmsCNewsData hmsCNewsData = (HmsCNewsData) finalMap.get("hmsCNewsData");
                    // 落库 -> hms_c_news
                    ApplicationContextProvider.getBeanByType(HmsCNewsMapper.class).insert(hmsCNews);
                    // 栏目缩写+类型id(文章、医师等)+栏目id+导入时数据的id
                    hmsCNews.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCNews.getId()) + ".html");
                    hmsCNews.setListorder(hmsCNews.getId());
                    hmsCNews.setDataid(hmsCNews.getId());
                    // 更新 -> hms_c_news
                    ApplicationContextProvider.getBeanByType(HmsCNewsMapper.class).updateById(hmsCNews);
                    hmsCNewsData.setDid(hmsCNews.getId());
                    // 落库 -> hms_c_news_data
                    ApplicationContextProvider.getBeanByType(HmsCNewsDataMapper.class).insert(hmsCNewsData);
                }
                return null;
            });
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
    }













/**
 * @description:多线程迁移数据
 * @author: RXH
 * @date: 2025/7/30 9:47
 * @param: [type, originCatId, targetCatId]
 * @return: void
 **/

    public void moveNewsMethod2(Integer type,Integer originCatId,Integer targetCatId) {
        try {
            // TODO 切换远程数据源
            DynamicDataSource.changeNewPhpDynamicDataSource();
            // 按栏目ID查询类别
            HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(targetCatId);
            if (hmsCategory == null) {
                log.info("请检查当前导入的栏目ID!");
                DynamicDataSource.changeDefaultDataSource();
                return;
            } else {
                log.info("当前导入栏目ID:{},当前导入栏目名称:{}",hmsCategory.getId(), hmsCategory.getName());
            }

            // TODO 切回为本地数据源
            DynamicDataSource.changeHandleDynamicDataSource();
            //找到CNews
            LambdaQueryWrapper<TblSpecialContent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TblSpecialContent::getSpecialid,originCatId)
                    .eq(TblSpecialContent::getTypeid,type)
                    .eq(TblSpecialContent::getState,0);

            List<TblSpecialContent> tblSpecialContents = tblSpecialContentMapper.selectList(wrapper);
            if (tblSpecialContents==null){
                return;
            }
            //定义线程数量
            int threadCount = 12;
            //创建CountDownLatch-》线程同步
            CountDownLatch latch = new CountDownLatch(threadCount);
            //create List with thread safety
            List<Map<HmsCNews, HmsCNewsData>> resultMaps =
                    Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));
            // 分割任务
            List<List<TblSpecialContent>> taskChunks = divideTasks(tblSpecialContents, threadCount);

            // 提交任务到线程池
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            for (int i = 0; i < threadCount; i++) {
                final int chunkIndex = i;
                executor.submit(() -> {
                    try {
                        // 处理分配的任务块
                        Map<HmsCNews, HmsCNewsData> chunkResult =
                                processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                        // 将结果按任务块索引放入对应位置
                        synchronized (resultMaps) {
                            resultMaps.set(chunkIndex, chunkResult);
                        }
                    } finally {
                        // 任务完成，计数减一
                        latch.countDown();
                    }
                });
            }
            //等待所有线程结束
            try {
                latch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Thread interrupted while waiting for tasks", e);
            } finally {
                // 关闭线程池
                executor.shutdown();
            }
            // 合并所有线程的结果
            Map<HmsCNews, HmsCNewsData> mergedMap = new LinkedHashMap<>();
            for (Map<HmsCNews, HmsCNewsData> map : resultMaps) {
                mergedMap.putAll(map);
            }

            // TODO 切换为动态数据源
            DynamicDataSource.changeNewPhpDynamicDataSource();
            // TODO 添加事务的处理
            TransactionUtil.executeInTransaction(() -> {
                for (HmsCNews hmsCNews : mergedMap.keySet()) {
                    HmsCNewsData hmsCNewsData =  mergedMap.get(hmsCNews);
                    // 落库 -> hms_c_news
                    ApplicationContextProvider.getBeanByType(HmsCNewsMapper.class).insert(hmsCNews);
                    // 栏目缩写+类型id(文章、医师等)+栏目id+导入时数据的id
                    hmsCNews.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", hmsCNews.getId()) + ".html");
                    hmsCNews.setListorder(hmsCNews.getId());
                    hmsCNews.setDataid(hmsCNews.getId());
                    // 更新 -> hms_c_news
                    ApplicationContextProvider.getBeanByType(HmsCNewsMapper.class).updateById(hmsCNews);
                    hmsCNewsData.setDid(hmsCNews.getId());
                    // 落库 -> hms_c_news_data
                    ApplicationContextProvider.getBeanByType(HmsCNewsDataMapper.class).insert(hmsCNewsData);
                }
                return null;
            });
        }finally {
            DynamicDataSource.changeDefaultDataSource();
        }
    }







    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start = end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }





    private Map<HmsCNews, HmsCNewsData> processTaskChunk(
            List<TblSpecialContent> chunk, Integer targetCatId) {
        Map<HmsCNews, HmsCNewsData> chunkResult = new LinkedHashMap<>();
        try {
            for (TblSpecialContent tblSpecialContent : chunk) {
                DynamicDataSource.changeNewPhpDynamicDataSource();
                // 按栏目ID查询类别
                HmsCategory hmsCategory = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class).selectById(targetCatId);
                if (hmsCategory == null) {
                    log.info("请检查当前导入的栏目ID!");
                    DynamicDataSource.changeDefaultDataSource();
                    return null;
                } else {
                    log.info("当前导入栏目ID:{},当前导入栏目名称:{}",hmsCategory.getId(), hmsCategory.getName());
                }

                // TODO 切回为本地数据源
                DynamicDataSource.changeHandleDynamicDataSource();
                //TODO 根据具体情况修改
                String hmsSite = "http://ychcqmu.1024199.foxtest.net/";
                String cloudPathPrefix = hmsSite + "oss/";
                //找到CNewsData
                LambdaQueryWrapper<TblSpecialCData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblSpecialCData::getId, tblSpecialContent.getId());
                TblSpecialCData tblSpecialCData = tblSpecialCDataMapper.selectOne(wrapper1);

                //处理数据
                // TODO 根据采集对象处理数据至对应类型 处理至hms_c_news
                HmsCNews hmsCNews = new HmsCNews();
                hmsCNews.setDataid(-1L);
                hmsCNews.setCatid(Integer.parseInt(hmsCategory.getId().toString()));
                hmsCNews.setUsername(1);
                hmsCNews.setStatus((byte) 99);
                hmsCNews.setState(0);
                String publish_time = tblSpecialContent.getInputtime().toString();
                if (StringUtils.isBlank(publish_time)) {
                    publish_time = "0";
                }
                hmsCNews.setPublishTime(Integer.parseInt(publish_time));
                hmsCNews.setExpireTime(0);
                hmsCNews.setInputTime(Integer.parseInt(publish_time));
                hmsCNews.setUpdateTime(Integer.parseInt(publish_time));
                hmsCNews.setIslink(tblSpecialContent.getIslink() == 0 ? null : tblSpecialContent.getUrl());

                // 获取正文
                String content = tblSpecialCData.getContent() == null ? "" : tblSpecialCData.getContent();
                Document document;
                try {
                    content = changeSpecialChat(content);
                    document = new Html(content).getDocument();

                } catch (Exception e) {
                    log.error("解析正文失败");
                    document = new Html("").getDocument();
                }


                Elements imgList = document.select("img");
                Elements linkList = document.select("a");

                try {
                    hmsCNews.setContentNum(document.text().length());
                } catch (Exception e) {
                    hmsCNews.setContentNum(0);
                }
                hmsCNews.setContentImageNum(imgList.size());
                hmsCNews.setContentLinkNum(linkList.size());
                hmsCNews.setTop(0L);
                hmsCNews.setListorder(-99L);
                hmsCNews.setSort(1);
                hmsCNews.setOldCatid(null);
                hmsCNews.setEndOperator(1);
                hmsCNews.setIsLocked(false);

                // TODO 处理至hms_c_news_data
                HmsCNewsData hmsCNewsData = new HmsCNewsData();
                hmsCNewsData.setDid(hmsCNews.getId());
                hmsCNewsData.setTitle(StringUtils.trim(changeSpecialChat(tblSpecialContent.getTitle())));
                hmsCNewsData.setTopTitle(null);
                hmsCNewsData.setSubTitle(null);
                hmsCNewsData.setOriginalTitle(null);

                // 缩略图处理 （只有一张图片）
                String filePath = "C:\\Collect_Data\\cqmu";
                //TODO 根据实际调整
                String localPathPrefix = "C:/Collect_Data/cqmu/";

                String thumb = tblSpecialContent.getThumb();
                if (!StringUtils.isBlank(thumb)) {
                    thumb = CrawlerManager.DownLoad(thumb, filePath, "");
                    thumb = thumb.replace(localPathPrefix, cloudPathPrefix);
                } else {
                    thumb = "";
                }
                hmsCNewsData.setThumb(thumb);

                hmsCNewsData.setComefrom(StringUtils.trim(tblSpecialContent.getComefrom()));

                // 摘要处理
                String description = tblSpecialContent.getDescription();
                description = changeSpecialChat(description);
                if (StringUtils.isBlank(description)) {
                    String text = document.text();
                    text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;
                } else {
                    description = StringUtils.trim(description);
                }
                hmsCNewsData.setDescription(description);

                // 作者处理
                String author = tblSpecialCData.getAuthor();
                List<String> authors = new ArrayList<>();
                if (!StringUtils.isBlank(author)) {
                    author = author.trim();
                    authors = Arrays.stream(author.split("\\s+")).collect(Collectors.toList());
                }
                hmsCNewsData.setAuthor(JsonUtil.obj2String(authors));
                // 图片路径集合处理
                try {
                    List<String> imgUrls = new ArrayList<>();
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            imgUrl = CrawlerManager.DownLoad(imgUrl, localPathPrefix, "");
                            boolean check = FileUtil.localUrlChick(imgUrl);
                            if (check) {
                                imgUrl = imgUrl.replace(localPathPrefix, cloudPathPrefix);
                                imgUrls.add(imgUrl);
                                if (hmsCNewsData.getThumb().isEmpty()) {
                                    hmsCNewsData.setThumb(imgUrl);
                                }
                                element.attr("src", imgUrl);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                        }
                    }
                    hmsCNewsData.setContentImage(JsonUtil.obj2String(imgUrls));
                } catch (Exception e) {
                    hmsCNewsData.setContentImage("[]");
                }

                // 链接路径集合处理
                try {
                    List<String> attachUrls = new ArrayList<>();
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNoneBlank(attachUrl)) {
                            if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                continue;
                            }
                            attachUrl = CrawlerManager.DownLoad(attachUrl, localPathPrefix, "");
                            boolean check = FileUtil.localUrlChick(attachUrl);
                            if (check) {
                                attachUrl = attachUrl.replace(localPathPrefix, cloudPathPrefix);
                                attachUrls.add(attachUrl);
                            } else {
                                // 移除失效图片
                                element.remove();
                            }
                            attachUrl = attachUrl.replace(localPathPrefix, cloudPathPrefix);
                            attachUrls.add(attachUrl);
                            element.attr("href", attachUrl);
                        }
                    }
                    hmsCNewsData.setContentLink(JsonUtil.obj2String(attachUrls));
                } catch (Exception e) {
                    hmsCNewsData.setContentLink("[]");
                }

                content = ElementUtil.getContentRemoveHtml(document);
                // TODO 这里图片处理完了，可以替换掉正文中的文件路径了
                if (StringUtils.isNotBlank(content)) {
                    content = content.replace(localPathPrefix, cloudPathPrefix);
                }


                hmsCNewsData.setContent(content);

                hmsCNewsData.setTopTitle(null);
                hmsCNewsData.setSubTitle(null);
                hmsCNewsData.setOriginalTitle(null);
                hmsCNewsData.setRelatedExpert(null);
                hmsCNewsData.setRelatedDepart(null);
                hmsCNewsData.setPhotographer("[]");
                hmsCNewsData.setRelevantFiles("[]");
                hmsCNewsData.setEditors(null);
                hmsCNewsData.setExecutiveEditor(null);
                hmsCNewsData.setExamineArticle(null);

                chunkResult.put(hmsCNews,hmsCNewsData);
            }
        }catch (Exception e){
            System.err.println("处理失败"+e.getMessage());
        }
        return chunkResult;
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }


}
