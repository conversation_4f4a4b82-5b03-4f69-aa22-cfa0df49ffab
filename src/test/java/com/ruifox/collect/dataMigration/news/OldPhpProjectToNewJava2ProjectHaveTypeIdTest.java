package com.ruifox.collect.dataMigration.news;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.aop.annotation.TestBefore;
import com.ruifox.collect.aop.impl.Notice;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station.StationMArticleMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorMapper;
import com.ruifox.collect.dao.mapper.station.StationMImageMapper;
import com.ruifox.collect.dao.mapper.station.StationMTeacherMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.dao.mapper.tbl.TblCManDataMxfyMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureDataMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCategoryMapper;
import com.ruifox.collect.dao.mapper.tbl_oldest.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.*;
import com.ruifox.collect.module.entity.tbl.TblCPicture;
import com.ruifox.collect.module.entity.tbl.TblCPictureData;
import com.ruifox.collect.module.entity.tbl.TblCategory;
import com.ruifox.collect.module.entity.tbl_oldest.*;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
@TestBefore
public class OldPhpProjectToNewJava2ProjectHaveTypeIdTest {
    @Autowired
    private TblCNewsMapperOldest tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapperOldest tblCNewsDataMapper;
    @Autowired
    private TblCManDataMxfyMapper tblCManDataMxfyMapper;
    @Autowired
    private TblCManDataMapperOldest tblCManDataMapper;
    @Autowired
    private TblCManMapperOldest tblCManMapper;
    @Autowired
    private StationMArticleMapper stationMArticleMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    private StationMDoctorDataMapper2 stationMDoctorDataMapper;
    @Autowired
    private StationMTeacherMapper stationMTeacherMapper;
    @Autowired
    private StationMTeacherDataMapper2 stationMTeacherDataMapper;
    @Autowired
    private StationCategoryMapper2 stationCategoryMapper;
    @Autowired
    private TblCategoryMapper tblCategoryMapper;
    @Autowired
    private TblCPictureMapper tblCPictureMapper;
    @Autowired
    private TblCPictureDataMapper tblCPictureDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Autowired
    private StationMImageDataMapper2 stationMImageDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private TblCVideoMapperOldest tblCVideoMapper;
    @Autowired
    private TblCVideoDataMapperOldest tblCVideoDataMapper;
    @Autowired
    private StationMVideoDataMapper2 stationMVideoDataMapper;



    @Value("${testDownload.config.refer}")
    private String refer;

    @Value("${testDownload.config.fileName}")
    private String downloadPath;

    @Autowired
    private Notice notice;
    @Value("${token.describe}")
    private String describe;

    /**
     * @description:提醒我账号选择是否正确
     * @author: RXH
     * @date: 2025/8/8 17:32
     * @param: []
     * @return: void
     **/
    @PostConstruct
    public void giveNotice(){
        notice.say();
    }


    public void motherChildrenIds(Map<Integer, Integer> map ){
        //        栏目1   新闻中心

//        map.put(13,23);   // 综合新闻 *
//        map.put(1991,27);   // 媒体聚焦 * 0
//        map.put(10,22);   // 焦点新闻  * 0
//        map.put(2341,24);   // 临床新闻
//        map.put(2342,25);   // 教学新闻
//        map.put(1846,26);   // 学术新闻
//
//
//
//
//        //栏目2    医院信息-招标前技术调研
//        map.put(2150,30);   // 专业设备物资及服务采购前市场调研 * 0
//        map.put(2151,31);   // 通用设备物资及服务采购前市场调研 * 0
//        map.put(2544,32);   // 信息类物资及服务采购市场调研
//        map.put(2152,33);   // 招标信息  * 0
//        map.put(1129,35);   // 招生信息
//        map.put(1130,36);   // 通知信息
//        map.put(12,37);   // 学术信息
//
//
//        //栏目3    视频中心
//        map.put(2091,44);   // 先进典型
//
//
//        //栏目4    就医指南
//        map.put(2308,11);   // 就诊重要通知
//        map.put(1829,14);   // 便民门诊
//
//
//        //栏目6    医联体
//        map.put(1488,92);   //最新动态
//
//
//        //栏目5    科室导航-院本部-党群部门-党委办公室
//        map.put(313,58);   // 部门动态
//        map.put(314,59);   // 规章制度
//        map.put(316,60);   // 通知通告
//        map.put(318,62);   // 重要文件
//        map.put(1965,63);   // 学习安排
//
//
//                map.put(153,114);   // 星耀二院
//        map.put(154,115);   // 人文二院
//        map.put(156,116);   // 医暖二院
//
//        //        栏目7 特色医疗
//        map.put(1484,118);   // 特色医疗-儿科
//        map.put(1485,119);   // 特色医疗-妇产科
//      //栏目8 信息公开
//        map.put(36,123);   // 信息公开目录
//        map.put(37,124);   // 信息公开实施办法
//        map.put(40,126);   // 信息公开制度文件
//
//        map.put(2746,67);   // 根栏目-健康科普
//        map.put(2729,191);   // 主动公开-妇幼健康产业
//        map.put(2582,235);   // 规范化培训-政策法规
//        map.put(2371,301);   // 科学研究-通知通告
//
//        map.put(2307,179);   // 财务资产-部门预算
//        map.put(2041,112);   // 党建工作-青春风采
//        map.put(2038,109);   // 党的建设-学习通知
//
//        map.put(1109,276);   // 项目申请-国家自然科学基金
//        map.put(1110,277);   // 项目申请-国家科技部
//        map.put(1111,278);   // 项目申请-国家教育部
//        map.put(1112,279);   // 项目申请-国家其他部委
//        map.put(1114,281);   // 项目申请-省科技厅
//        map.put(1116,282);   // 项目申请-其他项目
//
//
//        map.put(46,137);   // 发展规划-医联体
//        map.put(47,190);   // 主动公开-年度工作
//        map.put(49,139);   // 主动公开-公益捐赠
//        map.put(73,211);   // 护理天地-护理动态
//        map.put(74,212);   // 护理天地-护理教学
//        map.put(75,213);   // 护理天地-护理科研
//        map.put(76,214);   // 护理天地-专科护理
//        map.put(79,216);   // 护理天地-人物专访
//        map.put(81,218);   // 护理天地-继续教育
//
//        map.put(90,236);   // 规范化培训-招生简章
//        map.put(91,237);   // 规范化培训-面试通知
//        map.put(92,238);   // 规范化培训-录取名单
//        map.put(93,239);   // 规范化培训-报到通知
//        map.put(94,232);   // 医学教育-部门动态
//        map.put(95,241);   // 医学教育-本科教学
//        map.put(96,242);   // 医学教育-研究生培养
//
//
//        map.put(98,244);   // 继续医学教育-继教项目与培训
//        map.put(99,245);   // 继续医学教育-远程医学教育
//        map.put(100,246);   // 继续医学教育-护理继续教育
//        map.put(101,247);   // 继续医学教育-各类短期培训
//        map.put(102,229);   // 医学教育概况-基地项目
//        map.put(119,266);   // 科学研究-政策法规
//        map.put(123,284);   // 成果专利-成果
//        map.put(124,285);   // 成果专利-专利
//        map.put(129,288);   // 期刊及学科代码-期刊信息
//        map.put(130,289);   // 期刊及学科代码-学科代码
//        map.put(131,290);   // 科学研究-办事指南
//        map.put(143,106);   // 党建工作-新闻动态
//        map.put(150,108);   // 党的建设-支部风采
//
//
//        map.put(331,324);   // 党委宣传统战部（精神文明办公室）-部门动态
//        map.put(335,327);   // 党委宣传统战部（精神文明办公室）-办事流程
//        map.put(1205,326);   // 党委宣传统战部（精神文明办公室）-通知通告
//        map.put(1960,328);   // 党委宣传统战部（精神文明办公室）-统战工作
//        map.put(1206,339);   // 纪委办公室-通知通告
//        map.put(340,338);   // 纪委办公室-部门动态
//        map.put(969,340);   // 纪委办公室-办事流程
//        map.put(1179,177);   // 财务资产-财务政策
//        map.put(2280,330);   // 专题学习-学习内容
//
//
//        //        院长办公室
//        map.put(248,343);   // 院长办公室（应急办公室）-办事流程
//        map.put(250,344);   // 院长办公室（应急办公室）-部门动态
//        map.put(1192,345);   // 院长办公室（应急办公室）-通知通告
//        map.put(39,193);   // 信息公开-资料下载
//===============================================================================================================
//===============================================================================================================
        //        党委组织部
        map.put(322,450);   // 党委组织部-部门动态
        map.put(323,454);   // 党委组织部-支部风采
        map.put(326,452);   // 党委组织部-办事流程
        map.put(327,457);   // 党委组织部-基层党建
        map.put(328,451);   // 党委组织部-通知通告
        map.put(329,461);   // 党委组织部-学习园地

        //      行风建设办公室
        map.put(2360,471);   // 行风建设办公室-部门动态
        map.put(2363,473);   // 行风建设办公室-办事流程
        map.put(2364,478);   // 行风建设办公室-法规制度

        //      审计办公室
        map.put(263,485);   // 审计办公室-办事流程
        map.put(265,483);   // 审计办公室-部门动态
        map.put(699,488);   // 审计办公室-法规制度

        //      工会
        map.put(349,491);   // 工会-部门动态
        map.put(350,497);   // 工会-工作职责
        map.put(352,498);   // 工会-法律法规
        map.put(353,493);   // 工会-办事流程
        map.put(354,499);   // 工会-院务公开
        map.put(355,500);   // 工会-工会之家
        map.put(1207,492);   // 工会-通知通告

        //      团委（社工办）
        map.put(359,511);   // 团委（社工办）-志愿者
        map.put(361,512);   // 团委（社工办）-慈善捐赠
        map.put(362,513);   // 团委（社工办）-团务知识
        map.put(363,514);   // 团委（社工办）-文件制度
        map.put(364,515);   // 团委（社工办）-青年文明号
        map.put(365,516);   // 团委（社工办）-荣誉奖项
        map.put(1014,505);   // 团委（社工办）-部门动态
        map.put(1015,508);   // 团委（社工办）-组织架构
        map.put(1025,506);   // 团委（社工办）-通知通告
        map.put(1031,509);   // 团委（社工办）-工作职责


        //        人力资源部（党委教师工作办公室/人才工作办公室）
        map.put(253,526);   // 人力资源部（党委教师工作办公室/人才工作办公室）-办事流程
        map.put(255,527);   // 人力资源部（党委教师工作办公室/人才工作办公室）-部门动态
        map.put(697,532);   // 人力资源部（党委教师工作办公室/人才工作办公室）-相关政策
        map.put(1193,528);   // 人力资源部（党委教师工作办公室/人才工作办公室）-通知通告

        //      运营管理与评价部
        map.put(275,535);   // 运营管理与评价部-部门动态

        //      财务部
        map.put(258,548);   // 财务部-办事流程
        map.put(260,549);   // 财务部-部门动态
        map.put(698,555);   // 财务部-相关政策
        map.put(1194,550);   // 财务部-通知通告

        //      信息管理部
        map.put(288,566);   // 信息管理部-办事流程
        map.put(290,567);   // 信息管理部-部门动态

        //      采购部
        map.put(1347,576);   // 采购部-部门动态
        map.put(1348,579);   // 采购部-招标信息
        map.put(1349,577);   // 采购部-通知通告

        //      档案室
        map.put(1864,583);   // 档案室-部门动态
        map.put(1881,582);   // 档案室-办事流程

        //      法务部
        map.put(1323,590);   // 法务部-部门动态
        map.put(1324,589);   // 法务部-办事流程
        map.put(1327,591);   // 法务部-通知通告

        //      公共事业发展部
        map.put(269,598);   // 公共事业发展部-医联体
        map.put(270,595);   // 公共事业发展部-部门动态
        map.put(700,599);   // 公共事业发展部-远程医学

        //      医务概况
        map.put(1642,607);   // 医务概况-部门职能

        //      医务动态
        map.put(371,610);   // 医务动态-工作动态

        //      医务公开
        map.put(369,615);   // 医务公开-办事流程
        map.put(370,613);   // 医务公开-政策法规
        map.put(1644,614);   // 医务公开-规章制度

        //      专题专栏
        map.put(2229,633);   // 专题专栏-进修生管理

        //      护理部
        map.put(383,646);   // 护理部-办事流程
        map.put(384,651);   // 护理部-相关政策
        map.put(385,647);   // 护理部-部门动态

        //      医院感染管理部
        map.put(880,658);   // 医院感染管理部-办事流程
        map.put(881,662);   // 医院感染管理部-相关政策
        map.put(882,659);   // 医院感染管理部-部门动态
        map.put(1204,660);   // 医院感染管理部-通知通告

        //      门诊部（互联网医院办公室）
        map.put(392,668);   // 门诊部（互联网医院办公室）-部门动态


        //      医疗保健部
        map.put(2275,676);   // 医疗保健部-部门动态


        //      随访中心
        map.put(1334,680);   // 随访中心-部门动态
        map.put(2428,685);   // 随访中心-全病程管理


        //     病案管理部
        map.put(399,689);   // 病案管理部-部门动态
        map.put(1346,693);   // 病案管理部-打印须知

        //      妇儿保健部
        map.put(2165,697);   // 妇儿保健部-部门动态

        //        医保部
        map.put(1877,706);   // 医保部-部门动态
        map.put(1878,707);   // 医保部-通知通告


        //      医学装备保障部
        map.put(300,711);   // 医学装备保障部-部门动态
        map.put(1201,712);   // 医学装备保障部-通知通告
        map.put(1930,715);   // 医学装备保障部-招投标信息
        map.put(2048,716);   // 医学装备保障部-调研信息


        //      后勤管理部
        map.put(303,726);   // 后勤管理部-规章制度
        map.put(305,721);   // 后勤管理部-部门动态
        map.put(852,727);   // 后勤管理部-办事流程
        map.put(853,728);   // 后勤管理部-应急预案
        map.put(1202,722);   // 后勤管理部-通知通告
        map.put(1931,724);   // 后勤管理部-招投标信息
        map.put(2045,725);   // 后勤管理部-调研信息


        //      基建办公室
        map.put(1356,741);   // 基建办公室-部门动态
        map.put(1357,742);   // 基建办公室-通知通告
        map.put(2226,743);   // 基建办公室-政策法规


        //      安全保卫部
        map.put(293,753);   // 安全保卫部-安全知识
        map.put(295,746);   // 安全保卫部-部门动态
        map.put(705,752);   // 安全保卫部-相关政策
        map.put(1200,747);   // 安全保卫部-通知通告
        map.put(2761,749);   // 安全保卫部-调研信息

        //      妇产科教研室/妇产科
        map.put(405,355);   // 妇产科教研室/妇产科-医学教育
        map.put(406,357);   // 妇产科教研室/妇产科-科学研究

        //        妇科肿瘤科
        map.put(589,772);   // 妇科肿瘤科-科室动态
        map.put(1233,776);   // 妇科肿瘤科-温馨告示

        //        产科
        map.put(420,758);   // 产科-科室动态
        map.put(423,764);   // 产科-特色医疗

        //      生殖内分泌科
        map.put(1216,794);   // 生殖内分泌科-通知通告

        //      生殖男科/四川省人类精子库
        map.put(573,802);   // 生殖男科/四川省人类精子库-科室动态
        map.put(1860,807);   // 生殖男科/四川省人类精子库-男性生殖临床
        map.put(1861,806);   // 生殖男科/四川省人类精子库-特色医疗

        //      儿科/儿科教研室
        map.put(474,827);   // 儿科/儿科教研室-科室动态
        map.put(477,834);   // 儿科/儿科教研室-医学教育
        map.put(478,835);   // 儿科/儿科教研室-科学研究
        map.put(479,833);   // 儿科/儿科教研室-医疗管理

        //      新生儿科
        map.put(501,839);   // 新生儿科-科室动态
        map.put(1065,843);   // 新生儿科-特色医疗

        //      儿童心血管科
        map.put(492,847);   // 儿童心血管科-科室动态


        //      儿童呼吸科
        map.put(4103,858);   // 儿童呼吸科-科室动态
        map.put(4112,861);   // 儿童呼吸科-通知通告
        map.put(4113,863);   // 儿童呼吸科-特色医疗


        //     儿童免疫科
        map.put(1377,869);   // 儿童免疫科-科室动态
        map.put(1869,872);   // 儿童免疫科-通知通告
        map.put(1994,874);   // 儿童免疫科-特色医疗


        //      儿童血液科
        map.put(483,880);   // 儿童血液科-科室动态
        map.put(487,887);   // 儿童血液科-特色专科


        //      儿童肾脏科
        map.put(519,892);   // 儿童肾脏科-科室动态
        map.put(1067,897);   // 儿童肾脏科-特色医疗
        map.put(1226,895);   // 儿童肾脏科-通知通告
        map.put(2301,898);   // 儿童肾脏科-华西儿肾联盟


        //      儿童神经科
        map.put(510,912);   // 儿童神经科-科室动态
        map.put(1066,917);   // 儿童神经科-特色医疗


        //      儿童消化科
        map.put(654,928);   // 儿童消化科-特色医疗
        map.put(656,923);   // 儿童消化科-科室动态


        //      儿童感染科
        map.put(528,935);   // 儿童感染科-科室动态
        map.put(1068,940);   // 儿童感染科-特色医疗

        //      综合儿科
        map.put(2350,953);   // 综合儿科-科室动态
        map.put(2354,959);   // 综合儿科-特色医疗

        //      儿童保健科
        map.put(537,964);   // 儿童保健科-科室动态
        map.put(1069,969);   // 儿童保健科-特色医疗

        //      儿童重症医学科
        map.put(546,978);   // 儿童重症医学科-科室动态

        //      儿外科
        map.put(2052,990);   // 儿外科-科室动态
        map.put(2058,992);   // 儿外科-通知通告

        //      儿童泌尿外科
        map.put(2512,1004);   // 儿童泌尿外科-科室动态

        //      儿童神经外科
        map.put(2508,1009);   // 儿童神经外科-科室动态

        //      儿童普外科
        map.put(2513,1024);   // 儿童普外科-科室动态

        //      综合外科
        map.put(2505,1028);   // 综合外科-科室动态
        map.put(2750,1034);   // 综合外科-学科发展

        //      综合内科
        map.put(2507,1038);   // 综合内科-科室动态


        //      整形美容修复重建科/创面修复中心
        map.put(2466,1043);   // 整形美容修复重建科/创面修复中心-科室动态

        //      康复医学科
        map.put(564,1058);   // 康复医学科-科室动态
        map.put(1393,1063);   // 康复医学科-特色医疗

        //      中医科
        map.put(2119,1075);   // 中医科-特色医疗
        map.put(2120,1071);   // 中医科-科室动态

        //        筑浪学院
        map.put(2318,402);   // 筑浪学院-学院动态


        //      纪委办公室-廉洁教育
        map.put(1819,415);   // 廉洁教育-要闻关注
        map.put(1820,416);   // 廉洁教育-警示教育
        map.put(1821,417);   // 廉洁教育-廉政文化

        //      生殖男科/四川省人类精子库-学术研究
        map.put(2236,811);   // 学术研究-青年医师学术讲座
        map.put(2237,812);   // 学术研究-科研合作
        map.put(2238,813);   // 学术研究-科研成果
        map.put(2239,814);   // 学术研究-对外学术交流

        //      医疗运行保障办公室
        map.put(1933,732);   // 医疗运行保障办公室-相关职责
        map.put(1937,737);   // 医疗运行保障办公室-培训学习

        //      培训基地
        map.put(2232,817);   // 培训基地-精液分析标准化及质量控制培训基地
        map.put(2587,818);   // 培训基地-男性不育规范化诊疗诊疗培训基地
        map.put(2588,819);   // 培训基地-男科显微手术培训基地

        //      生殖男科联盟
        map.put(2240,820);   // 生殖男科联盟-进修
        map.put(2241,821);   // 生殖男科联盟-室间质评
        map.put(2243,822);   // 生殖男科联盟-走进联盟单位


        //  匹配有误
//        map.put(1359,350);   // 科室动态-科室动态
//        map.put(1360,352);   // 科室动态-学术讲座/业务学习
//        map.put(1375,350);   // 科室动态-科室动态
//        map.put(1376,352);   // 科室动态-学术讲座/业务学习



//   ====================================================================================================
//   ====================================================================================================

        System.out.println(map.size());
        Map<Integer, Integer> collect = map.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (existing, replacement) -> existing
        ));
        System.out.println(collect.size());

        Set<Integer> filterSet = new HashSet<>();
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            Integer value = entry.getValue();
            if (filterSet.contains(value))
                System.out.println("重复的targetId为："+value);
            filterSet.add(value);
        }
        System.out.println(filterSet.size());

    }

    @Test
    public void moveNewsJava()  {
        Integer id = 0;
        Map<Integer, Integer> map = new HashMap<>();

        motherChildrenIds(map);


        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("运行成功的任务ID有：");
        for (Integer i : success) {
            System.out.print(i+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    @Test
    public void testGetNewsId(){
        HashMap<Integer,Integer> map = new HashMap<>();
        getNewsId(map);
//        getDepartNewsID(map);
        System.out.println(map.size());
    }



    //TODO 需要调整，根据所有的父节点来确定
    public void getNewsId(Map<Integer, Integer> map){

        DynamicDataSource.changeOldPhpDynamicDataSource();
        //找到所有新闻数据的catID
        LambdaQueryWrapper<TblCNewsOldest> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.select(TblCNewsOldest::getCatid)
                .eq(TblCNewsOldest::getStatus,99)
                .eq(TblCNewsOldest::getState, 0);
        List<Integer> originList = tblCNewsMapper.selectObjs(wrapper1);
        List<Integer> DistinctList = originList.stream().distinct().collect(Collectors.toList());


        //根据originCatId 找出对应的CategoryName为后续找到targetId做准备
        LambdaQueryWrapper<TblCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TblCategory::getCatid,DistinctList);
        wrapper.select(TblCategory::getCatname,TblCategory::getCatid,TblCategory::getParentid);
        List<TblCategory> hmsCategories = tblCategoryMapper.selectList(wrapper);

        for (TblCategory hmsCategory : hmsCategories) {
            System.out.println(" categoryId: "+hmsCategory.getCatid()+"   categoryName: "+hmsCategory.getCatname()+"   parentId: "+hmsCategory.getParentid());
        }

        List<String> failure = new ArrayList<>();
        List<String> success = new ArrayList<>();
        for (TblCategory hmsCategory : hmsCategories) {
            Integer parentId = 0;
            String parentName = "根栏目";
            //parentId 为0时,targetID的Pid同样为0，不需要找
            if (hmsCategory.getParentid()!=0){
                //找到parentName
                DynamicDataSource.changeOldPhpDynamicDataSource();
                LambdaQueryWrapper<TblCategory> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                categoryLambdaQueryWrapper.eq(TblCategory::getCatid,hmsCategory.getParentid())
                        .select(TblCategory::getCatname);
                TblCategory tblCategory = tblCategoryMapper.selectOne(categoryLambdaQueryWrapper);
                parentName = tblCategory.getCatname();
                //根据parentName找到对应的parentId
                DynamicDataSource.changeBuildDynamicDataSource();
                LambdaQueryWrapper<StationCategory2> category2LambdaQueryWrapper = new LambdaQueryWrapper<>();
                category2LambdaQueryWrapper.eq(StationCategory2::getName,parentName)
                        .select(StationCategory2::getId)
                        .apply("model_id is null");
                List<StationCategory2> stationCategory2s = stationCategoryMapper.selectList(category2LambdaQueryWrapper);
                //检查存在性以及唯一性
                if (stationCategory2s.size()!=1){
                    continue;
                }
                parentId = stationCategory2s.get(0).getId();
            }

            //根据PID和categoryName定位到targetID
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getName, hmsCategory.getCatname())
                    .eq(StationCategory2::getModelId,20)
                    .eq(StationCategory2::getPid,parentId);
            List<StationCategory2> stationCategory2 = stationCategoryMapper.selectList(wrapper2);
            if (stationCategory2.size()!=1){
                String fail ="栏目名称："+hmsCategory.getCatname()+"；originId："+hmsCategory.getCatid();
                failure.add(fail);
                continue;
            }
            map.put(Math.toIntExact(hmsCategory.getCatid()), stationCategory2.get(0).getId());
            String tmp = "map.put("+hmsCategory.getCatid()+","+stationCategory2.get(0).getId()+");   // "+parentName+"-"+hmsCategory.getCatname();
            success.add(tmp);
        }
        System.out.println("======================================");
        System.out.println("======================================");
        System.out.println("===========匹配成功的栏目总合计:"+success.size()+"条：==============");
        for (String s : success) {
            System.out.println(s);
        }

//        System.out.println("======================================");
//        System.out.println("======================================");
//        System.out.println("===========匹配失败的栏目：总合计:"+failure.size()+"条==============");
//        for (String s : failure) {
//            System.out.println(s);
//        }
    }

    public void moveNewsJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCNewsOldest> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCNewsOldest::getCatid, originCatId)
                .eq(TblCNewsOldest::getState, 0)
                .eq(TblCNewsOldest::getStatus, 99)
                .orderByAsc(TblCNewsOldest::getListorder);
        List<TblCNewsOldest> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 60;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCNewsOldest>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMArticleData2> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMArticleData2> mergedMap = new LinkedHashMap<>(20000);
        for (Map<StationMReference, StationMArticleData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMArticleData2 targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetNew);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMArticleDataMapper.insert(targetNewData);
            //落库 -> folder_resource
            new FolderResource();
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetNewData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetNewData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetNew.setDataId(Long.valueOf(folderResource.getId()));
            targetNew.setSortLevel(Math.toIntExact(targetNew.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetNew);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start=end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<StationMReference, StationMArticleData2> processTaskChunk(
            List<TblCNewsOldest> chunk, Integer targetCatId) {
        Map<StationMReference, StationMArticleData2> chunkResult = new LinkedHashMap<>();
        try {
            for (TblCNewsOldest originNew : chunk) {
                // 新的article
                StationMReference stationMArticle = new StationMReference();
                stationMArticle.setCatId(targetCatId);
                stationMArticle.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));
                stationMArticle.setState(99);
                stationMArticle.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMArticle.setViews(originNew.getViews());
                stationMArticle.setIsTop(0);


                String linkUrl = originNew.getUrl();
                int isLink = originNew.getIslink();
                // TODO 根据url判断是否为外链(具体网站具体分析)
//                if (linkUrl.contains("myzyy")) {
//                    isLink = 0;
//                }

                DynamicDataSource.changeOldPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblCNewsDataOldest> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCNewsDataOldest::getId, originNew.getId());
                TblCNewsDataOldest originNewsData = tblCNewsDataMapper.selectOne(wrapper1);
                if (originNewsData==null)
                    continue;

                // 新的articleData
                StationMArticleData2 stationMArticleData = new StationMArticleData2();
                stationMArticleData.setUuid(UUID.randomUUID().toString());
                stationMArticleData.setTitle(originNew.getTitle());
                stationMArticleData.setIgnoreReason(null);
                stationMArticleData.setPhotographer(originNewsData.getPictuer());
                stationMArticleData.setCreateUserId(1);
                stationMArticleData.setUpdateUserId(1);
                stationMArticleData.setCreateTime(Double.valueOf(originNew.getInputtime()+"000"));
                stationMArticleData.setUpdateTime(Double.valueOf(originNew.getUpdatetime()+"000"));
                stationMArticleData.setState(2);
                // 处理作者
                String input = originNewsData.getAuthor();
                // TODO 根据实际处理
                if (input!=null&&!input.isBlank()&&!input.isEmpty()){
                    input = changeSpecialChat(input);
                    String author = leaveChinese(input);
                    System.out.println(author);
                    if (!author.equals("0") && !author.isEmpty())
                        stationMArticleData.setAuthor(author);
                }

                // 缩略图处理 （只有一张图片）
                String thumb = originNew.getThumb();
                // 调用接口
                if (thumb!=null&&!thumb.isBlank()&&!thumb.isEmpty()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    if (!thumb.isBlank()&&!thumb.isEmpty()){
                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    }
                    stationMArticleData.setThumb(thumb);
                }

                System.out.println("===============处理完头像============");
                // 处理正文,摘要
                String content = originNewsData.getContent();
                content = changeSpecialChat(content);
                content = changeContent(content);
                String description = originNew.getDescription();
                if (isLink == 0) {
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");

                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                    //没有头像就选择第一张图片为头像
                                    if (stationMArticleData.getThumb().isBlank()){
                                        stationMArticleData.setThumb(url);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getId());
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                    element.attr("src", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getId());
                    }
                    content = document.toString();
                } else {
                    // 是链接的话,content为空
                    content = linkUrl;
                    description = linkUrl;
                }
                System.out.println("===============处理完content============");
                stationMArticleData.setContent(content);
                stationMArticleData.setDescription(description);

                // 处理来源
                stationMArticleData.setComefrom(originNew.getComefrom());
                // 处理是否为外联
                stationMArticleData.setIsLink(isLink);
                // 处理发布时间
//                stationMArticleData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

                chunkResult.put(stationMArticle, stationMArticleData);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }

        return chunkResult;
    }





    @Test
    public void moveDoctorJava() {
        Map<Integer, Integer> map = new HashMap<>();
        //  TODO 根据实际情况修正
        map.put(151,5); //医师介绍
//        getDoctorId(map);   在医生投到对应科室下面时使用。根据具体情况进行修正

        Integer id = 0;
        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveDoctorJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("运行成功的任务ID有：");
        for (Integer i : success) {
            System.out.print(i+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void getDoctorId(Map<Integer, Integer> map){
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TblCategory::getModelid,2);
        List<TblCategory> tblCategories = tblCategoryMapper.selectList(wrapper);
        for (int i = 0; i < tblCategories.size(); i++) {
            LambdaQueryWrapper<StationCategory2> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(StationCategory2::getName,tblCategories.get(i).getCatname());
            StationCategory2 stationCategory2 = stationCategoryMapper.selectOne(wrapper1);
            map.put(tblCategories.get(i).getCatid(),stationCategory2.getId());
        }
    }



    public void moveDoctorJavaMethod(Integer originCatId,Integer targetCatId) {
//        Integer originCatId = 38;
//        Integer targetCatId = 246;
        DynamicDataSource.changeOldPhpDynamicDataSource();

        LambdaQueryWrapper<TblCManOldest> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCManOldest::getCatid, originCatId)
                .eq(TblCManOldest::getState, 0)
                .eq(TblCManOldest::getStatus, 99)
                .orderByAsc(TblCManOldest::getListorder);
        // TODO 临时测试
//                .last("limit 5");
        List<TblCManOldest> originMans = tblCManMapper.selectList(wrapper);

        List<TblCategory> tblCategories = tblCategoryMapper.selectList(null);
        Map<Integer, String> catMap = new HashMap<>();
        for (TblCategory tblCategory : tblCategories) {
            catMap.put(tblCategory.getCatid(), tblCategory.getCatname());
        }

        // 定义线程数量
        int threadCount = 6;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMDoctorData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, new HashMap<>())));

        // 分割任务
        List<List<TblCManOldest>> taskChunks = divideTasks(originMans, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMDoctorData2> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex), targetCatId, catMap);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMDoctorData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMDoctorData2> map : resultMaps) {
            if (map != null) { // 添加空值检查
                mergedMap.putAll(map);
            }
        }

        // 处理合并后的结果
        for (StationMReference targetMan : mergedMap.keySet()) {
            StationMDoctorData2 targetManData = mergedMap.get(targetMan);

            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetMan);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMDoctorDataMapper.insert(targetManData);
            //落库 -> folder_resource
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetManData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetManData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetMan.setDataId(Long.valueOf(folderResource.getId()));
            targetMan.setSortLevel(Math.toIntExact(targetMan.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetMan);
        }
    }

    private Map<StationMReference, StationMDoctorData2> processTaskChunk2(
            List<TblCManOldest> chunk, Integer targetCatId, Map<Integer, String> catMap) {
        Map<StationMReference, StationMDoctorData2> chunkResult = new LinkedHashMap<>();

        try {
            for (TblCManOldest originMan : chunk) {
                DynamicDataSource.changeOldPhpDynamicDataSource();
                // 新的doctor
                StationMReference stationMDoctor = new StationMReference();
                stationMDoctor.setCatId(targetCatId);
                stationMDoctor.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));
                stationMDoctor.setState(99);
                stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMDoctor.setViews(originMan.getViews());
                stationMDoctor.setIsTop(0);
//                stationMDoctor.setIsLock(0);

                // 找到修改前的manData并修改后存入新的doctorData
                LambdaQueryWrapper<TblCManDataOldest> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCManDataOldest::getId, originMan.getId());


                TblCManDataOldest originManData = tblCManDataMapper.selectOne(wrapper1);


                // 新的articleData
                StationMDoctorData2 stationMDoctorData = new StationMDoctorData2();
                stationMDoctorData.setUuid(UUID.randomUUID().toString());
                stationMDoctorData.setTitle(originMan.getTitle());
                stationMDoctorData.setIgnoreReason(null);
                stationMDoctorData.setCreateTime((double) System.currentTimeMillis());
                stationMDoctorData.setUpdateTime((double) System.currentTimeMillis());
                stationMDoctorData.setCreateUserId(1);
                stationMDoctorData.setUpdateUserId(1);
                stationMDoctorData.setState(2);

                // 缩略图处理 （只有一张图片）
                String thumb = originMan.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    if (!thumb.isBlank())
                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
                }
                stationMDoctorData.setThumb(thumb);

                // 处理正文,摘要
                String content = originManData.getContent();
                content = changeSpecialChat(content);
                Document document = Jsoup.parse(content);
                Elements imgList = document.select("img");
                Elements linkList = document.select("a");
                Elements videoList = document.select("video");
                // 图片路径集合处理
                try {
                    for (Element element : imgList) {
                        String imgUrl = element.attr("src");
                        if (StringUtils.isNotBlank(imgUrl)) {
                            imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(imgUrl)) {
                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                element.attr("src", url);
                                //没有头像就选择第一张图片为头像
                                if (stationMDoctorData.getThumb().isBlank()){
                                    stationMDoctorData.setThumb(url);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("图片下载失败,是资源id为:" + originManData.getId());
                }

                // 链接路径集合处理
                try {
                    for (Element element : linkList) {
                        String attachUrl = element.attr("href");
                        if (StringUtils.isNotBlank(attachUrl)) {
                            attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(attachUrl)) {
                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                element.attr("href", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("链接下载失败,是资源id为:" + originManData.getId());
                }

                // 视频路径集合处理
                try {
                    for (Element element : videoList) {
                        String videoUrl = element.attr("src");
                        if (StringUtils.isNotBlank(videoUrl)) {
                            videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                            if (StringUtils.isNotBlank(videoUrl)) {
                                String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                element.attr("src", url);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("视频下载失败,是资源id为:" + originManData.getId());
                }
                content = document.toString();

                stationMDoctorData.setContent(content);

                // 处理医师职称
                String protit = originMan.getProtit();
                if (!protit.isEmpty()) {
                    Map<Integer, Integer> docPositionMap = getDocPositionMap();
                    stationMDoctorData.setDocPosition(docPositionMap.get(Integer.valueOf(protit)));
                }

                // 处理擅长
                stationMDoctorData.setGoodat(originMan.getGoodat());
                // 处理科室
                //TODO 获取以前科室名称
                String depart = originMan.getDepart();
                if (!depart.isEmpty()) {
                    String[] departs = depart.split(",");
                    List<String> departNames = new ArrayList<>();
                    List<Integer> newDepartIds = new ArrayList<>();
                    for (int i = 0; i < departs.length; i++) {
                        departNames.add(catMap.get(Integer.valueOf(departs[i])));
                    }
                    //通过科室名称匹配现在科室id
                    DynamicDataSource.changeBuildDynamicDataSource();
                    LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
                    wrapper.in(StationCategory2::getName, departNames);
                    List<StationCategory2> stationCategories = stationCategoryMapper.selectList(wrapper);
                    for (StationCategory2 stationCategory : stationCategories) {
                        newDepartIds.add(stationCategory.getId());
                    }
                    stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
                }

                // 处理发布时间
//                stationMDoctorData.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));

                chunkResult.put(stationMDoctor, stationMDoctorData);
            }
        } catch (Exception e) {
            log.error("数据处理出错", e);
        }

        return chunkResult;
    }

//    private Map<StationMDoctor2, StationMDoctorData2> processTaskChunk2XBQ(
//            List<TblCMan> chunk, Integer targetCatId, Map<Integer, String> catMap) {
//        Map<StationMDoctor2, StationMDoctorData2> chunkResult = new LinkedHashMap<>();
//        try {
//            for (TblCMan originMan : chunk) {
//                DynamicDataSource.changeDefaultDataSource();
//                // 新的doctor
//                StationMDoctor2 stationMDoctor = new StationMDoctor2();
//                stationMDoctor.setCatId(targetCatId);
//                stationMDoctor.setPublishUserId(1);
//                stationMDoctor.setCreateTime(Double.valueOf(originMan.getInputtime() + "000"));
//                stationMDoctor.setUpdateTime(Double.valueOf(originMan.getUpdatetime() + "000"));
//                stationMDoctor.setState(99);
//                stationMDoctor.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
//                stationMDoctor.setViews(originMan.getViews());
//                stationMDoctor.setIsTop(0);
//                stationMDoctor.setIsLock(0);
//
//                // 找到修改前的manData并修改后存入新的doctorData
//                LambdaQueryWrapper<TblCManDataXbq> wrapper1 = new LambdaQueryWrapper<>();
//                wrapper1.eq(TblCManDataXbq::getId, originMan.getDataid());
//                TblCManDataXbq originManData = tblCManDataXbqMapper.selectOne(wrapper1);
//
//                // 新的articleData
//                StationMDoctorData2 stationMDoctorData = new StationMDoctorData2();
//                stationMDoctorData.setUuid(UUID.randomUUID().toString());
//                stationMDoctorData.setTitle(originManData.getTitle());
//
//                // 缩略图处理 （只有一张图片）
//                String thumb = originManData.getThumb();
//                // 调用接口
//                if (!thumb.isBlank()) {
//                    thumb = CrawlerManager.DownLoad(thumb, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                    thumb = CrawlerManager.changeFileUrl(new File(thumb));
//                }
//                stationMDoctorData.setThumb(thumb);
//
//                // 处理正文,摘要
//                String content = originManData.getContent();
//                content = changeSpecialChat(content);
//                Document document = Jsoup.parse(content);
//                Elements imgList = document.select("img");
//                Elements linkList = document.select("a");
//                Elements videoList = document.select("video");
//                // 图片路径集合处理
//                try {
//                    for (Element element : imgList) {
//                        String imgUrl = element.attr("src");
//                        if (StringUtils.isNotBlank(imgUrl)) {
//                            imgUrl = CrawlerManager.DownLoad(imgUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(imgUrl)) {
//                                String url = CrawlerManager.changeFileUrl(new File(imgUrl));
//                                element.attr("src", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("图片下载失败,是资源id为:" + originManData.getId());
//                }
//
//                // 链接路径集合处理
//                try {
//                    for (Element element : linkList) {
//                        String attachUrl = element.attr("href");
//                        if (StringUtils.isNotBlank(attachUrl)) {
//                            attachUrl = CrawlerManager.DownLoad(attachUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(attachUrl)) {
//                                String url = CrawlerManager.changeFileUrl(new File(attachUrl));
//                                element.attr("href", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("链接下载失败,是资源id为:" + originManData.getId());
//                }
//
//                // 视频路径集合处理
//                try {
//                    for (Element element : videoList) {
//                        String videoUrl = element.attr("src");
//                        if (StringUtils.isNotBlank(videoUrl)) {
//                            videoUrl = CrawlerManager.DownLoad(videoUrl, "C:\\Collect_Data\\xbqrmyy", "http://xbqrmyy.foxtest.net/");
//                            if (StringUtils.isNotBlank(videoUrl)) {
//                                String url = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
//                                element.attr("src", url);
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("视频下载失败,是资源id为:" + originManData.getId());
//                }
//                content = document.toString();
//
//                stationMDoctorData.setContent(content);
//
//                // 处理医师职称
//                String docPosition = originManData.getDocPosition();
//                if (!docPosition.isEmpty()) {
//                    stationMDoctorData.setDocPosition(Integer.valueOf(docPosition));
//                }
//
//                // 处理擅长
//                stationMDoctorData.setGoodat(originManData.getGoodat());
//                // 处理科室
//                //TODO 获取以前科室名称
//                String depart = originManData.getDepart();
//                if (!depart.isEmpty()) {
//                    String[] departs = depart.split(",");
//                    List<String> departNames = new ArrayList<>();
//                    List<Integer> newDepartIds = new ArrayList<>();
//                    for (int i = 0; i < departs.length; i++) {
//                        departNames.add(catMap.get(Integer.valueOf(departs[i])));
//                    }
//                    //通过科室名称匹配现在科室id
//                    DynamicDataSource.changeDynamicDataSource();
//                    LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
//                    wrapper.in(StationCategory2::getName, departNames);
//                    List<StationCategory2> stationCategories = stationCategoryMapper.selectList(wrapper);
//                    for (StationCategory2 stationCategory : stationCategories) {
//                        newDepartIds.add(stationCategory.getId());
//                    }
//                    stationMDoctorData.setDepart(JSONObject.toJSONString(newDepartIds));
//                }
//
//                // 处理发布时间
//                stationMDoctorData.setPublishTime(Double.valueOf(originMan.getInputtime() + "000"));
//
//                chunkResult.put(stationMDoctor, stationMDoctorData);
//            }
//        } catch (Exception e) {
//            log.error("解析数据失败", e);
//        }
//
//        return chunkResult;
//    }

    @Test
    public void moveNewsToTeacherJava() {
        Integer originCatId = 3843;
        Integer targetCatId = 254;

        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCNewsOldest> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCNewsOldest::getCatid, originCatId)
                .eq(TblCNewsOldest::getState, 0)
                .eq(TblCNewsOldest::getStatus, 99)
                .orderByAsc(TblCNewsOldest::getListorder);
        List<TblCNewsOldest> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        Map<StationMReference, StationMTeacherData2> newTeacherDataMap = new LinkedHashMap<>();
        for (TblCNewsOldest originNew : originNews) {
            // 新的teacher
            StationMReference stationMTeacher = new StationMReference();
            stationMTeacher.setCatId(targetCatId);
            stationMTeacher.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));
            stationMTeacher.setState(99);
            stationMTeacher.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
            stationMTeacher.setViews(originNew.getViews());
            stationMTeacher.setIsTop(0);

            // 找到修改前的newsData并修改后存入新的teacherData
            LambdaQueryWrapper<TblCNewsDataOldest> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TblCNewsDataOldest::getId, originNew.getId());
            TblCNewsDataOldest originNewsData = tblCNewsDataMapper.selectOne(wrapper1);

            // 新的articleData
            StationMTeacherData2 stationMTeacherData = new StationMTeacherData2();
            stationMTeacherData.setUuid(UUID.randomUUID().toString());
            stationMTeacherData.setTitle(originNew.getTitle());

            // 缩略图处理 （只有一张图片）
            String thumb = originNew.getThumb();
            // 调用接口
            if (!thumb.isBlank()) {
                thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                thumb = CrawlerManager.changeFileUrl(new File(thumb));
            }
            stationMTeacherData.setThumb(thumb);

            // 处理培养研究生学校、指导研究生层次、研究方向、邮箱
            String description = originNew.getDescription();
            description = changeSpecialChat(description);
            description = description.replaceAll("</br>", "");
            String[] split = description.split("\n");
            String school = "";
            String level = "";
            String direction = "";
            String mail = "";
            for (String s : split) {
                if (s.contains("联合培养研究生学校")) {
                    school = s.split("：")[1];
                } else if (s.contains("当前指导研究生层次")) {
                    level = s.split("：")[1];
                } else if (s.contains("研究方向")) {
                    direction = s.split("：")[1];
                } else if (s.contains("邮箱")) {
                    mail = s.split("：")[1];
                }
            }
            stationMTeacherData.setSchool(school);
            stationMTeacherData.setCurrentLevel(level);
            stationMTeacherData.setResearchDirection(direction);
            stationMTeacherData.setEmail(mail);

            // 处理发布时间
            stationMTeacherData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

            newTeacherDataMap.put(stationMTeacher, stationMTeacherData);
        }

        // 处理合并后的结果
        for (StationMReference targetTeacher : newTeacherDataMap.keySet()) {
            StationMTeacherData2 targetTeacherData = newTeacherDataMap.get(targetTeacher);
            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetTeacher);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMTeacherDataMapper.insert(targetTeacherData);
            //落库 -> folder_resource
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetTeacherData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetTeacherData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetTeacher.setDataId(Long.valueOf(folderResource.getId()));
            targetTeacher.setSortLevel(Math.toIntExact(targetTeacher.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetTeacher);
        }
    }


    public void getPicIds(Map<Integer, Integer> map){
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCPicture> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TblCPicture::getState,0)
                .eq(TblCPicture::getStatus,99)
                .select(TblCPicture::getCatid);
        List<Integer> catIds = tblCPictureMapper.selectObjs(wrapper);
        List<Integer> catId = catIds.stream().distinct().collect(Collectors.toList());


        //根据CatId TO Find CategoryName
        LambdaQueryWrapper<TblCategory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.in(TblCategory::getCatid,catId)
                .select(TblCategory::getCatid,TblCategory::getCatname);
        List<TblCategory> tblCategories = tblCategoryMapper.selectList(wrapper1);

        List<String> failure = new ArrayList<>();

        //According to CategoryName to Find TargetId
        DynamicDataSource.changeBuildDynamicDataSource();
        for (TblCategory tblCategory : tblCategories) {
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getName,tblCategory.getCatname());
            List<StationCategory2> stationCategory2s = stationCategoryMapper.selectList(wrapper2);
            if (stationCategory2s.size()!=1){
                failure.add(tblCategory.getCatname());
                continue;
            }
            map.put(tblCategory.getCatid(),stationCategory2s.get(0).getId());
        }

        System.out.println("===========未找到的栏目：=================");
        System.out.println(failure);

    }

    @Test
    public void testGetPicIds(){
        Map<Integer, Integer> map = new HashMap<>();
        getPicIds(map);
        System.out.println(map);
    }




    // 导入视频
    @Test
    public void moveVideoJava() {
        Map<Integer, Integer> map = new HashMap<>();

        map.put(21,20);  //医院视频

        Integer id = 0;

        Integer[] success = new Integer[map.size()];
        int count = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveVideoJavaMethod(originCatId, map.get(originCatId));
                success[count++] = originCatId;
            } catch (Exception e) {
                log.error("moveVideoJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        System.out.println("运行成功的任务ID有：");
        for (Integer i : success) {
            System.out.print(i+" ");
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void moveVideoJavaMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCVideoOldest> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCVideoOldest::getCatid, originCatId)
                .eq(TblCVideoOldest::getState, 0)
                .eq(TblCVideoOldest::getStatus, 99)
                .orderByAsc(TblCVideoOldest::getListorder);
        List<TblCVideoOldest> originVideo = tblCVideoMapper.selectList(wrapper);
        if (originVideo.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 3;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<StationMReference, StationMVideoData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCVideoOldest>> taskChunks = divideTasks(originVideo, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<StationMReference, StationMVideoData2> chunkResult =
                            processTaskChunk3(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<StationMReference, StationMVideoData2> mergedMap = new LinkedHashMap<>();
        for (Map<StationMReference, StationMVideoData2> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 处理合并后的结果
        for (StationMReference targetNew : mergedMap.keySet()) {
            StationMVideoData2 targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.insert(targetNew);
            LambdaQueryWrapper<StationCategory2> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(StationCategory2::getId,targetCatId);
            StationCategory2 stationCategory2 = stationCategoryMapper.selectOne(wrapper2);

            DynamicDataSource.changeResourceDynamicDataSource();
            stationMVideoDataMapper.insert(targetNewData);
            //落库 -> folder_resource
            new FolderResource();
            FolderResource folderResource= FolderResource.builder()
                    .userId(1).folderId(1).modelId(stationCategory2.getModelId())
                    .resourceId(targetNewData.getDataId())
                    .createTime((double) System.currentTimeMillis())
                    .updateTime((double) System.currentTimeMillis())
                    .version(1).sort(targetNewData.getDataId())
                    .listOrder(0).state(2).isDeleted(0).build();
            ApplicationContextProvider.getBeanByType(FolderResourceMapper.class).insert(folderResource);

            // 引用数据引用资源数据id
            targetNew.setDataId(Long.valueOf(folderResource.getId()));
            targetNew.setSortLevel(Math.toIntExact(targetNew.getId()));

            DynamicDataSource.changeBuildDynamicDataSource();
            stationMReferenceMapper.updateById(targetNew);
        }
    }



    private Map<StationMReference, StationMVideoData2> processTaskChunk3(
            List<TblCVideoOldest> chunk, Integer targetCatId) {
        Map<StationMReference, StationMVideoData2> chunkResult = new LinkedHashMap<>();
        try {
            for (TblCVideoOldest originNew : chunk) {
                // 新的article
                StationMReference stationMVideo = new StationMReference();
                stationMVideo.setCatId(targetCatId);
                stationMVideo.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));
                stationMVideo.setState(99);
                stationMVideo.setUri("/" + CrawlerManager.randomCharacterGenerator() + ".html");
                stationMVideo.setViews(originNew.getViews());
                stationMVideo.setIsTop(0);


                String linkUrl = originNew.getUrl();
                int isLink = 1;
                // TODO 根据url判断是否为外链(具体网站具体分析)
                if (linkUrl.contains("myzyy")) {
                    isLink = 0;
                }

                DynamicDataSource.changeOldPhpDynamicDataSource();
                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblCVideoDataOldest> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCVideoDataOldest::getId, originNew.getId());
                TblCVideoDataOldest originVideoData = tblCVideoDataMapper.selectOne(wrapper1);

                // 新的articleData
                StationMVideoData2 stationMVideoData = new StationMVideoData2();
                stationMVideoData.setUuid(UUID.randomUUID().toString());
                stationMVideoData.setTitle(originNew.getTitle());
                stationMVideoData.setCreateUserId(1);
                stationMVideoData.setUpdateUserId(1);
                stationMVideoData.setCreateTime(Double.valueOf(originNew.getInputtime()+"000"));
                stationMVideoData.setUpdateTime(Double.valueOf(originNew.getUpdatetime()+"000"));
                stationMVideoData.setState(2);
                // 处理作者
//                String input = originNew.getAuthor();
                // TODO 根据实际处理
//                Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]+");
//                Matcher matcher = pattern.matcher(input);
//                StringBuilder author = new StringBuilder();
//                while (matcher.find()){
//                    author.append(matcher.group());
//                }
//                System.out.println(author);

//                author = changeSpecialChat(author);
//                if (!author.equals("0") && !author.toString().isEmpty())
////                    stationMVideoData.setAuthor(originVideoData.getAuthor());
//                    stationMVideoData.setAuthor(author.toString());

                // 缩略图处理 （只有一张图片）
                String thumb = originNew.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    if (!thumb.isBlank()){
                        thumb = CrawlerManager.changeFileUrl(new File(thumb));
                    }
                }
                stationMVideoData.setThumb(thumb);

                // 处理正文,摘要
                String content = originVideoData.getContent();
                String description = originNew.getDescription();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    if (StringUtils.isBlank(description)) {
                        String text = document.text();
                        text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                        description = text;
                    } else {
                        description = StringUtils.trim(description);
                    }

                    // 图片路径集合处理
                    try {
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(imgUrl));
                                    element.attr("src", url);
                                    //没有头像就选择第一张图片为头像
                                    if (stationMVideoData.getThumb().isBlank()){
                                        stationMVideoData.setThumb(url);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originVideoData.getId());
                    }

                    // 链接路径集合处理
                    try {
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    String url = CrawlerManager.changeFileUrl(new File(attachUrl));
                                    element.attr("href", url);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originVideoData.getId());
                    }

                    // 视频路径集合处理
                    String video = "";
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    video = CrawlerManager.getChunkVideoUrl(new File(videoUrl));
                                }
                                element.remove();
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originVideoData.getId());
                    }
                    stationMVideoData.setVideo(video);
                    content = document.toString();
                } else {
                    // 是链接的话,content为空
                    content = linkUrl;
                }
                //转义
                content = changeContent(content);

                stationMVideoData.setContent(content);
                stationMVideoData.setDescription(description);

                // 处理来源
                stationMVideoData.setComefrom(originNew.getComefrom());
                // 处理是否为外联
//                stationMVideoData.setIsLink(isLink);
                // 处理发布时间
//                stationMVideoData.setPublishTime(Double.valueOf(originNew.getInputtime() + "000"));

                chunkResult.put(stationMVideo, stationMVideoData);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }

        return chunkResult;
    }



/**
 * @description:获得每个科室下面的新闻
 * @author: RXH
 * @date: 2025/8/6 16:29
 * @param: [map]
 * @return: void
 **/

    public void getDepartNewsID(Map<Integer, Integer> map){
        // 找到所有科室的下的新闻
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCategory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TblCategory::getModelid,2)
                .eq(TblCategory::getCatname,"健康知识");   //等会换成健康知识&门诊排班&科室动态
        List<TblCategory> hmsCategories = tblCategoryMapper.selectList(wrapper1);
        Integer[] origin = new Integer[hmsCategories.size()];

        //找到科室动态对应的科室名称
        String[] DepartName = new String[origin.length];
        for (int i = 0; i < hmsCategories.size(); i++) {
            origin[i]= Math.toIntExact(hmsCategories.get(i).getCatid());

            LambdaQueryWrapper<TblCategory> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(TblCategory::getCatid,hmsCategories.get(i).getParentid());
            DepartName[i]=tblCategoryMapper.selectOne(wrapper2).getCatname();
        }
        //根据DepartName一致找到targetID
        List<String> success = new ArrayList<>();
        DynamicDataSource.changeBuildDynamicDataSource();
        for (int i = 0; i < origin.length; i++) {
            LambdaQueryWrapper<StationCategory2> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationCategory2::getName,DepartName[i]);
            System.out.println(stationCategoryMapper.selectList(wrapper));
            List<StationCategory2> stationCategory2 = stationCategoryMapper.selectList(wrapper);
            if (stationCategory2.size()!=1)
                continue;
            LambdaQueryWrapper<StationCategory2> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(StationCategory2::getPid,stationCategory2.get(0).getId())
                    .eq(StationCategory2::getName,"健康知识");
            StationCategory2 stationCategory = stationCategoryMapper.selectOne(wrapper3);
            if (stationCategory==null)
                continue;
            map.put(origin[i],stationCategory.getId());
            String tmp = "map.put("+origin[i]+","+stationCategory.getId()+");   // "+DepartName[i];
            success.add(tmp);
        }
        for (String s : success) {
            System.out.println(s);
        }
        System.out.println(map);
    }



    //手动转义
    public String changeContent(String text) {
        return text.replace("&quot;", "\"");
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }

    @Test
    public void testChangeChat(){
        String test = "&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302line-height:15.96px&#300&#302>我院现对下列项目征集相关市场调研资料，请符合相关项目要求且具有合法合格资质的公司或厂家积极参加本次调研。&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>一、调研项目清单：&#305span style&#304&#302color:#ff0000&#300&#302>详见调研报名网站&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>二、注意事项&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>1.请参与调研的供应商或厂家于&#305span style&#304&#302color:#ff0000&#300&#302>2025年7月29&#305/span>日前，按报名操作手册（见附件1）进行线上报名，并线上填写信息采集内容。请各供应商或厂家准确填写相关信息，并对所填信息的真实有效性负责,如发现所填报信息虚假，则此次调研报名作无效处理。&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>2.产品彩页&#305span style&#304&#302color:#ff0000&#300&#302>：上传彩页请根据挂网调研设备名称逐一命名，保持与挂网调研设备名称一致&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302color:#ff0000&#300&#302>3.产品技术参数表（附件2）：请下载公告附件3模板按要求如实填写，此表必须上传PDF盖章版以及可编辑的word版，共两种文档&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>&#305span style&#304&#302color:#ff0000&#300&#302>4.市场同类产品参数对照表（附件3）：请下载公告附件4模板如实填写并上传&#305/span>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>三、联系人&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:15.96px&#300text-align:left&#300&#302>医学工程部 &nbsp&#300 沈老师 &nbsp&#300 028-62346182&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>四、报名方式&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>1、线上报名&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>2、报名链接：&#305a href&#304&#302https://mk.cd5120.com:28083/admin/login&#302 data_ue_src&#304&#302https://mk.cd5120.com:28083/admin/login&#302>&#305span style&#304&#302color:#0000ff&#300&#302>https://mk.cd5120.com:28083/admin/login&#305/span>&#305/a> &#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>3、报名操作手册：详见附件2&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305br />&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20250722/134226259.docx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20250722/134226259.docx&#302>调研项目清单.docx&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240130/101429228.pptx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240130/101429228.pptx&#302>附件1：供应商报名操作手册.pptx&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240521/104956502.doc&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240521/104956502.doc&#302>附件2：产品技术参数表.doc&#305/a>&#305/p>&#305p style&#304&#302line-height:15.96px&#300text-indent:2em&#300text-align:left&#300&#302>&#305a href&#304&#302https://cd5120.my120.org/oss/20240521/104956616.xlsx&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20240521/104956616.xlsx&#302>附件3：市场同类产品参数对照表.xlsx&#305/a>&#305/p>&#305p>&#305br />&#305/p>";
        System.out.println(changeSpecialChat(test));
    }

    @Test
    public void test() {
        String Refer = "http://jrha.lan24.foxtest.net/";
        String s = CrawlerManager.DownLoad("http://jrha.1024199.foxtest.net/oss/20241203/115322689_2000_0_50.jpg", downloadPath, Refer);
        String s1 = CrawlerManager.changeFileUrl(new File(s));
        log.info(s);
        log.info(s1);
        System.out.println(s);
    }

    @Test
    public void test2() {
        String a = "姓名：徐俊波&#305/br>\n" +
                "联合培养研究生学校：西南交通大学、川北医学院&#305/br>\n" +
                "当前指导研究生层次：硕士研究生&#305/br>\n" +
                "研究方向：高血压，心力衰竭等心血管病的药物器械治疗&#300医院管理&#305/br>\n" +
                "邮箱：<EMAIL>";
        a = changeSpecialChat(a);
        a = a.replaceAll("</br>", "");
        String[] split = a.split("\n");
        String name = "";
        String school = "";
        String level = "";
        String direction = "";
        String mail = "";
        for (String s : split) {
            if (s.contains("姓名")) {
                name = s.split("：")[1];
            } else if (s.contains("联合培养研究生学校")) {
                school = s.split("：")[1];
            } else if (s.contains("当前指导研究生层次")) {
                level = s.split("：")[1];
            } else if (s.contains("研究方向")) {
                direction = s.split("：")[1];
            } else if (s.contains("邮箱")) {
                mail = s.split("：")[1];
            }
        }
        System.out.println(name);
        System.out.println(school);
        System.out.println(level);
        System.out.println(direction);
        System.out.println(mail);
    }

    public String leaveChinese(String input){
        input = input.replaceAll("[^\\u4e00-\\u9fa5,、；; （/）]", "");
        while (input.charAt(input.length()-1)==','||input.charAt(input.length()-1)=='、'||input.charAt(input.length()-1)=='；'||input.charAt(input.length()-1)==';'){
            input=input.substring(0,input.length()-1);
        }
        return input;
    }
    @Test
    public void testFilter(){
        String input = "array (\n" +
                "  0 => ' 陈静)',\n" +
                "  1 => ' ',\n" +
                "  2 => ' ',\n" +
                "  3 => ' ',\n" +
                "  4 => ' ',\n" +
                "  5 => ' ',\n" +
                "  6 => ' ',\n" +
                "  7 => ' ',\n" +
                "  8 => ' ',\n" +
                "  9 => ' ',\n" +
                "  10 => ' ',\n" +
                "  11 => ' ',\n" +
                "  12 => ' ',\n" +
                ")";
        String input2 = "array &#303\n" +
                "  0 &#304> &#301伍黎黎&#301,\n" +
                "  1 &#304> &#301刘小娟&#301,\n" +
                "  2 &#304> &#301江咏梅&#301,\n" +
                ")";
        String input3 = "&#301李知默&#301刘小娟&#301江咏梅、&#301刘小娟&#301";
        String input4 = "&#305p style&#304&#302text-align:left&#300text-indent:2em&#300&#302>&#305span style&#304&#302color:#333333&#300letter-spacing:0px&#300font-style:normal&#300background:#ffffff&#300&#302>&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302> &#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>又到粽叶飘香时，端午节应时而来。为进一步弘扬传统文化，丰富&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>退休&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>职工的&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>精神&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>文化生活&#305/span>&#305span style&#304&#302color:#333333&#300background:#ffffff&#300&#302>，传递组织关爱，&#305/span>2025年5月28日，医院人力资源部退休办公室代表医院组织举办了退休职工端午主题活动，本次活动共计参与人数200余人&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>医院共组织10余位志愿者为退休职工贴心服务，本次主题活动精心为退休职工准备了“书香阅读角”，发放《离退休工作简报》《枫华》《心系下一代》等相关杂志，让老师们了解国家、学校退休工作的新动向，分享退休生活心得，传递读书的喜悦。医院退休办公室精心编撰的《退休一本通》凝聚了医院的关爱，小礼品国风团扇也为退休老师们送来了缕缕夏日清风和对传统节日美好心思的寄托。&#305/p>&#305p style&#304&#302text-align:center&#302>&#305img src&#304&#302https://oss.motherchildren.com/20250529/154902303.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/154902303.png&#302 title&#304&#3025ddf839e6ad9a0d47798bad3fd2e193&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305img src&#304&#302https://oss.motherchildren.com/20250529/155013977.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155013977.png&#302 title&#304&#302986a08bef941191ead36debe4a2cbdd&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305/p>&#305p style&#304&#302text-align:center&#302>&#305img src&#304&#302https://oss.motherchildren.com/20250529/155014881.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155014881.png&#302 title&#304&#3025789ddb8722c7da3278e3e25c6cb5d8&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305img src&#304&#302https://oss.motherchildren.com/20250529/155013769.png&#302 data_ue_src&#304&#302https://oss.motherchildren.com/20250529/155013769.png&#302 title&#304&#30289ca6a03218f59695317ea1cbbc910f&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 width&#304&#302400&#302 height&#304&#302548&#302 style&#304&#302width:400px&#300height:548px&#300&#302 />&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>端午临仲夏，时清日复长。活动当天，老朋友、老同事们欢乐相聚拍照留念，或座谈共话安康，或读书细品书香。退休办公室张静主任代表医院领导向大家表达了医院对退休职工的关心关爱，祝福大家&#305span style&#304&#302color:#000000&#300&#302>老有所乐，共享幸福！并传达了近期&#305/span>学校、医院工作重点，及时让退休职工了解学校、医院建设发展的新动向。&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>退休老师们感谢医院组织端午主题活动，既传承了端午传统文化，又增进了老同志之间的交流与情谊，大家都深切感受到组织的温暖关怀，归属感和幸福感进一步提升，表示期待下一次的相聚！&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300&#302>&#305br style&#304&#302text-indent:2em&#300text-align:left&#300&#302 />&#305/p>";

        input = changeSpecialChat(input);
        input2 = changeSpecialChat(input2);
        input3 = changeSpecialChat(input3);
        input4 = changeSpecialChat(input4);
        // TODO 根据实际处理
//        input = leaveChinese(input);
//        input2 = leaveChinese(input2);
//        System.out.println(input);
//        System.out.println(input2);
//        System.out.println(input3);
        System.out.println(input4);
//        System.out.println(leaveChinese(input3));
    }

    private Map<Integer, Integer> getDocPositionMap() {
        Map<Integer, Integer> titleLevelMap = new HashMap<>();

        // 医师系列  TODO 根据实际调整
        titleLevelMap.put(20, 1);  // 主任医师
        titleLevelMap.put(16, 2);  // 副主任医师
        titleLevelMap.put(12, 5);  // 主治医师
        titleLevelMap.put(8, 15); // 医师
//        titleLevelMap.put(8, 12); // 住院医师
        titleLevelMap.put(4, 13); // 医士

        // 心理咨询师系列
        titleLevelMap.put(96, 14); // 一级心理咨询师
        titleLevelMap.put(86, 15); // 二级心理咨询师
        titleLevelMap.put(76, 16); // 三级心理咨询师

        // 护师系列
        titleLevelMap.put(19, 8);  // 主任护师
        titleLevelMap.put(15, 9);  // 副主任护师
        titleLevelMap.put(11, 10);  // 主管护师
        titleLevelMap.put(7, 17); // 护师
        titleLevelMap.put(3, 18); // 护士

        // 技师系列
        titleLevelMap.put(17, 0); // 主任技师
        titleLevelMap.put(13, 20); // 副主任技师
        titleLevelMap.put(9, 21); // 主管技师
        titleLevelMap.put(5, 22); // 技师
        titleLevelMap.put(1, 23); // 技士

        // 药师系列
        titleLevelMap.put(18, 6);  // 主任药师
        titleLevelMap.put(14, 7);  // 副主任药师
        titleLevelMap.put(10, 24); // 主管药师
        titleLevelMap.put(6, 25); // 药师
        titleLevelMap.put(2, 26); // 药士

        return titleLevelMap;
    }



    /**
     * @description:华西二
     * @author: RXH
     * @date: 2025/8/5 17:04
     * @param: []
     * @return: void
     **/


    public void motherChildren(){




    }


    /**
     * @description:成都第五栏目
     * @author: RXH
     * @date: 2025/8/1 15:42
     * @param:
     * @return:
     **/
    public void cd5120(){
        //        map.put(18,17);   // 院务公开  0
//        map.put(2404,44);   // 伦理委员会动态  0
//        map.put(1538,40);   // 科研动态  0
//        map.put(55,36);   // 廉政建设  0
//        map.put(57,37);   // 青春风采  0
//        map.put(2389,20);   // 招贤纳士  0
//        map.put(1544,26);   // 患者须知  0
//        map.put(1567,38);   // 职工之家  0
//        map.put(2391,32);   // 体检套餐  0
//        map.put(2406,48);   // 机构动态
//        map.put(2426,39);   // 劳模风采
//        map.put(2540,6);   // 省政府信息
//        map.put(2550,57);   // 住培专栏
//        map.put(2551,411);   // 进修申请
//        map.put(2533,18);   // 招标采购
//        map.put(19,16);   // 医院动态
//        map.put(20,19);   // 媒体报道
//        getDepartNewsID(map);

//        //最后四个栏目:
//        map.put(2405,45);   //伦理委员会-文件制度
//        map.put(2263,49);   //国家药物临床试验机构-文件制度

//        map.put(88,33);     //健康促进-健康科普
    }
}
