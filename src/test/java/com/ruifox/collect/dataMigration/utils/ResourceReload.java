package com.ruifox.collect.dataMigration.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.aop.impl.Notice;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station.StationMArticleMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorMapper;
import com.ruifox.collect.dao.mapper.station.StationMImageMapper;
import com.ruifox.collect.dao.mapper.station.StationMTeacherMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.dao.mapper.tbl.TblCManDataMxfyMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureDataMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCategoryMapper;
import com.ruifox.collect.dao.mapper.tbl_oldest.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.StationMArticleData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description:重新下载因为网络波动未上传成功的文件(适合小批量未成功)《src有对应文件地址类型》
 * @author: RXH
 * @date: 2025/7/28 15:28
 * @param:
 * @return:
 **/

@SpringBootTest
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
public class ResourceReload {
    @Autowired
    private TblCNewsMapperOldest tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapperOldest tblCNewsDataMapper;
    @Autowired
    private TblCManDataMxfyMapper tblCManDataMxfyMapper;
    @Autowired
    private StationMArticleMapper stationMArticleMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    private StationMDoctorDataMapper2 stationMDoctorDataMapper;
    @Autowired
    private StationMTeacherMapper stationMTeacherMapper;
    @Autowired
    private StationMTeacherDataMapper2 stationMTeacherDataMapper;
    @Autowired
    private StationCategoryMapper2 stationCategoryMapper;
    @Autowired
    private TblCategoryMapper tblCategoryMapper;
    @Autowired
    private TblCPictureMapper tblCPictureMapper;
    @Autowired
    private TblCPictureDataMapper tblCPictureDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Autowired
    private StationMImageDataMapper2 stationMImageDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private TblCVideoMapperOldest tblCVideoMapper;
    @Autowired
    private TblCVideoDataMapperOldest tblCVideoDataMapper;
    @Autowired
    private StationMVideoDataMapper2 stationMVideoDataMapper;
    @Autowired
    private FolderResourceMapper folderResourceMapper;



    @Value("${testDownload.config.refer}")
    private String refer;

    @Value("${testDownload.config.fileName}")
    private String downloadPath;

    private static final Integer SRC = 0;
    private static final Integer HREF = 1;
    private static final Integer THUMB = 2;

    @Autowired
    private Notice notice;


    @PostConstruct
    public void init(){
        notice.say();
    }

    /**
     * @description:重新下载所有未下载的资源
     * @author: RXH
     * @date: 2025/8/6 14:50
     * @param: []
     * @return: void
     **/
    @Test
    public void reloadAllNews(){
        List<StationMArticleData2> srcId = getSrcId();
        List<StationMArticleData2> hrefId = getHrefId();
        List<StationMArticleData2> thumbId = getThumbId();
        try {
            reloadNewsJavaMethod(thumbId,THUMB);
            reloadNewsJavaMethod(srcId,SRC);
            reloadNewsJavaMethod(hrefId,HREF);
        } catch (Exception e) {
            log.error("reloadNewsJavaMethod error:", e);
        }
    }


/**
 * @description:获得所有资源
 * @author: RXH
 * @date: 2025/8/7 17:43
 * @param: []
 * @return: void
 **/
    @Test
    public void testNews(){
        List<StationMArticleData2> srcId = getSrcId();
        List<StationMArticleData2> hrefId = getHrefId();
        List<StationMArticleData2> thumbId = getThumbId();
        System.out.println("srcId.size() = "+srcId.size());;
        System.out.println("hrefId.size() = "+hrefId.size());
        System.out.println("thumbId.size() = "+thumbId.size());
    }

    /**
     * @description:根据未下载成功的标识符Src找到未下载的数据
     * @author: RXH
     * @date: 2025/8/6 14:48
     * @param: []
     * @return: java.util.List<com.ruifox.collect.module.entity.station2.StationMArticleData2>
     **/
    public List<StationMArticleData2> getSrcId(){
        DynamicDataSource.changeResourceDynamicDataSource();
        //TODO 每次根据实际修改
        String pattern = " src=\"https://oss.motherchildren.com/";
//        String pattern = " src=\"https://cmc.motherchildren";
        LambdaQueryWrapper<StationMArticleData2> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StationMArticleData2::getContent,pattern);
        return stationMArticleDataMapper.selectList(wrapper);
    }
    /**
     * @description:找到未下载成功的HrefId
     * @author: RXH
     * @date: 2025/8/7 17:33
     * @param: [idList]
     * @return: void
     **/
    public List<StationMArticleData2> getHrefId(){
        DynamicDataSource.changeResourceDynamicDataSource();
        //TODO 每次根据实际修改
//        String pattern = " href=\"https://oss.motherchildren.com/";
        String pattern = " href=\"https://cmc.motherchildren";

        LambdaQueryWrapper<StationMArticleData2> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StationMArticleData2::getContent,pattern);
        return stationMArticleDataMapper.selectList(wrapper);
    }

    /**
     * @description:找到头像未下载成功的
     * @author: RXH
     * @date: 2025/8/6 14:48
     * @param: []
     * @return: java.util.List<com.ruifox.collect.module.entity.station2.StationMArticleData2>
     **/
    public List<StationMArticleData2> getThumbId(){
        DynamicDataSource.changeResourceDynamicDataSource();
        //TODO 每次根据实际修改
        String pattern = "https://oss.motherchildren.com/";
        String pattern2 = "https://cmc.motherchildren";
        LambdaQueryWrapper<StationMArticleData2> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StationMArticleData2::getThumb,pattern)
                .or()
                .like(StationMArticleData2::getThumb,pattern2);
        return stationMArticleDataMapper.selectList(wrapper);
    }

    /**
     * @description:reload All News
     * @author: RXH
     * @date: 2025/8/5 17:45
     * @param: [newsId]
     * @return: void
     **/
    public void reloadNewsJavaMethod(List<StationMArticleData2> originNews,Integer type) {
        // 找到所有存在的信息
        if (originNews.isEmpty()) {
            return;
        }
        // 定义线程数量
        int threadCount = 60;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<List<StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<StationMArticleData2>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    List<StationMArticleData2> chunkResult = null;
                    //type 为0-》src 为1-》href
                    if (Objects.equals(type, SRC)){
                        chunkResult = processTaskChunkSrc(taskChunks.get(chunkIndex));
                    }else if (Objects.equals(type, HREF)){
                        chunkResult = processTaskChunkHref(taskChunks.get(chunkIndex));
                    }else {
                        chunkResult = processTaskChunkThumb(taskChunks.get(chunkIndex));
                    }
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        List<StationMArticleData2> mergedMap = new ArrayList<>();
        for (List<StationMArticleData2> map : resultMaps) {
            mergedMap.addAll(map);
        }

        int affect = 0;
        // 处理合并后的结果
        if (type.equals(SRC)){
            for (StationMArticleData2 stationMArticleData2 : mergedMap) {
                LambdaUpdateWrapper<StationMArticleData2> wrapper1 = new LambdaUpdateWrapper<>();
                wrapper1.eq(StationMArticleData2::getDataId,stationMArticleData2.getDataId())
                        .set(StationMArticleData2::getThumb,stationMArticleData2.getThumb())
                        .set(StationMArticleData2::getContent,stationMArticleData2.getContent());
                int update = stationMArticleDataMapper.update(wrapper1);
                affect += update;
            }
        }else if (type.equals(HREF)){
            for (StationMArticleData2 stationMArticleData2 : mergedMap) {
                LambdaUpdateWrapper<StationMArticleData2> wrapper1 = new LambdaUpdateWrapper<>();
                wrapper1.eq(StationMArticleData2::getDataId,stationMArticleData2.getDataId())
                        .set(StationMArticleData2::getContent,stationMArticleData2.getContent());
                int update = stationMArticleDataMapper.update(wrapper1);
                affect += update;
            }
        }else {
            for (StationMArticleData2 stationMArticleData2 : mergedMap) {
                LambdaUpdateWrapper<StationMArticleData2> wrapper1 = new LambdaUpdateWrapper<>();
                wrapper1.eq(StationMArticleData2::getDataId,stationMArticleData2.getDataId())
                        .set(StationMArticleData2::getThumb,stationMArticleData2.getThumb());
                int update = stationMArticleDataMapper.update(wrapper1);
                affect += update;
            }
        }
        System.out.println("=======================处理结束，一共重新导入了"+affect+"条数据！====================");
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start=end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块Src
     */
    private List<StationMArticleData2> processTaskChunkSrc(
            List<StationMArticleData2> chunk) {
        List<StationMArticleData2> chunkResult = new ArrayList<>();
        try {
            for (StationMArticleData2 originNew : chunk) {
                String newContent = modifySrcContent(originNew.getContent(),originNew);
                originNew.setContent(newContent);
                chunkResult.add(originNew);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }
        return chunkResult;
    }


    /**
     * 处理一个任务块Href
     */
    private List<StationMArticleData2> processTaskChunkHref(
            List<StationMArticleData2> chunk) {
        List<StationMArticleData2> chunkResult = new ArrayList<>();
        try {
            for (StationMArticleData2 originNew : chunk) {
                String newContent = modifyHrefContent(originNew.getContent());
                originNew.setContent(newContent);
                chunkResult.add(originNew);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }
        return chunkResult;
    }

    /**
     * 处理一个任务块Thumb
     */
    private List<StationMArticleData2> processTaskChunkThumb(
            List<StationMArticleData2> chunk) {
        List<StationMArticleData2> chunkResult = new ArrayList<>();
        try {
            for (StationMArticleData2 originNew : chunk) {
                String newThumb = modifyThumb(originNew.getThumb());
                originNew.setThumb(newThumb);
                chunkResult.add(originNew);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }
        return chunkResult;
    }

    /**
     * @description:修改Href
     * @author: RXH
     * @date: 2025/8/7 17:44
     * @param: [content]
     * @return: java.lang.String
     **/

    public String modifyHrefContent(String content){
        //找到 href=的位置
        Pattern pattern = Pattern.compile(" href=\"([^\"]*)\"");

        Matcher matcher = pattern.matcher(content);
        StringBuilder result = new StringBuilder();

        while (matcher.find()){
            //获取下载链接
            String matchStr = matcher.group(1);
            String oldUrl;
            //已经上传成功的数据
            if (matchStr.contains("***********:9000")||matchStr.isEmpty()||matchStr.contains("shtml")||matchStr.contains("html"))
                continue;
            oldUrl = matchStr;
            //完成下载上传
            String newUrl = CrawlerManager.DownLoad(oldUrl,downloadPath,refer);
            newUrl = CrawlerManager.changeFileUrl(new File(newUrl));
            if (newUrl.isEmpty()||newUrl.isBlank()){//上传失败，就用原来的地址链接
                matcher.appendReplacement(result, matcher.group());
                continue;
            }
            matcher.appendReplacement(result, " href=\""+newUrl+"\" ");
        }
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * @description:修改Src
     * @author: RXH
     * @date: 2025/8/7 17:44
     * @param: [content, data]
     * @return: java.lang.String
     **/

    public String modifySrcContent(String content,StationMArticleData2 data){

        //找到<img src=的位置
        Pattern pattern = Pattern.compile(" src=\"([^\"]*)\"");

        Matcher matcher = pattern.matcher(content);
        StringBuilder result = new StringBuilder();

        while (matcher.find()){
            //获取下载链接
            String matchStr = matcher.group(1);
            String oldUrl = matchStr;
            //已经上传成功的数据
            if (matchStr.contains("***********:9000")||matchStr.isEmpty())
                continue;
            oldUrl = matchStr;
            //完成下载上传
            String newUrl = CrawlerManager.DownLoad(oldUrl,downloadPath,refer);
            newUrl = CrawlerManager.changeFileUrl(new File(newUrl));
            if (newUrl.isEmpty()||newUrl.isBlank()){//上传失败，就用原来的地址链接
                matcher.appendReplacement(result, matcher.group());
                continue;
            }
            //获取第一张图片为头像（若没有头像）
            String thumb = data.getThumb();
            if (thumb == null || thumb.isBlank() || !thumb.contains("***********")){
                data.setThumb(newUrl);
            }
            matcher.appendReplacement(result, " src=\""+newUrl+"\" ");
        }
        matcher.appendTail(result);
        return result.toString();
    }


    /**
     * @description:修改Thumb
     * @author: RXH
     * @date: 2025/8/7 17:44
     * @param: [content, data]
     * @return: java.lang.String
     **/

    public String modifyThumb(String thumb){
        //完成下载上传
        String newUrl = CrawlerManager.DownLoad(thumb,downloadPath,refer);
        newUrl = CrawlerManager.changeFileUrl(new File(newUrl));
        return newUrl;
    }


//==================================================================================================
//==================================================================================================
//==================================================================================================

    /**
     * @description:下载某栏目下的所有为下载成功的数据
     * @author: RXH
     * @date: 2025/8/7 17:35
     * @param: []
     * @return: void
     **/
    @Test
    public void reloadNewsJava() {
        Integer catId = 23;
        List<Integer> idList = getNewsId(getResourceIds(getFolderIds(catId)));
//        List<Integer> idList = new ArrayList<>();
        try {
            reloadNewsJavaMethodOld(idList);
        } catch (Exception e) {
            log.error("reloadNewsJavaMethod error:", e);
        }

    }

    /**
     * @description:获得某栏目下的资源
     * @author: RXH
     * @date: 2025/8/7 17:43
     * @param: []
     * @return: void
     **/
    @Test
    public void testGetId(){
        try {
            List<Integer> idList = getNewsId(getResourceIds(getFolderIds(23)));
            System.out.println(idList.size());
        }catch (Exception e){
            System.err.println(e.getMessage());
        }

    }

    /**
     * 处理一个任务块
     */
    private List<StationMArticleData2> processTaskChunk(
            List<StationMArticleData2> chunk) {
        List<StationMArticleData2> chunkResult = new ArrayList<>();
        try {
            for (StationMArticleData2 originNew : chunk) {
                String newContent = modifyContent(originNew.getContent(),originNew);
                originNew.setContent(newContent);
                chunkResult.add(originNew);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }
        return chunkResult;
    }

    /**
     * @description:已废除
     * @author: RXH
     * @date: 2025/8/7 17:37
     * @param: [idList]
     * @return: void
     **/
    public void reloadNewsJavaMethodOld(List<Integer> idList) {
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<StationMArticleData2> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.in(StationMArticleData2::getDataId, idList);
        List<StationMArticleData2> originNews = stationMArticleDataMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 58;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<List<StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<StationMArticleData2>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    List<StationMArticleData2> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex));
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        List<StationMArticleData2> mergedMap = new ArrayList<>();
        for (List<StationMArticleData2> map : resultMaps) {
            mergedMap.addAll(map);
        }

        int affect = 0;
        // 处理合并后的结果
        for (StationMArticleData2 stationMArticleData2 : mergedMap) {
            LambdaUpdateWrapper<StationMArticleData2> wrapper1 = new LambdaUpdateWrapper<>();
            wrapper1.eq(StationMArticleData2::getDataId,stationMArticleData2.getDataId())
                    .set(StationMArticleData2::getThumb,stationMArticleData2.getThumb())
                    .set(StationMArticleData2::getContent,stationMArticleData2.getContent());
            int update = stationMArticleDataMapper.update(wrapper1);
            affect += update;
        }
        System.out.println("=======================处理结束，一共重新导入了"+affect+"条数据！====================");
    }





    public List<Integer>  getFolderIds(Integer catId){
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId)
                .select(StationMReference::getDataId);
        return stationMReferenceMapper.selectObjs(wrapper);
    }

    public List<Integer>  getResourceIds(List<Integer> folderIds){
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<FolderResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FolderResource::getId,folderIds)
                .select(FolderResource::getResourceId);
        return folderResourceMapper.selectObjs(wrapper);
    }


    public List<Integer>  getNewsId( List<Integer> ids){
        DynamicDataSource.changeResourceDynamicDataSource();
        String success = "***********:9000";
        String pattern = "(<img)* src=\"([^\"]*)\"";
        //找到所有新闻数据的catID
        LambdaQueryWrapper<StationMArticleData2> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.select(StationMArticleData2::getDataId)
                .apply("content regexp {0}",pattern)
                .notLike(StationMArticleData2::getContent,success)
                .in(StationMArticleData2::getDataId,ids);
        return stationMArticleDataMapper.selectObjs(wrapper1);
    }



    public String modifyContent(String content,StationMArticleData2 data){

        //找到<img src=的位置
//        Pattern pattern = Pattern.compile("(<img)* src=\"([^\"]*)\"");    //没有转义&quot;时
        Pattern pattern = Pattern.compile("(<img)* src=\"\\\\\"([^\"]*)\\\\\"\"");  //转义了&quot;时


        Matcher matcher = pattern.matcher(content);

        StringBuilder result = new StringBuilder();

        while (matcher.find()){
            //获取下载链接
            String matchStr = matcher.group(2);
            String oldUrl = matchStr;
            //已经上传成功的数据
            if (matchStr.contains("***********:9000"))
                continue;
            //TODO 具体情况具体处理
            if (matchStr.contains(";")&&matchStr.contains("\\")){
                oldUrl = matchStr.substring(matchStr.indexOf(";")+1,matchStr.lastIndexOf("\\"));
                System.out.println(oldUrl);
            }else if (matchStr.contains(";")&&matchStr.indexOf(";")!=matchStr.lastIndexOf(";")){
                oldUrl = matchStr.substring(matchStr.indexOf(";")+1,matchStr.lastIndexOf("&"));
                System.out.println(oldUrl);
            }else if (matchStr.contains("?")){
                oldUrl = matchStr.substring(0,matchStr.lastIndexOf("?"));
                System.out.println(oldUrl);
            }else {
                System.out.println("================================================");
                System.out.println(matchStr);
                System.out.println("================================================");
            }
            //完成下载上传
            String newUrl = CrawlerManager.DownLoad(oldUrl,downloadPath,refer);
            newUrl = CrawlerManager.changeFileUrl(new File(newUrl));
            if (newUrl.isEmpty()||newUrl.isBlank()){//上传失败，就用原来的地址链接
                matcher.appendReplacement(result, matcher.group());
                continue;
            }
            //获取第一张图片为头像（若没有头像）
            if (data.getThumb().isBlank()||data.getThumb().isEmpty()){
                data.setThumb(newUrl);
            }
            //新地址replace
            String group1 = matcher.group(1);
            if (group1==null)
                group1=" ";
            matcher.appendReplacement(result, group1+" src= \""+newUrl+"\"");
        }
        matcher.appendTail(result);
        return result.toString();
    }



    @Test
    public void testStr(){
        String url = "\"https://cmc.motherchildren.com/hx-hosp-media-integration/hhmi/ueditor/676e496eb760ae4658e7333b.jpg\"";
        if (url.contains("\"")){
            url = url.substring(url.indexOf("\"")+1,url.lastIndexOf("\""));
        }
        System.out.println(url);
    }

    @Test
    public void testSpecialChat(){
        String content = "&#305p style&#304&#302text-indent:2em&#300text-align:left&#300line-height:2em&#300margin-left:0px&#300&#302>&#305span style&#304&#302line-height:150%&#300&#302> 2025年7月，中华医学会杂志社CMJ肺癌学术会议&#303China Lung Cancer 2025）在河南郑州顺利召开。在7月18日进行的“肺癌领域中青年医师英文论文竞赛”中，成都市第五人民医院肿瘤科严沁医师从众多参赛选手中脱颖而出，荣获大赛一等奖。&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300line-height:2em&#300&#302>&#305span style&#304&#302line-height:150%&#300&#302>本次论文比赛面向全国45岁以下肺癌领域医务工作者公开征稿，经过层层严格筛选，共13篇投稿论文进入最终决赛阶段。&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300line-height:2em&#300&#302>&#305span style&#304&#302line-height:150%&#300&#302>决赛现场，所有参赛选手要求全英文汇报，且不能透露所在单位或学校信息，评委从论文科学性、创新性、实用性、现场表现、幻灯片制作、口头呈现等方面进行打分，评选出一等奖一个，二等奖两个，三等奖三个。&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300line-height:2em&#300&#302>&#305span style&#304&#302line-height:150%&#300&#302>经过激烈的角逐，肿瘤科严沁医师发挥出色，以深厚的理论功底、流利的口语表达以及精彩的演讲技巧，最终荣获一等奖。&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300line-height:2em&#300&#302>&#305span style&#304&#302line-height:150%&#300&#302>&#305/span>&#305/p>&#305p style&#304&#302text-align:center&#302>&#305img src&#304&#302https://cd5120.my120.org/oss/20250722/175238420.jpg&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20250722/175238420.jpg&#302 title&#304&#302图片2&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 />&#305/p>&#305p style&#304&#302text-align:center&#300&#302>&#305img src&#304&#302https://cd5120.my120.org/oss/20250722/175237657.jpg&#302 data_ue_src&#304&#302https://cd5120.my120.org/oss/20250722/175237657.jpg&#302 title&#304&#302图片1&#302 border&#304&#3020&#302 hspace&#304&#3020&#302 vspace&#304&#3020&#302 style&#304&#302text-align:center&#300text-wrap-mode:wrap&#300&#302 />&#305/p>&#305p style&#304&#302text-align:center&#302>&#305br />&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300line-height:2em&#300&#302>&#305span style&#304&#302line-height:150%&#300&#302>&#305/span>&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:150%&#300text-align:left&#300&#302>来源：肿瘤科&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:150%&#300text-align:left&#300&#302>作者：严沁、田浩&#305/p>&#305p style&#304&#302text-indent:2em&#300line-height:150%&#300text-align:left&#300&#302>审核：何朗&#305/p>&#305p style&#304&#302text-indent:2em&#300text-align:left&#300line-height:2em&#300&#302>&#305span style&#304&#302line-height:150%&#300&#302>&#305br />&#305/span>&#305br />&#305/p>&#305p>&#305br />&#305/p>";
        System.out.println(changeSpecialChat(content));
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }


}
