package com.ruifox.collect.dataMigration.utils;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station2.StationMArticleDataMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMReferenceMapper;
import com.ruifox.collect.dao.mapper.tbl_oldest.TblCNewsDataMapperOldest;
import com.ruifox.collect.dao.mapper.tbl_oldest.TblCNewsMapperOldest;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.StationMArticleData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.module.entity.tbl_oldest.TblCNewsOldest;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:找到没有导入的数据（适用于缺失少量数据时）
 * @author: RXH
 * @date: 2025/8/5 15:44
 * @param:
 * @return:
 **/
@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class findTheLostWithTypeId {
    @Autowired
    private TblCNewsMapperOldest tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapperOldest tblCNewsDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private FolderResourceMapper folderResourceMapper;


    @Test
    public void getReuslt(){
        Integer originId = 340;
        Integer targetId = 338;
        List<String> originTitle = findOriginTitle(originId);
        List<String> targetTitle = findTargetTitle(targetId);

        List<String> collect = originTitle.stream()
                .filter(element -> !targetTitle.contains(element))
                .collect(Collectors.toList());
        for (String s : collect) {
            System.out.println(s);
        }

    }

    public List<String> findOriginTitle(Integer originId){
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCNewsOldest> tblCNewsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tblCNewsLambdaQueryWrapper.eq(TblCNewsOldest::getCatid,originId)
                .eq(TblCNewsOldest::getStatus,99)
                .eq(TblCNewsOldest::getState,0)
                .select(TblCNewsOldest::getTitle);
        return tblCNewsMapper.selectObjs(tblCNewsLambdaQueryWrapper);
    }


    public List<String> findTargetTitle(Integer targetId){
        List<Integer> resourceIds = getResourceIds(getFolderIds(targetId));
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<StationMArticleData2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StationMArticleData2::getDataId,resourceIds)
                .select(StationMArticleData2::getTitle);
        return stationMArticleDataMapper.selectObjs(wrapper);
    }


    public List<Integer>  getFolderIds(Integer catId){
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId)
                .select(StationMReference::getDataId);
        return stationMReferenceMapper.selectObjs(wrapper);
    }

    public List<Integer>  getResourceIds(List<Integer> folderIds){
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<FolderResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FolderResource::getId,folderIds)
                .select(FolderResource::getResourceId);
        return folderResourceMapper.selectObjs(wrapper);
    }




}
