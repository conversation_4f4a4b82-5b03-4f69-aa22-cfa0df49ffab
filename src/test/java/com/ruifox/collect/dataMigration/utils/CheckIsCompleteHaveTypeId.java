package com.ruifox.collect.dataMigration.utils;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station2.StationMArticleDataMapper2;
import com.ruifox.collect.dao.mapper.station2.StationMReferenceMapper;
import com.ruifox.collect.dao.mapper.tbl_oldest.TblCNewsDataMapperOldest;
import com.ruifox.collect.dao.mapper.tbl_oldest.TblCNewsMapperOldest;
import com.ruifox.collect.module.entity.myexcel.NeedUpdateNews;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.StationMArticleData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.module.entity.tbl_oldest.TblCNewsOldest;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:检查导入的栏目数据是否完整（库里有100条，后台数据也要有一百条）
 * @author: RXH
 * @date: 2025/8/7 10:05
 * @param:
 * @return:
 **/
@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class CheckIsCompleteHaveTypeId {

    @Autowired
    private TblCNewsMapperOldest tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapperOldest tblCNewsDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private FolderResourceMapper folderResourceMapper;
    private static final int MAX = 10; //最大容忍误差,超过就重新导入

    private static final String excelName = "D:\\医院数据\\华西二院-excel\\lost.xlsx";

    @Test
    public void check(){
        HashMap<Integer,Integer> map = new HashMap<>();
        motherChildrenIds(map);
        HashMap<Integer,Integer> failure = new HashMap<>();
        List<NeedUpdateNews> news = new ArrayList<>();
        for (Integer i : map.keySet()) {
            int res = isComplete(i,map.get(i));
            if (res==0){
                continue;
            }else if (res <= MAX){
                String originAndTarget = "map.put("+i+" , "+map.get(i)+");";
                List<Integer> result = getResult(i, map.get(i));
                //写入excel表
                news.add(new NeedUpdateNews(originAndTarget,result.toString()));
            }else {
                failure.put(i,map.get(i));
            }
        }
        EasyExcel.write(excelName, NeedUpdateNews.class)
                        .sheet("需要新增的新闻信息表")
                                .doWrite(news);
        System.out.println("===================================需要重新导入的数据：==================================");
        for (Integer i : failure.keySet()) {
            System.out.println("map.put("+i+" , "+failure.get(i)+");");
        }

    }

    public int isComplete(Integer originId,Integer targetId){
        DynamicDataSource.changeOldPhpDynamicDataSource();
        // find the origin database datalist size
        List<Integer> originIds = getOriginIds(originId);
        //find the target database datalist size
        List<Integer> resourceIds = getResourceIds(getFolderIds(targetId));
        return originIds.size()- resourceIds.size();
    }




    /**
     * @description:获取没有导入到目标数据库的数据
     * @author: RXH
     * @date: 2025/8/7 10:22
     * @param: []
     * @return: void
     **/
    public List<Integer> getResult(Integer originId,Integer targetId){
        List<String> originTitle = findOriginTitle(originId);
        List<String> targetTitle = findTargetTitle(targetId);

        List<String> collect = originTitle.stream()
                .filter(element -> !targetTitle.contains(element))
                .collect(Collectors.toList());

        DynamicDataSource.changeOldPhpDynamicDataSource();
        List<Integer> res = new ArrayList<>();
        for (String s : collect) {
            LambdaQueryWrapper<TblCNewsOldest> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TblCNewsOldest::getTitle,s)
                    .eq(TblCNewsOldest::getCatid,originId)
                    .eq(TblCNewsOldest::getState,0)
                    .eq(TblCNewsOldest::getStatus,99)
                    .select(TblCNewsOldest::getId);
            List<Integer> list = tblCNewsMapper.selectObjs(wrapper);
            if (list.size()!=1){
                continue;
            }
            res.add(list.get(0));
        }
        return res;
    }

    /**
     * @description:本地数据库的数据title
     * @author: RXH
     * @date: 2025/8/7 10:22
     * @param: [originId]
     * @return: java.util.List<java.lang.String>
     **/
    public List<String> findOriginTitle(Integer originId){
        DynamicDataSource.changeOldPhpDynamicDataSource();
        LambdaQueryWrapper<TblCNewsOldest> tblCNewsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tblCNewsLambdaQueryWrapper.eq(TblCNewsOldest::getCatid,originId)
                .eq(TblCNewsOldest::getStatus,99)
                .eq(TblCNewsOldest::getState,0)
                .select(TblCNewsOldest::getTitle);
        return tblCNewsMapper.selectObjs(tblCNewsLambdaQueryWrapper);
    }

    /**
     * @description:目标数据库的数据title
     * @author: RXH
     * @date: 2025/8/7 10:22
     * @param: [targetId]
     * @return: java.util.List<java.lang.String>
     **/


    public List<String> findTargetTitle(Integer targetId){
        List<Integer> resourceIds = getResourceIds(getFolderIds(targetId));
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<StationMArticleData2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StationMArticleData2::getDataId,resourceIds)
                .select(StationMArticleData2::getTitle);
        return stationMArticleDataMapper.selectObjs(wrapper);
    }
    /**
     * @description:本地数据库的数据
     * @author: RXH
     * @date: 2025/8/7 10:21
     * @param: [originId]
     * @return: java.util.List<java.lang.Integer>
     **/

    public List<Integer> getOriginIds(Integer originId){
        LambdaQueryWrapper<TblCNewsOldest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TblCNewsOldest::getState,0)
                .eq(TblCNewsOldest::getStatus,99)
                .eq(TblCNewsOldest::getCatid,originId)
                .select(TblCNewsOldest::getId);
        return tblCNewsMapper.selectObjs(wrapper);
    }

    /**
     * @description:目标数据库的数据
     * @author: RXH
     * @date: 2025/8/7 10:22
     * @param: [catId]
     * @return: java.util.List<java.lang.Integer>
     **/
    public List<Integer>  getFolderIds(Integer catId){
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId)
                .select(StationMReference::getDataId);
        return stationMReferenceMapper.selectObjs(wrapper);
    }

    public List<Integer>  getResourceIds(List<Integer> folderIds){
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<FolderResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FolderResource::getId,folderIds)
                .select(FolderResource::getResourceId);
        return folderResourceMapper.selectObjs(wrapper);
    }


    /**
     * @description:华西二院栏目ID
     * @author: RXH
     * @date: 2025/8/7 11:46
     * @param: [map]
     * @return: void
     **/


    public void motherChildrenIds(HashMap<Integer,Integer> map){
        map.put(2150,30);   // 专业设备物资及服务采购前市场调研 * 0
        map.put(2151,31);   // 通用设备物资及服务采购前市场调研 * 0
        map.put(2544,32);   // 信息类物资及服务采购市场调研
        map.put(2152,33);   // 招标信息  * 0
        map.put(1129,35);   // 招生信息
        map.put(1130,36);   // 通知信息
        map.put(12,37);   // 学术信息
        //栏目3    视频中心
        map.put(2091,44);   // 先进典型
        //栏目4    就医指南
        map.put(2308,11);   // 就诊重要通知
        map.put(1829,14);   // 便民门诊
        //栏目6    医联体
        map.put(1488,92);   //最新动态
        //栏目5    科室导航-院本部-党群部门-党委办公室
        map.put(313,58);   // 部门动态
        map.put(314,59);   // 规章制度
        map.put(316,60);   // 通知通告
        map.put(318,62);   // 重要文件
        map.put(1965,63);   // 学习安排

        map.put(153,114);   // 星耀二院
        map.put(154,115);   // 人文二院
        map.put(156,116);   // 医暖二院

//        栏目7 特色医疗
        map.put(1484,118);   // 特色医疗-儿科
        map.put(1485,119);   // 特色医疗-妇产科
        //栏目8 信息公开
        map.put(36,123);   // 信息公开目录
        map.put(37,124);   // 信息公开实施办法
        map.put(40,126);   // 信息公开制度文件

        map.put(2746,67);   // 根栏目-健康科普
        map.put(2729,191);   // 主动公开-妇幼健康产业
        map.put(2582,235);   // 规范化培训-政策法规
        map.put(2371,301);   // 科学研究-通知通告

        map.put(2307,179);   // 财务资产-部门预算
        map.put(2041,112);   // 党建工作-青春风采
        map.put(2038,109);   // 党的建设-学习通知

        map.put(1109,276);   // 项目申请-国家自然科学基金
        map.put(1110,277);   // 项目申请-国家科技部
        map.put(1111,278);   // 项目申请-国家教育部
        map.put(1112,279);   // 项目申请-国家其他部委
        map.put(1114,281);   // 项目申请-省科技厅
        map.put(1116,282);   // 项目申请-其他项目


        map.put(46,137);   // 发展规划-医联体
        map.put(47,190);   // 主动公开-年度工作
        map.put(49,139);   // 主动公开-公益捐赠
        map.put(73,211);   // 护理天地-护理动态
        map.put(74,212);   // 护理天地-护理教学
        map.put(75,213);   // 护理天地-护理科研
        map.put(76,214);   // 护理天地-专科护理
        map.put(79,216);   // 护理天地-人物专访
        map.put(81,218);   // 护理天地-继续教育

        map.put(90,236);   // 规范化培训-招生简章
        map.put(91,237);   // 规范化培训-面试通知
        map.put(92,238);   // 规范化培训-录取名单
        map.put(93,239);   // 规范化培训-报到通知
        map.put(94,232);   // 医学教育-部门动态
        map.put(95,241);   // 医学教育-本科教学
        map.put(96,242);   // 医学教育-研究生培养

        map.put(98,244);   // 继续医学教育-继教项目与培训
        map.put(99,245);   // 继续医学教育-远程医学教育
        map.put(100,246);   // 继续医学教育-护理继续教育
        map.put(101,247);   // 继续医学教育-各类短期培训
        map.put(102,229);   // 医学教育概况-基地项目
        map.put(119,266);   // 科学研究-政策法规
        map.put(123,284);   // 成果专利-成果
        map.put(124,285);   // 成果专利-专利
        map.put(129,288);   // 期刊及学科代码-期刊信息
        map.put(130,289);   // 期刊及学科代码-学科代码
        map.put(131,290);   // 科学研究-办事指南
        map.put(143,106);   // 党建工作-新闻动态
        map.put(150,108);   // 党的建设-支部风采


        map.put(331,324);   // 党委宣传统战部（精神文明办公室）-部门动态
        map.put(335,327);   // 党委宣传统战部（精神文明办公室）-办事流程
        map.put(1205,326);   // 党委宣传统战部（精神文明办公室）-通知通告
        map.put(1960,328);   // 党委宣传统战部（精神文明办公室）-统战工作
        map.put(1206,339);   // 纪委办公室-通知通告
        map.put(340,338);   // 纪委办公室-部门动态
        map.put(969,340);   // 纪委办公室-办事流程
        map.put(1179,177);   // 财务资产-财务政策
        map.put(2280,330);   // 专题学习-学习内容

//        院长办公室
        map.put(248,343);   // 院长办公室（应急办公室）-办事流程
        map.put(250,344);   // 院长办公室（应急办公室）-部门动态
        map.put(1192,345);   // 院长办公室（应急办公室）-通知通告
        map.put(39,193);   // 信息公开-资料下载
    }
}
