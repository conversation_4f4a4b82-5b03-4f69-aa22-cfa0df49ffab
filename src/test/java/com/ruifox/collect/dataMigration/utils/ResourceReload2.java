package com.ruifox.collect.dataMigration.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station.StationMArticleMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorMapper;
import com.ruifox.collect.dao.mapper.station.StationMImageMapper;
import com.ruifox.collect.dao.mapper.station.StationMTeacherMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.dao.mapper.tbl.TblCManDataMxfyMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureDataMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCategoryMapper;
import com.ruifox.collect.dao.mapper.tbl_oldest.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.StationMArticleData2;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description:重新下载因为网络波动未上传成功的文件(适合小批量未成功)《href/src没有地址，从data_src里面取》
 * @author: RXH
 * @date: 2025/7/28 15:28
 * @param:
 * @return:
 **/

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class ResourceReload2 {
    @Autowired
    private TblCNewsMapperOldest tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapperOldest tblCNewsDataMapper;
    @Autowired
    private TblCManDataMxfyMapper tblCManDataMxfyMapper;
    @Autowired
    private StationMArticleMapper stationMArticleMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    private StationMDoctorDataMapper2 stationMDoctorDataMapper;
    @Autowired
    private StationMTeacherMapper stationMTeacherMapper;
    @Autowired
    private StationMTeacherDataMapper2 stationMTeacherDataMapper;
    @Autowired
    private StationCategoryMapper2 stationCategoryMapper;
    @Autowired
    private TblCategoryMapper tblCategoryMapper;
    @Autowired
    private TblCPictureMapper tblCPictureMapper;
    @Autowired
    private TblCPictureDataMapper tblCPictureDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Autowired
    private StationMImageDataMapper2 stationMImageDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private TblCVideoMapperOldest tblCVideoMapper;
    @Autowired
    private TblCVideoDataMapperOldest tblCVideoDataMapper;
    @Autowired
    private StationMVideoDataMapper2 stationMVideoDataMapper;
    @Autowired
    private FolderResourceMapper folderResourceMapper;



    @Value("${testDownload.config.refer}")
    private String refer;

    @Value("${testDownload.config.fileName}")
    private String downloadPath;

    @Test
    public void reloadNewsJava() {
        List<StationMArticleData2> allNewsIdWithoutSrc = getAllNewsIdWithoutSrc();
//        List<StationMArticleData2> allNewsIdWithoutHref = getAllNewsIdWithoutHref();
        try {
            reloadNewsJavaMethodWithoutSrc(allNewsIdWithoutSrc);
//            reloadNewsJavaMethodWithoutHref(allNewsIdWithoutHref);
        } catch (Exception e) {
            log.error("reloadNewsJavaMethod error:", e);
        }

    }


    public void reloadNewsJavaMethodWithoutSrc(List<StationMArticleData2> idList) throws InterruptedException {
        DynamicDataSource.changeResourceDynamicDataSource();
        // 找到所有存在的信息
        if (idList.isEmpty()) {
            return;
        }
        // 定义线程数量
        int threadCount = 12;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<List<StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));
        // 分割任务
        List<List<StationMArticleData2>> taskChunks = divideTasks(idList, threadCount);
        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    List<StationMArticleData2> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex));
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }
        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }
        // 合并所有线程的结果
        List<StationMArticleData2> mergedMap = new ArrayList<>();
        for (List<StationMArticleData2> map : resultMaps) {
            mergedMap.addAll(map);
        }
        int affect = 0;
        // 处理合并后的结果
        for (StationMArticleData2 stationMArticleData2 : mergedMap) {
            LambdaUpdateWrapper<StationMArticleData2> wrapper1 = new LambdaUpdateWrapper<>();
            wrapper1.eq(StationMArticleData2::getDataId,stationMArticleData2.getDataId())
                    .set(StationMArticleData2::getThumb,stationMArticleData2.getThumb())
                    .set(StationMArticleData2::getContent,stationMArticleData2.getContent());
            int update = stationMArticleDataMapper.update(wrapper1);
            affect += update;
        }
        System.out.println("=======================处理结束，一共重新导入了"+affect+"条数据！====================");
    }


    public void reloadNewsJavaMethodWithoutHref(List<StationMArticleData2> idList) throws InterruptedException {
        DynamicDataSource.changeResourceDynamicDataSource();
        // 找到所有存在的信息
        if (idList.isEmpty()) {
            return;
        }
        // 定义线程数量
        int threadCount = 12;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<List<StationMArticleData2>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));
        // 分割任务
        List<List<StationMArticleData2>> taskChunks = divideTasks(idList, threadCount);
        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    List<StationMArticleData2> chunkResult =
                            processTaskChunk2(taskChunks.get(chunkIndex));
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }
        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }
        // 合并所有线程的结果
        List<StationMArticleData2> mergedMap = new ArrayList<>();
        for (List<StationMArticleData2> map : resultMaps) {
            mergedMap.addAll(map);
        }
        int affect = 0;
        // 处理合并后的结果
        for (StationMArticleData2 stationMArticleData2 : mergedMap) {
            LambdaUpdateWrapper<StationMArticleData2> wrapper1 = new LambdaUpdateWrapper<>();
            wrapper1.eq(StationMArticleData2::getDataId,stationMArticleData2.getDataId())
                    .set(StationMArticleData2::getContent,stationMArticleData2.getContent());
            int update = stationMArticleDataMapper.update(wrapper1);
            affect += update;
        }
        System.out.println("=======================处理结束，一共重新导入了"+affect+"条数据！====================");
    }
    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            if (start>=end){
                start=end;
            }
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块（without src）
     */
    private List<StationMArticleData2> processTaskChunk(
            List<StationMArticleData2> chunk) {
        List<StationMArticleData2> chunkResult = new ArrayList<>();
        try {
            for (StationMArticleData2 originNew : chunk) {
                String newContent = modifyContentWithoutSrc(originNew.getContent(),originNew);
                originNew.setContent(newContent);
                chunkResult.add(originNew);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }
        return chunkResult;
    }

    /**
     * 处理一个任务块（without href）
     */
    private List<StationMArticleData2> processTaskChunk2(
            List<StationMArticleData2> chunk) {
        List<StationMArticleData2> chunkResult = new ArrayList<>();
        try {
            for (StationMArticleData2 originNew : chunk) {
                String newContent = modifyContentWithoutHref(originNew.getContent());
                originNew.setContent(newContent);
                chunkResult.add(originNew);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }
        return chunkResult;
    }


    /**
     * @description:找到src不存在的
     * @author: RXH
     * @date: 2025/8/1 18:04
     * @param: []
     * @return: java.util.List<com.ruifox.collect.module.entity.station2.StationMArticleData2>
     **/
    public List<StationMArticleData2> getAllNewsIdWithoutSrc(){
        //找到所有folder_id
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StationMReference::getDataId);
        List<Integer> folder_ids = stationMReferenceMapper.selectObjs(wrapper);

        //根据folder_id找到resource_id
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<FolderResource> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.in(FolderResource::getId,folder_ids)
                .select(FolderResource::getResourceId);
        List<Integer> resource_id = folderResourceMapper.selectObjs(wrapper1);

        //根据resource_id找到对应的resource
        String srcPattern = "src=\"\"";
        LambdaQueryWrapper<StationMArticleData2> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.in(StationMArticleData2::getDataId,resource_id)
                .like(StationMArticleData2::getContent,srcPattern);
        return stationMArticleDataMapper.selectList(wrapper2);
    }
    /**
     * @description:找到href不存在的
     * @author: RXH
     * @date: 2025/8/1 18:05
     * @param: []
     * @return: java.util.List<com.ruifox.collect.module.entity.station2.StationMArticleData2>
     **/
    public List<StationMArticleData2> getAllNewsIdWithoutHref(){
        //找到所有folder_id
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StationMReference::getDataId);
        List<Integer> folder_ids = stationMReferenceMapper.selectObjs(wrapper);
        //根据folder_id找到resource_id
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<FolderResource> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.in(FolderResource::getId,folder_ids)
                .select(FolderResource::getResourceId);
        List<Integer> resource_id = folderResourceMapper.selectObjs(wrapper1);

        //根据resource_id找到对应的resource
        String srcPattern = "href=\"\"";
        LambdaQueryWrapper<StationMArticleData2> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.in(StationMArticleData2::getDataId,resource_id)
                .like(StationMArticleData2::getContent,srcPattern);
        return stationMArticleDataMapper.selectList(wrapper2);
    }


    @Test
    public void test(){
        List<StationMArticleData2> allSrcId = getAllNewsIdWithoutSrc();
        System.out.println(allSrcId.size());
        for (StationMArticleData2 stationMArticleData2 : allSrcId) {
            System.out.println(stationMArticleData2);
        }

        List<StationMArticleData2> allHrefId = getAllNewsIdWithoutHref();
        System.out.println(allHrefId.size());
        for (StationMArticleData2 stationMArticleData2 : allHrefId) {
            System.out.println(stationMArticleData2);
        }


    }

    @Test
    public void testModifyWithoutHref(){
        String content = "<html>\n" +
                " <head></head>\n" +
                " <body>\n" +
                "  <p style=\"text-indent:2em;text-align:left;\">市五医院举行医疗争议防范与处置知识培训及考试</p>\n" +
                "  <p style=\"text-align:center\"><img src=\"\" style=\"width:400px;height:300px;\" title=\"图片2.jpg\" alt=\"图片2.jpg\" data_ue_src=\"https://cd5120.my120.org/upload/2019/0116/20190116042404678.jpg\" width=\"400\" vspace=\"0\" height=\"300\" border=\"0\"></p>\n" +
                "  <p style=\"text-align:center\"><img src=\"https://cd5120.my120.org/upload/2019/0116/20190116042505351.jpg\" style=\"width:400px;height:300px;\" title=\"图片4.jpg\" alt=\"图片4.jpg\" data_ue_src=\"https://cd5120.my120.org/upload/2019/0116/20190116042505351.jpg\" width=\"400\" vspace=\"0\" height=\"300\" border=\"0\"></p>\n" +
                "  <p style=\"text-align:center\"><img src=\"\" style=\"width:400px;height:300px;\" title=\"图片3.jpg\" alt=\"图片3.jpg\" data_ue_src=\"https://cd5120.my120.org/upload/2019/0116/20190116042505970.jpg\" width=\"400\" vspace=\"0\" height=\"300\" border=\"0\"></p>\n" +
                "  <p style=\"text-indent:2em;text-align:left;\">为进一步提高医务人员医疗纠纷防范与处理的意识，市五医院在今年多次多形式对全院医务人员进行“医疗争议防范与处置”相关内容培训的基础上，于2016年9月20日-22日组织全院1000余名医务人员分批次进行医疗纠纷防范与处理基本知识考试。内容包括：医疗纠纷发生原因、侵权责任法、医生应向患者告知的内容、如何防范医疗纠纷等。</p>\n" +
                "  <p style=\"text-indent:2em;text-align:left;\">本次考试为严格的闭卷考试，考题从题库中随机抽取7套，每场考试试卷全部不同，整个考试公证、严明，被医务人员称为“市五医院史上最严考试”及“市五医院的高考”。通过此次考试，使全院干部职工进一步掌握了医疗纠纷防范与处理的基本知识和应对技巧，有效预防和避免了医疗纠纷，提高了医疗纠纷防范和医患关系的处理能力。</p>\n" +
                " </body>\n" +
                "</html>";
        StationMArticleData2 data2 = new StationMArticleData2();
        data2.setThumb("");
        System.out.println(modifyContentWithoutSrc(modifyContentWithoutHref(content),data2));

    }



    /**
     * @description:处理没有Src的content
     * @author: RXH
     * @date: 2025/8/4 9:48
     * @param:
     * @return:
     **/
    public String modifyContentWithoutSrc(String content,StationMArticleData2 data){

        //找到<img src=的位置 TODO还可以用Jsoup（HTML解析库来操作）
        Pattern pattern = Pattern.compile("(<img\\s+[^>]*?src=[\"'])((?:(?!\\1).)*?)([\"'][^>]*?data_ue_src=[\"'][^>]*?>)");


        Matcher matcher = pattern.matcher(content);

        StringBuilder result = new StringBuilder();

        while (matcher.find()){
            //获取下载链接
            String headStr = matcher.group(1);
            String replaceStr = matcher.group(2);
            String tailStr = matcher.group(3);

            //已经上传成功的数据
            if (!replaceStr.isEmpty()||tailStr.isEmpty())
                continue;
            //TODO 具体情况具体处理
            System.out.println("tailStr = "+tailStr);
            int begin = tailStr.indexOf("\"",tailStr.indexOf("data_ue_src=\""))+1;
            int end = tailStr.indexOf("\"",begin);
            String originUrl = tailStr.substring(begin,end);
            System.out.println("originUrl = "+originUrl);
            //完成下载上传
            String newUrl = CrawlerManager.DownLoad(originUrl,downloadPath,refer);
            newUrl = CrawlerManager.changeFileUrl(new File(newUrl));
            if (newUrl.isEmpty()||newUrl.isBlank()){//上传失败，就用原来的地址链接
                matcher.appendReplacement(result, headStr+replaceStr+tailStr);
                continue;
            }
            //获取第一张图片为头像（若没有头像）
            if (data.getThumb().isBlank()||data.getThumb().isEmpty()){
                data.setThumb(newUrl);
            }

            matcher.appendReplacement(result, headStr+newUrl+tailStr);
        }
        matcher.appendTail(result);
        return result.toString();
    }


    /**
     * @description:修改没有Href的content
     * @author: RXH
     * @date: 2025/8/4 9:12
     * @param: []
     * @return: void
     **/

    public String modifyContentWithoutHref(String content){

        //找到<img src=的位置
        Pattern pattern = Pattern.compile("(<a\\s+[^>]*?href=[\"'])((?:(?!\\1).)*?)([\"'][^>]*?data_ue_src=[\"'][^>]*?>)");

        Matcher matcher = pattern.matcher(content);

        StringBuilder result = new StringBuilder();

        while (matcher.find()){
            //获取下载链接
            String tailStr = matcher.group(3);
            String replaceStr = matcher.group(2);
            String headStr = matcher.group(1);
            //已经上传成功的数据
            if (!replaceStr.isEmpty()||tailStr.isEmpty())
                continue;

            int begin = tailStr.indexOf("\"",tailStr.indexOf("data_ue_src=\""))+1;
            int end = tailStr.indexOf("\"",begin);
            String originUrl = tailStr.substring(begin,end);
            if (originUrl.contains("@") || originUrl.contains("javascript") || originUrl.contains(".html") || originUrl.contains(".htm") || originUrl.contains(".jsp") || originUrl.contains(".aspx")) {
                continue;
            }
            //完成下载上传
            String newUrl = CrawlerManager.DownLoad(originUrl,downloadPath,refer);
            newUrl = CrawlerManager.changeFileUrl(new File(newUrl));
            if (newUrl.isEmpty()||newUrl.isBlank()){//上传失败，就用原来的地址链接
                matcher.appendReplacement(result, headStr+replaceStr+tailStr);
                continue;
            }
            matcher.appendReplacement(result, headStr+newUrl+tailStr);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }

}
