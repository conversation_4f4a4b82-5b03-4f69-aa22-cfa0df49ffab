package com.ruifox.collect.dataMigration.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station.StationMArticleMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorMapper;
import com.ruifox.collect.dao.mapper.station.StationMImageMapper;
import com.ruifox.collect.dao.mapper.station.StationMTeacherMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.dao.mapper.tbl.TblCManDataMxfyMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureDataMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCategoryMapper;
import com.ruifox.collect.dao.mapper.tbl_oldest.*;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

/**
 * @description:删除导入成功的数据及其引用（适用于重新导入）
 * @author: RXH
 * @date: 2025/7/28 15:28
 * @param:
 * @return:
 **/

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class RemoveResourceAndReference {
    @Autowired
    private TblCNewsMapperOldest tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapperOldest tblCNewsDataMapper;
    @Autowired
    private TblCManDataMxfyMapper tblCManDataMxfyMapper;
    @Autowired
    private StationMArticleMapper stationMArticleMapper;
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMDoctorMapper stationMDoctorMapper;
    @Autowired
    private StationMDoctorDataMapper2 stationMDoctorDataMapper;
    @Autowired
    private StationMTeacherMapper stationMTeacherMapper;
    @Autowired
    private StationMTeacherDataMapper2 stationMTeacherDataMapper;
    @Autowired
    private StationCategoryMapper2 stationCategoryMapper;
    @Autowired
    private TblCategoryMapper tblCategoryMapper;
    @Autowired
    private TblCPictureMapper tblCPictureMapper;
    @Autowired
    private TblCPictureDataMapper tblCPictureDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Autowired
    private StationMImageDataMapper2 stationMImageDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private TblCVideoMapperOldest tblCVideoMapper;
    @Autowired
    private TblCVideoDataMapperOldest tblCVideoDataMapper;
    @Autowired
    private StationMVideoDataMapper2 stationMVideoDataMapper;
    @Autowired
    private FolderResourceMapper folderResourceMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;


    @Value("${testDownload.config.refer}")
    private String refer;

    @Value("${testDownload.config.fileName}")
    private String downloadPath;


    @Test
    public void removeNewsJava() {
        Integer catId = 44;
        //获取folder_resource的id列表
        List<Integer> folderIds = getFolderIds(catId);
        //获得resource_data_article的id列表
        List<Integer> resourceIds = getResourceIds(folderIds);

        //开始删除
        //删除resource库中的两张表，开启事务
        //编程式事务
        transactionTemplate.execute(status -> {
            DynamicDataSource.changeResourceDynamicDataSource();
            //删除resource_data_article
            stationMArticleDataMapper.deleteByIds(resourceIds);
            //删除folder_resource
            folderResourceMapper.deleteByIds(folderIds);
            return true;
        });

        //删除station_m_reference
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaUpdateWrapper<StationMReference> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId);
        stationMReferenceMapper.delete(wrapper);

    }

    @Test
    public void testGetId(){
        Integer catId = 9999999;
        //获取folder_resource的id列表
        List<Integer> folderIds = getFolderIds(catId);
        //获得resource_data_article的id列表
        List<Integer> resourceIds = getResourceIds(folderIds);
        //获取reference的id列表
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId)
                .select(StationMReference::getId);
        List<Integer> references = stationMReferenceMapper.selectObjs(wrapper);

        System.out.println(folderIds.size());
        System.out.println(resourceIds.size());
        System.out.println(references.size());
    }


    public List<Integer>  getFolderIds(Integer catId){
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId)
                .select(StationMReference::getDataId);
        return stationMReferenceMapper.selectObjs(wrapper);
    }

    public List<Integer>  getResourceIds(List<Integer> folderIds){
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<FolderResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FolderResource::getId,folderIds)
                .select(FolderResource::getResourceId);
        return folderResourceMapper.selectObjs(wrapper);
    }



}
