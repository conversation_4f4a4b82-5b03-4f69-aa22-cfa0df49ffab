package com.ruifox.collect.dataMigration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCNewsMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.dao.mapper.tbl.*;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.hms.HmsCNews;
import com.ruifox.collect.module.entity.hms.HmsCNewsData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.module.entity.tbl.*;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class OldPhpProjectToNewPhpProjectTest {
    @Autowired
    private TblCNewsMapper tblCNewsMapper;
    @Autowired
    private TblCNewsDataMapper tblCNewsDataMapper;
    @Autowired
    private HmsCNewsMapper hmsCNewsMapper;
    @Autowired
    private HmsCNewsDataMapper hmsCNewsDataMapper;
    @Autowired
    private HmsCategoryMapper hmsCategoryMapper;

    //下载时的来源页面
    private static String refer = "https://www.cd5120.cn/";
    //下载时的refer
    private static String downloadPath = "C:\\Collect_Data\\cd5120";

    //切换为php使用的地址
    private static String replacePath = "http://cd5120.web.foxtest.net/oss/";

    //原本被切换的地址
    private static String originPath = "C:/Collect_Data/cd5120/";

    //判断是否为内链的host
    private static String localhost = "cd5120";


    @Test
    public void moveNews() {
        Map<Integer, Integer> map = new HashMap<>();
        map.put(57, 137);
        Integer id = 0;
        for (Integer originCatId : map.keySet()) {
            try {
                moveNewsMethod(originCatId, map.get(originCatId));
            } catch (Exception e) {
                log.error("moveNewsJavaMethod error:" + originCatId, e);
                id = originCatId;
                break;
            }
        }
        if (id != 0) {
            log.info("运行到id为: " + id + " 报错");
        }
    }

    public void moveNewsMethod(Integer originCatId, Integer targetCatId) {
        DynamicDataSource.changeDefaultDataSource();
        LambdaQueryWrapper<TblCNews> wrapper = new LambdaQueryWrapper<>();
        // 找到所有存在的信息
        wrapper.eq(TblCNews::getCatid, originCatId)
                .eq(TblCNews::getState, 0)
                .eq(TblCNews::getStatus, 99)
                .gt(TblCNews::getInputtime, 1723824000)
                .orderByAsc(TblCNews::getListorder);
        List<TblCNews> originNews = tblCNewsMapper.selectList(wrapper);
        if (originNews.isEmpty()) {
            return;
        }

        // 定义线程数量
        int threadCount = 1;
        // 创建CountDownLatch用于线程同步
        CountDownLatch latch = new CountDownLatch(threadCount);
        // 创建线程安全的结果集合
        List<Map<HmsCNews, HmsCNewsData>> resultMaps =
                Collections.synchronizedList(new ArrayList<>(Collections.nCopies(threadCount, null)));

        // 分割任务
        List<List<TblCNews>> taskChunks = divideTasks(originNews, threadCount);

        // 提交任务到线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        for (int i = 0; i < threadCount; i++) {
            final int chunkIndex = i;
            executor.submit(() -> {
                try {
                    // 处理分配的任务块
                    Map<HmsCNews, HmsCNewsData> chunkResult =
                            processTaskChunk(taskChunks.get(chunkIndex), targetCatId);
                    // 将结果按任务块索引放入对应位置
                    synchronized (resultMaps) {
                        resultMaps.set(chunkIndex, chunkResult);
                    }
                } finally {
                    // 任务完成，计数减一
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for tasks", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
        }

        // 合并所有线程的结果
        Map<HmsCNews, HmsCNewsData> mergedMap = new LinkedHashMap<>();
        for (Map<HmsCNews, HmsCNewsData> map : resultMaps) {
            mergedMap.putAll(map);
        }

        // 切换到总院的数据源
        DynamicDataSource.changeDynamicDataSource();
        // 处理合并后的结果
        for (HmsCNews targetNew : mergedMap.keySet()) {
            HmsCategory hmsCategory = hmsCategoryMapper.selectById(targetNew.getCatid());
            HmsCNewsData targetNewData = mergedMap.get(targetNew);
            // 先加入资源数据
            hmsCNewsMapper.insert(targetNew);
            // 引用数据引用资源数据id
            targetNew.setUrl(hmsCategory.getUrl() + "/" + String.format("%02d", hmsCategory.getModelId()) + String.format("%05d", hmsCategory.getId()) + String.format("%08d", targetNew.getId()) + ".html");
            targetNew.setListorder(targetNew.getId());
            targetNew.setDataid(targetNew.getId());
            // 加入引用数据
            hmsCNewsMapper.updateById(targetNew);
            // 根据id更新排序
            targetNewData.setDid(targetNew.getId());
            hmsCNewsDataMapper.insert(targetNewData);
        }
    }

    /**
     * 将任务列表分割成多个子列表
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int threadCount) {
        List<List<T>> result = new ArrayList<>();
        int taskCount = tasks.size();
        int chunkSize = (taskCount + threadCount - 1) / threadCount; // 向上取整

        for (int i = 0; i < threadCount; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, taskCount);
            result.add(tasks.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个任务块
     */
    private Map<HmsCNews, HmsCNewsData> processTaskChunk(
            List<TblCNews> chunk, Integer targetCatId) {
        Map<HmsCNews, HmsCNewsData> chunkResult = new LinkedHashMap<>();
        try {
            for (TblCNews originNew : chunk) {
                // 新的article
                HmsCNews hmsCNews = new HmsCNews();
                hmsCNews.setCatid(targetCatId);
                hmsCNews.setUsername(1);
                hmsCNews.setPublishTime(originNew.getInputtime());
                hmsCNews.setInputTime(originNew.getInputtime());
                hmsCNews.setUpdateTime(originNew.getInputtime());
                hmsCNews.setExpireTime(0);
                hmsCNews.setState(0);
//                hmsCNews.setStatus(99);
                hmsCNews.setTop(0L);
                hmsCNews.setListorder(-99L);
                hmsCNews.setSort(1);
                hmsCNews.setOldCatid(null);
                hmsCNews.setEndOperator(1);
                hmsCNews.setIsLocked(false);

                String linkUrl = originNew.getUrl();
                int isLink = 1;
                //根据url判断是否为外链
                if (linkUrl.contains(localhost)) {
                    isLink = 0;
                }

                // 找到修改前的newsData并修改后存入新的newsData
                LambdaQueryWrapper<TblCNewsData> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(TblCNewsData::getId, originNew.getDataid());
                TblCNewsData originNewsData = tblCNewsDataMapper.selectOne(wrapper1);

                // 新的articleData
                HmsCNewsData hmsCNewsData = new HmsCNewsData();
                hmsCNewsData.setTitle(originNewsData.getTitle());

                // 缩略图处理 （只有一张图片）
                String thumb = originNewsData.getThumb();
                // 调用接口
                if (!thumb.isBlank()) {
                    thumb = CrawlerManager.DownLoad(thumb, downloadPath, refer);
                    thumb = thumb.replace(originPath, replacePath);
                }
                hmsCNewsData.setThumb(thumb);

                // 处理正文,摘要
                Integer contentNum = 0;
                String description = "";
                String content = originNewsData.getContent();
                if (isLink == 0) {
                    content = changeSpecialChat(content);
                    Document document = Jsoup.parse(content);
                    Elements imgList = document.select("img");
                    Elements linkList = document.select("a");
                    Elements videoList = document.select("video");

                    String text = document.text();
                    contentNum = text.length();
                    text = "   " + StringUtils.trim(text.substring(0, Math.min(120, text.length()))) + "...";
                    description = text;


                    // 图片路径集合处理
                    try {
                        List<String> imgUrls = new ArrayList<>();
                        for (Element element : imgList) {
                            String imgUrl = element.attr("src");
                            if (StringUtils.isNotBlank(imgUrl)) {
                                imgUrl = CrawlerManager.DownLoad(imgUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(imgUrl)) {
                                    imgUrl = imgUrl.replace(originPath, replacePath);
                                    element.attr("src", imgUrl);
                                    imgUrls.add(imgUrl);
                                }
                            }
                        }
                        hmsCNewsData.setContentImage(JsonUtil.obj2String(imgUrls));
                    } catch (Exception e) {
                        log.error("图片下载失败,是资源id为:" + originNewsData.getId());
                        hmsCNewsData.setContentImage("[]");
                    }

                    // 链接路径集合处理
                    try {
                        List<String> attachUrls = new ArrayList<>();
                        for (Element element : linkList) {
                            String attachUrl = element.attr("href");
                            if (StringUtils.isNotBlank(attachUrl)) {
                                if (attachUrl.contains("@") || attachUrl.contains("javascript") || attachUrl.contains(".html") || attachUrl.contains(".htm") || attachUrl.contains(".jsp") || attachUrl.contains(".aspx")) {
                                    continue;
                                }
                                if (attachUrl.contains(".shtml")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                if (attachUrl.contains("department_")) {
                                    element.attr("href", "");
                                    continue;
                                }
                                attachUrl = CrawlerManager.DownLoad(attachUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(attachUrl)) {
                                    attachUrl = attachUrl.replace(originPath, replacePath);
                                    element.attr("href", attachUrl);
                                    attachUrls.add(attachUrl);
                                }
                            }
                        }
                        hmsCNewsData.setContentLink(JsonUtil.obj2String(attachUrls));
                    } catch (Exception e) {
                        log.error("链接下载失败,是资源id为:" + originNewsData.getId());
                        hmsCNewsData.setContentLink("[]");
                    }

                    // 视频路径集合处理
                    try {
                        for (Element element : videoList) {
                            String videoUrl = element.attr("src");
                            if (StringUtils.isNotBlank(videoUrl)) {
                                videoUrl = CrawlerManager.DownLoad(videoUrl, downloadPath, refer);
                                if (StringUtils.isNotBlank(videoUrl)) {
                                    videoUrl = videoUrl.replace(originPath, replacePath);
                                    element.attr("src", videoUrl);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("视频下载失败,是资源id为:" + originNewsData.getId());
                    }
                    content = document.toString();

                    hmsCNews.setContentImageNum(imgList.size());
                    hmsCNews.setContentLinkNum(linkList.size());
                    hmsCNews.setIslink(null);
                } else {

                    hmsCNews.setContentImageNum(0);
                    hmsCNews.setContentLinkNum(0);
                    hmsCNews.setIslink(linkUrl);
                }
                hmsCNewsData.setContent(content);
                hmsCNewsData.setDescription(description);

                // 处理来源
                hmsCNewsData.setComefrom(originNewsData.getComefrom());
                // 处理浏览量
                hmsCNewsData.setViews(originNew.getViews());

                hmsCNewsData.setTopTitle(null);
                hmsCNewsData.setSubTitle(null);
                hmsCNewsData.setOriginalTitle(null);
                hmsCNewsData.setRelatedExpert(null);
                hmsCNewsData.setRelatedDepart(null);
                hmsCNewsData.setPhotographer("[]");
                hmsCNewsData.setRelevantFiles("[]");
                hmsCNewsData.setEditors(null);
                hmsCNewsData.setExecutiveEditor(null);
                hmsCNewsData.setExamineArticle(null);

                hmsCNews.setContentNum(contentNum);

                chunkResult.put(hmsCNews, hmsCNewsData);
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }

        return chunkResult;
    }


    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "").replace("&ldquo", "\"")
                .replace("&rdquo", "\"").replace("&mdash","—");
    }

    @Test
    public void test() {
        String s = CrawlerManager.DownLoad("https://oss.cd5120.cn/20250702/120541771.jpg", downloadPath, refer);
        log.info(s);
        System.out.println(s);
    }
}
