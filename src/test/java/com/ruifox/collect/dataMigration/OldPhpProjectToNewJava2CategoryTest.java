package com.ruifox.collect.dataMigration;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.station2.StationCategoryMapper2;
import com.ruifox.collect.dao.mapper.tbl.TblCategoryMapper;
import com.ruifox.collect.module.entity.station2.StationCategory2;
import com.ruifox.collect.module.entity.tbl.TblCategory;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class OldPhpProjectToNewJava2CategoryTest {

    @Autowired
    private TblCategoryMapper tblCategoryMapper;

    @Autowired
    private StationCategoryMapper2 stationCategoryMapper2;

    private ExecutorService executor;
    private int threadCount;

    // Model ID映射表：旧model_id -> 新model_id (沿用原有映射)
    private static final Map<Integer, Integer> MODEL_ID_MAPPING = new HashMap<>();

    // 全局sort计数器和锁，确保sort值唯一性和线程安全（从1开始）
    private volatile int globalSortCounter = 1;
    private final Object sortLock = new Object();

    static {
        // 根据 tbl_model (旧) 和 model (新) 的数据重新建立的映射关系
        MODEL_ID_MAPPING.put(2, 20);   // 新闻 (旧ID:2) -> 文章 (新ID:20)
        MODEL_ID_MAPPING.put(1, 19);   // 医生 (旧ID:1) -> 医生 (新ID:19)
        MODEL_ID_MAPPING.put(53, 29);  // 领导 (旧ID:53) -> 领导 (新ID:29)
        MODEL_ID_MAPPING.put(11, 41);  // 图片 (旧ID:11) -> 图集 (新ID:41)
        MODEL_ID_MAPPING.put(36, 42);  // 报刊 (旧ID:36) -> 院报 (新ID:42)
        MODEL_ID_MAPPING.put(6, 54);   // 招标 (旧ID:6) -> 招标 (新ID:54)
        MODEL_ID_MAPPING.put(14, 53);  // 下载 (旧ID:14) -> 下载 (新ID:53)
        MODEL_ID_MAPPING.put(13, 40);  // 视频 (旧ID:13) -> 视频 (新ID:40)
    }

    @Before
    public void setUp() {
        // 根据CPU核心数动态配置线程数
        this.threadCount = Math.min(Runtime.getRuntime().availableProcessors(), 6);
        this.executor = Executors.newFixedThreadPool(threadCount);
        log.info("测试线程池初始化完成，线程数：{}", threadCount);
    }

    @PreDestroy
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 主要的栏目迁移测试方法
     */
    @Test
    public void migrateTblCategoryTest() {
        try {
            log.info("开始执行TBL栏目数据迁移...");

            // 1. 切换到默认数据源，获取源数据
            DynamicDataSource.changeDefaultDataSource();

            List<TblCategory> sourceCategories;
            LambdaQueryWrapper<TblCategory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TblCategory::getSiteid, 1)
                    .orderByAsc(TblCategory::getListorder)
                    .orderByAsc(TblCategory::getCatid);
            sourceCategories = tblCategoryMapper.selectList(wrapper);

            if (sourceCategories.isEmpty()) {
                log.warn("没有找到需要迁移的栏目数据");
                return;
            }

            log.info("找到 {} 个栏目需要迁移", sourceCategories.size());

            // 2. 处理旧数据库数据（ID从1开始）
            List<TblCategory> processedCategories = processAndReassignIds(sourceCategories);

            // 3. 按层级排序（纯内存操作，不涉及数据库）
            List<TblCategory> sortedCategories = sortCategoriesByLevel(processedCategories);

            // 4. 按层级重新分配sort值（从1开始）
            List<TblCategory> finalCategories = reassignSortByLevel(sortedCategories);

            // 5. 验证最终处理结果
            validateProcessedData(finalCategories);

            // 6. 切换到新Java2数据源，执行迁移
            DynamicDataSource.changeBuildDynamicDataSource();
            try {
                migrateCategories(finalCategories);
            } finally {
                DynamicDataSource.changeDefaultDataSource();
            }

            log.info("TBL栏目数据迁移完成！");

        } catch (Exception e) {
            log.error("TBL栏目数据迁移失败", e);
            throw e;
        }
    }

    /**
     * 处理并重新分配ID的综合方法
     */
    private List<TblCategory> processAndReassignIds(List<TblCategory> sourceCategories) {
        log.info("开始处理TBL分类数据...");

        // 1. 按原始catid排序
        List<TblCategory> filteredCategories = sourceCategories.stream()
                .sorted(Comparator.comparing(TblCategory::getCatid))
                .collect(Collectors.toList());

        if (filteredCategories.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 创建id映射关系（旧catid -> 新catid，从1开始连续分配）
        Map<Integer, Integer> idMapping = new HashMap<>();
        idMapping.put(0, 0); // 添加0的映射，用于处理顶级分类

        Integer newId = 1; // 从1开始
        for (TblCategory category : filteredCategories) {
            Integer oldId = category.getCatid();
            idMapping.put(oldId, newId);
            log.debug("ID映射: {} -> {}", oldId, newId);
            newId++;
        }

        log.info("创建id映射关系完成，共 {} 个映射（不含0）", idMapping.size() - 1);

        // 3. 处理每条记录
        List<TblCategory> processedCategories = new ArrayList<>();

        for (TblCategory oldCategory : filteredCategories) {
            try {
                // 创建新对象并复制属性
                TblCategory newCategory = new TblCategory();
                BeanUtil.copyProperties(oldCategory, newCategory);

                Integer oldId = oldCategory.getCatid();
                Integer newMappedId = idMapping.get(oldId);

                // 更新catid
                newCategory.setCatid(newMappedId);

                // 保持原listorder值，用于后续按层级排序
                newCategory.setListorder(oldCategory.getListorder());

                // 更新parentid
                Integer oldParentid = oldCategory.getParentid();
                if (oldParentid == null || oldParentid == 0) {
                    newCategory.setParentid(0); // 顶级分类
                } else {
                    Integer newParentid = idMapping.get(oldParentid);
                    if (newParentid != null) {
                        newCategory.setParentid(newParentid);
                    } else {
                        // 父级不在过滤后的数据中，设为顶级
                        newCategory.setParentid(0);
                        log.warn("记录 '{}' (原catid={}) 的父级parentid={} 不存在于当前数据集中，设置为顶级",
                                newCategory.getCatname(), oldId, oldParentid);
                    }
                }

                // 更新arrparentid路径
                String newArrparentid = rebuildArrparentid(oldCategory.getArrparentid(), idMapping);
                newCategory.setArrparentid(newArrparentid);

                processedCategories.add(newCategory);

                log.debug("处理记录完成: '{}' 原catid={} -> 新catid={}, 新parentid={}, 新arrparentid={}",
                        newCategory.getCatname(), oldId, newMappedId,
                        newCategory.getParentid(), newCategory.getArrparentid());

            } catch (Exception e) {
                log.error("处理记录失败: catid={}, catname={}", oldCategory.getCatid(), oldCategory.getCatname(), e);
                throw new RuntimeException("处理记录失败", e);
            }
        }

        log.info("TBL分类数据处理完成，共处理 {} 条记录", processedCategories.size());
        return processedCategories;
    }

    /**
     * 重建arrparentid路径
     */
    private String rebuildArrparentid(String oldArrparentid, Map<Integer, Integer> idMapping) {
        if (StrUtil.isBlank(oldArrparentid)) {
            return "0,";
        }

        // 解析原路径，格式如: "0,111,119,"
        String[] pathIds = oldArrparentid.split(",");
        List<String> newPathIds = new ArrayList<>();

        for (String pathIdStr : pathIds) {
            if (StrUtil.isNotBlank(pathIdStr)) {
                try {
                    Integer pathId = Integer.valueOf(pathIdStr.trim());

                    if (pathId == 0) {
                        newPathIds.add("0");
                    } else {
                        Integer newPathId = idMapping.get(pathId);
                        if (newPathId != null) {
                            newPathIds.add(newPathId.toString());
                        } else {
                            log.warn("路径中的id={} 不存在于映射中，跳过", pathId);
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的路径ID: '{}'", pathIdStr);
                }
            }
        }

        // 确保至少有"0"
        if (newPathIds.isEmpty() || !newPathIds.contains("0")) {
            newPathIds.add(0, "0");
        }

        // 重建路径字符串，末尾加逗号
        return String.join(",", newPathIds) + ",";
    }

    /**
     * 按层级排序
     */
    private List<TblCategory> sortCategoriesByLevel(List<TblCategory> categories) {
        // 计算每个分类的层级（根据arrparentid中逗号的数量）
        Map<Integer, Integer> levelMap = new HashMap<>();
        for (TblCategory category : categories) {
            String arrparentid = category.getArrparentid();
            int level = 1;
            if (StrUtil.isNotBlank(arrparentid)) {
                level = arrparentid.split(",").length - 1;
                if (level <= 0) level = 1;
            }
            levelMap.put(category.getCatid(), level);
        }

        // 按层级和原始排序值排序
        return categories.stream()
                .sorted(Comparator.comparing((TblCategory c) -> levelMap.getOrDefault(c.getCatid(), 1))
                        .thenComparing(TblCategory::getListorder, Comparator.nullsFirst(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    /**
     * 按层级重新分配sort值
     */
    private List<TblCategory> reassignSortByLevel(List<TblCategory> categories) {
        log.info("开始重新分配sort值，当前数据量：{}", categories.size());

        // 重置全局sort计数器（从1开始）
        synchronized (sortLock) {
            globalSortCounter = 1;
        }

        // 计算层级并分组
        Map<Integer, List<TblCategory>> levelGroups = new HashMap<>();
        for (TblCategory category : categories) {
            String arrparentid = category.getArrparentid();
            int level = 1;
            if (StrUtil.isNotBlank(arrparentid)) {
                level = arrparentid.split(",").length - 1;
                if (level <= 0) level = 1;
            }
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(category);
        }

        // 按层级顺序处理
        List<TblCategory> sortedCategories = new ArrayList<>();
        List<Integer> sortedLevels = levelGroups.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        for (Integer level : sortedLevels) {
            List<TblCategory> levelCategories = levelGroups.get(level);

            // 同层级内按原listorder排序
            levelCategories.sort(Comparator.comparing(
                    category -> category.getListorder() != null ? category.getListorder() : 0
            ));

            // 为当前层级的所有分类分配新的sort值
            synchronized (sortLock) {
                for (TblCategory category : levelCategories) {
                    int oldSort = category.getListorder() != null ? category.getListorder() : 0;
                    category.setListorder(globalSortCounter);

                    log.debug("层级 {} - 分类 '{}': 原listorder={} -> 新listorder={}",
                            level, category.getCatname(), oldSort, globalSortCounter);

                    globalSortCounter++;
                }
            }

            sortedCategories.addAll(levelCategories);
            log.info("层级 {} 处理完成，包含 {} 个分类，当前全局sort计数器：{}",
                    level, levelCategories.size(), globalSortCounter - 1);
        }

        log.info("sort值重新分配完成，总计 {} 个分类，最终sort范围：1-{}",
                sortedCategories.size(), globalSortCounter - 1);

        return sortedCategories;
    }

    /**
     * 验证处理结果
     */
    private void validateProcessedData(List<TblCategory> categories) {
        Set<Integer> idSet = new HashSet<>();
        Set<Integer> sortSet = new HashSet<>();
        int duplicateIdCount = 0;
        int duplicateSortCount = 0;
        int invalidIdCount = 0;
        int invalidSortCount = 0;

        for (TblCategory category : categories) {
            // 检查重复catid
            if (!idSet.add(category.getCatid())) {
                duplicateIdCount++;
                log.error("发现重复的catid: {}, catname={}", category.getCatid(), category.getCatname());
            }

            // 检查重复listorder
            if (!sortSet.add(category.getListorder())) {
                duplicateSortCount++;
                log.error("发现重复的listorder: {}, catid={}, catname={}",
                        category.getListorder(), category.getCatid(), category.getCatname());
            }

            // 验证catid从1开始
            if (category.getCatid() < 1) {
                invalidIdCount++;
                log.error("新catid不符合要求（应从1开始）: catid={}, catname={}",
                        category.getCatid(), category.getCatname());
            }

            // 验证listorder从1开始
            if (category.getListorder() < 1) {
                invalidSortCount++;
                log.error("新listorder不符合要求（应从1开始）: listorder={}, catid={}, catname={}",
                        category.getListorder(), category.getCatid(), category.getCatname());
            }
        }

        if (duplicateIdCount == 0 && duplicateSortCount == 0 &&
                invalidIdCount == 0 && invalidSortCount == 0) {
            log.info("数据验证通过：无重复catid和listorder，catid和listorder都从1开始");
        } else {
            String errorMsg = String.format(
                    "数据验证失败：重复catid=%d，重复listorder=%d，无效catid=%d，无效listorder=%d",
                    duplicateIdCount, duplicateSortCount, invalidIdCount, invalidSortCount);
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    /**
     * 执行栏目迁移（多线程处理）
     */
    private void migrateCategories(List<TblCategory> categories) {
        // 分批处理
        List<List<TblCategory>> batches = divideTasks(categories, threadCount);

        // 创建 CompletableFuture 列表
        List<CompletableFuture<List<StationCategory2>>> futures = new ArrayList<>();

        // 提交任务到线程池
        for (int i = 0; i < batches.size(); i++) {
            final int batchIndex = i;
            final List<TblCategory> batch = batches.get(i);

            CompletableFuture<List<StationCategory2>> future = CompletableFuture
                    .supplyAsync(() -> {
                        log.info("开始处理批次 {}, 包含 {} 个栏目", batchIndex + 1, batch.size());
                        return processCategoryBatch(batch);
                    }, executor)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            log.error("批次 {} 处理失败", batchIndex + 1, throwable);
                        } else {
                            log.info("批次 {} 处理完成", batchIndex + 1);
                        }
                    });

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        try {
            List<StationCategory2> results = futures.stream()
                    .map(CompletableFuture::join)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            log.info("所有批次处理完成，共迁移 {} 个栏目", results.size());
        } catch (CompletionException e) {
            log.error("任务执行过程中发生异常", e.getCause());
            throw e;
        }
    }

    /**
     * 处理单个批次的栏目数据
     */
    private List<StationCategory2> processCategoryBatch(List<TblCategory> batch) {
        List<StationCategory2> batchResults = new ArrayList<>();

        try {
            // 重要：在子线程中重新设置数据源
            DynamicDataSource.changeBuildDynamicDataSource();

            for (TblCategory sourceCategory : batch) {
                try {
                    StationCategory2 targetCategory = convertCategory(sourceCategory);
                    if (targetCategory != null) {
                        // 插入到目标数据源
                        stationCategoryMapper2.insert(targetCategory);
                        batchResults.add(targetCategory);
                        log.debug("成功迁移栏目：{} -> {}", sourceCategory.getCatname(), targetCategory.getId());
                    }
                } catch (Exception e) {
                    log.error("迁移栏目失败：{}", sourceCategory.getCatname(), e);
                }
            }
        } finally {
            // 恢复默认数据源
            DynamicDataSource.changeDefaultDataSource();
        }

        return batchResults;
    }

    /**
     * 转换栏目数据格式
     * TblCategory -> StationCategory2
     */
    private StationCategory2 convertCategory(TblCategory source) {
        if (source == null) {
            return null;
        }

        StationCategory2 target = new StationCategory2();

        // 1. 字段映射
        // catid -> id
        target.setId(source.getCatid());

        // siteid -> siteId
        target.setSiteId(source.getSiteid());

        // parentid -> pid
        target.setPid(source.getParentid());

        // 计算level（根据arrparentid）
        int level = 1;
        if (StrUtil.isNotBlank(source.getArrparentid())) {
            level = source.getArrparentid().split(",").length - 1;
            if (level <= 0) level = 1;
        }
        target.setLevel(level);

        // listorder -> sortLevel
        target.setSortLevel(source.getListorder());

        // catname -> name
        target.setName(source.getCatname());

        // type: 强制设置为4
        target.setType(4);

        // enable -> state
        target.setState(source.getEnable() ^ 1);

        // 默认info为空字符串
        target.setInfo("");

        // modelid -> modelId (通过映射表转换)
        target.setModelId(convertModelId(source.getModelid()));

        // ismenu -> isShow
        target.setIsShow(source.getIsmenu());

        // 2. 格式转换字段
        // arrparentid -> parentString: "0,111,119," -> "[0,111,119]"
        String parentString = convertArrparentidToParentString(source.getArrparentid());
        target.setParentString(parentString);

        // catdir -> uri: 添加斜杠前缀
        String uri = convertCatdirToUri(source.getCatdir());
        target.setUri(uri);

        // 3. 时间字段设置
        // create_time和update_time：设置为当前时间戳(Double类型)
        Double currentTimestamp = (double) System.currentTimeMillis();
        target.setCreateTime(currentTimestamp);
        target.setUpdateTime(currentTimestamp);

        return target;
    }

    /**
     * 转换arrparentid格式到parentString格式
     * 从 "0,111,119," 转换为 "[0,111,119]"
     */
    private String convertArrparentidToParentString(String arrparentid) {
        if (StrUtil.isBlank(arrparentid)) {
            return "[0]";  // 默认值
        }

        // 移除末尾的逗号，然后用[]包裹
        String trimmed = arrparentid.trim();
        if (trimmed.endsWith(",")) {
            trimmed = trimmed.substring(0, trimmed.length() - 1);
        }

        return "[" + trimmed + "]";
    }

    /**
     * 转换catdir格式到uri格式
     * 在原catdir前面加上"/"
     */
    private String convertCatdirToUri(String catdir) {
        if (StrUtil.isBlank(catdir)) {
            return "/";  // 默认值
        }

        // 如果已经以"/"开头，直接返回
        if (catdir.startsWith("/")) {
            return catdir;
        }

        // 在前面加上"/"
        return "/" + catdir;
    }

    /**
     * 转换model_id
     * 通过映射表将旧model_id转换为新model_id
     */
    private Integer convertModelId(Integer oldModelId) {
        if (oldModelId == null) {
            return 20; // 返回默认值20
        }

        // 通过映射表转换
        Integer newModelId = MODEL_ID_MAPPING.get(oldModelId);
        if (newModelId != null) {
            return newModelId;
        }

        // 如果映射表中没有对应关系，记录警告并返回默认值0
        log.warn("未找到model_id映射: oldModelId={}, 使用默认值20", oldModelId);
        return 0;
    }

    /**
     * 将任务列表分割成多个批次
     */
    private <T> List<List<T>> divideTasks(List<T> tasks, int batchCount) {
        List<List<T>> batches = new ArrayList<>();
        int taskCount = tasks.size();
        int batchSize = Math.max(1, (taskCount + batchCount - 1) / batchCount);

        for (int i = 0; i < taskCount; i += batchSize) {
            int end = Math.min(i + batchSize, taskCount);
            batches.add(tasks.subList(i, end));
        }

        return batches;
    }
}