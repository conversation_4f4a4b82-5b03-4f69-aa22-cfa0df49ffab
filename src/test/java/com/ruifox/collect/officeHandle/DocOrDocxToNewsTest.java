package com.ruifox.collect.officeHandle;

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import com.ruifox.collect.util.WordToHtmlUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@SpringBootTest // 指定这是一个 Spring Boot 测试
@RunWith(SpringRunner.class) // 指定使用 Spring 运行器
public class DocOrDocxToNewsTest {
    @Test
    public void test() {
        int collectId = 612;
        // 获取 CollectTask 对象
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectId);
        // 获取与 CollectTask 相关的 TaskObject 对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());

        RedisGetManager.deleteRedisById(collectId);

        String folderPath = "C:/Users/<USER>/Desktop/testDoc"; // 文件夹路径
        String imageOutputDir = "C:/save-path/testData"; // 图片存放文件夹

        File folder = new File(folderPath);
        int sort = 1;
        if (folder.isDirectory()) {
            int index = 0;
            for (File file : Objects.requireNonNull(folder.listFiles())) {
                String htmlContent = "";

                String fileName = file.getName().toLowerCase();
                // 创建用于存储解析结果的映射
                Map<String, String> map = new HashMap<>();
                // 获取数据表字段映射
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                // 初始化字段为空字符串
                dataTableFieldMap.forEach((key, value) -> {
                    if (!map.containsKey(key)) {
                        map.put(key, "");
                    }
                });

                // 填充基本信息到映射中
                map.put("collect_task_id", String.valueOf(collectTask.getId())); // 采集任务 ID
                map.put("classification", collectTask.getImportCategoryName()); // 分类名称
                map.put("target_url", ""); // 目标 URL
                map.put("page_id", UUID.randomUUID().toString()); // 生成唯一页面 ID
                map.put("flag", "0"); // 标志位
                map.put("origin_code", ""); // 原始代码
                try {
                    if (fileName.endsWith(".docx")) {
                        //转换docx/doc文件为html,返回文档内容
                        htmlContent = WordToHtmlUtil.parseDocxToHtml(file);
                    }else if (fileName.endsWith(".doc")) {
                        htmlContent = WordToHtmlUtil.parseDocToHtml(file);
                    }
                } catch (Exception e) {
                    System.out.println("Error processing file: " + file.getName());
                    e.printStackTrace();
                }
                index++;
                if (index % 11 == 0) {
                    index = 1;
                    sort++;
                }
                // 设置排序值
                map.put("sort", collectTask.getSort() + String.format("%4s", sort).replace(" ", "0") + String.format("%4s", index).replace(" ", "0"));

                //标题
                String title = fileName.substring(0, fileName.lastIndexOf("."));
                map.put("title", title);
                //正文
                String content = htmlContent;
                map.put("content", content);
                // 将数据属性推送到 Redis
                RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
            }
        }
        //  将 TaskObject实体对象 推到 Redis
        RedisUtil.getInstance().set(RedisConstant.TASK_OBJECT + collectTask.getId(), JsonUtil.obj2String(taskObject));
        // 持久化数据
        CrawlerManager.persistenceDatas(collectTask);
    }
}
