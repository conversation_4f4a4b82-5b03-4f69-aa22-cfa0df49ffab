package com.ruifox.collect.officeHandle;

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.junit.Test;

import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Picture;
import org.apache.poi.hwpf.usermodel.Paragraph;
import org.apache.poi.hwpf.usermodel.Range;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;

@SpringBootTest // 指定这是一个 Spring Boot 测试
@RunWith(SpringRunner.class) // 指定使用 Spring 运行器
@Slf4j
public class DocxToNewsInfoTest {

    static int x = 1;

    @Test
    public void test01() {

        int collectId = 473;
        // 获取 CollectTask 对象
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectId);
        // 获取与 CollectTask 相关的 TaskObject 对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());

        RedisGetManager.deleteRedisById(collectId);

        String folderPath = "C:/Users/<USER>/Desktop/testDoc"; // 文件夹路径
        String imageOutputDir = "C:/save-path/testData"; // 图片存放文件夹
        String htmlContent = "";

        File folder = new File(folderPath);
        int sort = 1;
        if (folder.isDirectory()) {
            int index = 0;
            for (File file : Objects.requireNonNull(folder.listFiles())) {

                String fileName = file.getName().toLowerCase();
                // 创建用于存储解析结果的映射
                Map<String, String> map = new HashMap<>();
                // 获取数据表字段映射
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                // 初始化字段为空字符串
                dataTableFieldMap.forEach((key, value) -> {
                    if (!map.containsKey(key)) {
                        map.put(key, "");
                    }
                });

                // 填充基本信息到映射中
                map.put("collect_task_id", String.valueOf(collectTask.getId())); // 采集任务 ID
                map.put("classification", collectTask.getImportCategoryName()); // 分类名称
                map.put("target_url", ""); // 目标 URL
                map.put("page_id", UUID.randomUUID().toString()); // 生成唯一页面 ID
                map.put("flag", "0"); // 标志位
                map.put("origin_code", ""); // 原始代码
                try {
                    if (fileName.endsWith(".docx") || fileName.endsWith(".doc")) {
                        htmlContent = convertDocFileToHtmlString(file, imageOutputDir);
                    }
                } catch (Exception e) {
                    System.out.println("Error processing file: " + file.getName());
                    e.printStackTrace();
                }
                index++;
                if (index % 11 == 0) {
                    index = 1;
                    sort++;
                }
                map.put("sort", collectTask.getSort() + String.format("%4s", sort).replace(" ", "0") + String.format("%4s", index).replace(" ", "0")); // 排序


                //标题
                map.put("title", fileName.substring(0, fileName.indexOf(".")));
                //正文
                map.put("content", htmlContent);

                RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
            }
        }
        // 将数据推送到 Redis
        RedisUtil.getInstance().set(RedisConstant.TASK_OBJECT + collectTask.getId(), JsonUtil.obj2String(taskObject));
        // 持久化数据
        CrawlerManager.persistenceDatas(collectTask);
    }

    public static String convertDocFileToHtmlString(File file, String imageOutputDir) throws Exception {
        StringBuilder htmlBuilder = new StringBuilder();

        if (file.getName().toLowerCase().endsWith(".docx")) {
            try (FileInputStream fis = new FileInputStream(file); XWPFDocument docx = new XWPFDocument(fis)) {
                htmlBuilder.append(convertDocxToHtml(docx, imageOutputDir));
            }
        } else if (file.getName().toLowerCase().endsWith(".doc")) {
            try (FileInputStream fis = new FileInputStream(file); HWPFDocument doc = new HWPFDocument(fis)) {
                htmlBuilder.append(convertDocToHtml(doc, imageOutputDir));
            }
        }

        return htmlBuilder.toString(); // 返回完整的 HTML 字符串,标题不是
    }

    private static String convertDocxToHtml(XWPFDocument docx, String imageOutputDir) throws Exception {
        StringBuilder htmlBuilder = new StringBuilder();

        // 提取段落
        List<XWPFParagraph> paragraphs = docx.getParagraphs();
        if (paragraphs.isEmpty()) return "";

        // 设置标题（第一个段落）
        String title = paragraphs.get(0).getText();

        // 定义基础样式，添加首行缩进两字符
        String baseParagraphStyle = "font-size: 16px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2; text-indent: 2em;";
        // 定义图片样式
        String imageStyle = "max-width:600px; height:auto; display: block; margin-left: auto; margin-right: auto;";

        // 从第一个段落开始处理（索引0是标题，已处理）
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph para = paragraphs.get(i);
            StringBuilder paraContent = new StringBuilder();
            boolean hasContent = false;

            // 获取段落对齐方式
            String alignmentStyle = "";
            switch (para.getAlignment()) {
                case CENTER:
                    alignmentStyle = "text-align: center;";
                    break;
                case RIGHT:
                    alignmentStyle = "text-align: right;";
                    break;
                case LEFT:
                    alignmentStyle = "text-align: left;";
                    break;
            }

            // 合并样式
            String combinedParagraphStyle = baseParagraphStyle + alignmentStyle;

            // 遍历段落中的所有Run
            for (XWPFRun run : para.getRuns()) {
                // 处理文本
                String text = run.getText(0);
                if (text != null && !text.trim().isEmpty()) {
                    paraContent.append(text);
                    hasContent = true;
                }

                // 处理图片（保留在原始位置）
                List<XWPFPicture> pictures = run.getEmbeddedPictures();
                for (XWPFPicture picture : pictures) {
                    XWPFPictureData pic = picture.getPictureData();
                    // 保存图片
                    String extension = "." + pic.suggestFileExtension();
                    LocalDateTime now = LocalDateTime.now();
                    String imageName = String.format("%02d%02d%02d%03d%s",
                            now.getHour(), now.getMinute(), now.getSecond(), x++, extension);
                    Path imagePath = Path.of(imageOutputDir, imageName);
                    Files.write(imagePath, pic.getData());

                    // 添加图片标签（使用新样式）
                    String imgTag = String.format("<img src='%s' style='%s' alt='Image'/>",
                            imagePath.toString(), imageStyle);
                    paraContent.append(imgTag);
                    hasContent = true;
                }
            }

            // 如果有内容，则用div包裹（使用合并后的样式）
            if (hasContent) {
                htmlBuilder.append("<div style=\"").append(combinedParagraphStyle).append("\">")
                        .append(paraContent.toString())
                        .append("</div>\n");
            }
        }

        return htmlBuilder.toString();
    }

    private static String convertDocToHtml(HWPFDocument doc, String imageOutputDir) throws Exception {
        StringBuilder htmlBuilder = new StringBuilder();

        // 获取所有段落
        Range range = doc.getRange();
        List<String> content = new ArrayList<>();
        for (int i = 0; i < range.numParagraphs(); i++) {
            Paragraph para = range.getParagraph(i);
            String text = para.text().trim();
            if (!text.isEmpty()) content.add(text);
        }

        // 内容
        if (content.isEmpty()) return "";

        // 处理图片
        List<String> imagePaths = new ArrayList<>();
        for (Picture pic : doc.getPicturesTable().getAllPictures()) {
            String extension = "." + pic.suggestFileExtension();
            LocalDateTime now = LocalDateTime.now();
            String imageName = String.format("%02d%02d%02d%03d%s",
                    now.getHour(), now.getMinute(), now.getSecond(), x++, extension);
            Path imagePath = Path.of(imageOutputDir, imageName);
            Files.write(imagePath, pic.getContent());
            imagePaths.add(imagePath.toString());
        }

        // 定义基础样式，添加首行缩进两字符
        String baseParagraphStyle = "font-size: 16px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2; text-indent: 2em;";
        String imageStyle = "max-width:600px; height:auto; display: block; margin-left: auto; margin-right: auto;";

        // 处理内容段落
        for (int i = 0; i < content.size(); i++) {
            // 这里由于HWPFDocument没有直接获取对齐方式的方法，暂不处理对齐样式
            String combinedParagraphStyle = baseParagraphStyle;

            // 添加段落（使用合并后的样式）
            htmlBuilder.append("<div style=\"").append(combinedParagraphStyle).append("\">")
                    .append(content.get(i))
                    .append("</div>");

            // 添加图片（保留在段落后面）
            if (!imagePaths.isEmpty()) {
                String imgTag = String.format("<img src='%s' style='%s' alt='Image'/>",
                        imagePaths.get(0), imageStyle);
                htmlBuilder.append(imgTag);
                imagePaths.remove(0);
            }
            htmlBuilder.append("\n");
        }

        // 添加剩余图片
        for (String remainingImage : imagePaths) {
            String imgTag = String.format("<img src='%s' style='%s' alt='Image'/>",
                    remainingImage, imageStyle);
            htmlBuilder.append(imgTag).append("\n");
        }

        return htmlBuilder.toString();
    }
}