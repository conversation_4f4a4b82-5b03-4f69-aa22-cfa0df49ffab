package com.ruifox.collect.officeHandle;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruifox.collect.dao.mapper.station.StationMImageDataMapper;
import com.ruifox.collect.dao.mapper.station.StationMImageMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.module.entity.station.StationMImage;
import com.ruifox.collect.module.entity.station.StationMImageData;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;

@SpringBootTest
@RunWith(SpringRunner.class) // 指定使用SpringRunner来运行测试
@Slf4j
public class ImportImageTest {
    @Autowired
    private StationMImageDataMapper stationMImageDataMapper;
    @Autowired
    private StationMImageMapper stationMImageMapper;
    @Test
    public void test(){
        //TODO 指定要添加的栏目
        int targetCatId=111;

        //TODO  指定文件夹路径，包含该图片集
        String folderPath = "C:\\(test)科室网站素材（1）\\人才招聘-标准化专题所需素材-发杜主任--丁香园\\2、专家队伍（专家的简介及照片）\\科室主任照片";

        Map<StationMImage,StationMImageData> imageSets=new HashMap<>();
        File folder =new File(folderPath);
        File[] files=folder.listFiles();

        if(files!=null){
            for(File file:files){
                if(file.isDirectory()){
                    //TODO 新的image
                    StationMImage stationMImage=new StationMImage();
                    stationMImage.setCatId(targetCatId);
                    stationMImage.setPublishUserId(1);
                    stationMImage.setCreateTime(Double.valueOf(System.currentTimeMillis()));
                    stationMImage.setUpdateTime(Double.valueOf(System.currentTimeMillis()));
                    stationMImage.setState(99);
                    stationMImage.setUri("/"+ CrawlerManager.randomCharacterGenerator()+".html");
                    stationMImage.setIsTop(0);
                    stationMImage.setIsLock(0);
                    //TODO 新的imageData
                    StationMImageData stationMImageData=new StationMImageData();
                    stationMImageData.setUuid(UUID.randomUUID().toString());
                    //TODO 设置图集标题
                    String title=file.getName();
                    stationMImageData.setTitle(title);

                    //TODO 在子文件夹中查找图片文件
                    File[] imageFiles=file.listFiles((dir,name)->{
                        String lowerCaseName=name.toLowerCase();
                        return lowerCaseName.endsWith(".jpg")||lowerCaseName.endsWith(".png")||lowerCaseName.endsWith(".jpeg");});
                    if(imageFiles!=null&&imageFiles.length>0){
                        //TODO 处理images Json数组格式
                        JSONArray jsonArray=new JSONArray();
                        JSONObject item = new JSONObject();
                        for (File imageFile:imageFiles){
                            //TODO 设置图片标题
                            String imageTitle=imageFile.getName();
                            item.put("title",imageTitle);
                            //TODO 设置图集时间戳
                            long currentTimeMillis = System.currentTimeMillis();
                            item.put("id",currentTimeMillis);
                            //TODO 获取url图片进行替换
                            String url=CrawlerManager.changeFileUrl(imageFile);
                            if(url!=null&&!url.isBlank()){
                                item.put("url",url);
                            }
                            jsonArray.add(item);
                        }
                        stationMImageData.setImages(jsonArray.toJSONString());
                    }
                    //TODO 处理发布时间
                    stationMImageData.setPublishTime(Double.valueOf(System.currentTimeMillis()));

                    imageSets.put(stationMImage,stationMImageData);
                }
            }
        }
        //TODO 切换到java库的数据源
        DynamicDataSource.changeDynamicDataSource();
        //TODO 处理结果
        for(StationMImage targetImage:imageSets.keySet()){
            StationMImageData targetImageData=imageSets.get(targetImage);
            //TODO 先加入资源数据
            stationMImageDataMapper.insert(targetImageData);
            //TODO 引用数据引用资源数据id
            targetImage.setDataId(targetImageData.getDataId());
            //TODO 加入引用数据
            stationMImageMapper.insert(targetImage);
            //TODO 根据id更新排序
            targetImage.setSortLevel(targetImage.getId().intValue());
            stationMImageMapper.updateById(targetImage);
        }
    }
}

