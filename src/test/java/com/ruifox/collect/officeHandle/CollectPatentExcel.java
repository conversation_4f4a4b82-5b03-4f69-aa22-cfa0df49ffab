package com.ruifox.collect.officeHandle;

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;

@SpringBootTest
@RunWith(SpringRunner.class)
public class CollectPatentExcel {

    @Test
    public void collectExcels() {
        // Excel文件路径
        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\专利.xlsx";
        // 图片文件路径
        String folderPath = "C:\\Users\\<USER>\\Desktop\\专利摘要附图";
        List<String> pngFiles = classifyPngFiles(folderPath);
        // pdf文件路径
        String pdfFilePath = "C:\\Users\\<USER>\\Desktop\\专利pdf";
        List<String> pdfFiles = classifyPdfFiles(pdfFilePath);

        int collectTaskId = 475;
        // 获取 CollectTask 对象
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        // 获取与 CollectTask 相关的 TaskObject 对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());

        RedisGetManager.deleteRedisById(collectTaskId);

        try (FileInputStream fis = new FileInputStream(new File(excelFilePath));
             Workbook workbook = WorkbookFactory.create(fis)) {
            // 获取第一个工作表（索引从0开始），你可以根据实际需要修改索引获取不同的工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 获取合并区域
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                int firstRow = region.getFirstRow();
                int lastRow = region.getLastRow();
                int firstCol = region.getFirstColumn();
                int lastCol = region.getLastColumn();

                // 设置合并区域的内容为起始单元格的内容
                for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
                    for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                        Row row = sheet.getRow(rowNum);
                        if (row == null) {
                            continue;
                        }
                        Cell cell = row.getCell(colNum);
                        if (cell == null) {
                            continue;
                        }
                        Cell startCell = sheet.getRow(firstRow).getCell(firstCol);
                        if (startCell != null) {
                            cell.setCellValue(startCell.getStringCellValue());
                        }
                    }
                }
            }

            int index = 0;
            int x = 0;
            // 遍历每一行，从第二行开始（索引为1，通常第一行是标题行，可根据实际情况调整）
            for (int rowIndex = 1; rowIndex < 320; rowIndex++) {

                // 创建用于存储解析结果的映射
                Map<String, String> map = new HashMap<>();
                // 获取数据表字段映射
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                // 初始化字段为空字符串
                dataTableFieldMap.forEach((key, value) -> {
                    if (!map.containsKey(key)) {
                        map.put(key, "");
                    }
                });

                // 填充基本信息到映射中
                map.put("collect_task_id", String.valueOf(collectTask.getId())); // 采集任务 ID
                map.put("classification", collectTask.getImportCategoryName()); // 分类名称
                map.put("target_url", ""); // 目标 URL
                map.put("page_id", UUID.randomUUID().toString()); // 生成唯一页面 ID
                map.put("flag", "0"); // 标志位
                map.put("origin_code", ""); // 原始代码

                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    index++;
                    Cell patentTypeCell = row.getCell(0);
                    String patentType = " ";
                    if (patentTypeCell != null) {
                        patentType = patentTypeCell.getStringCellValue().trim();
                    }
                    if (!patentType.contains("实用新型专利")) {
                        continue;
                    }
                    map.put("patent_type", patentType);

                    Cell applicantCodeCell = row.getCell(1);
                    String applicantCode = " ";
                    if (applicantCodeCell != null) {
                        applicantCode = applicantCodeCell.getStringCellValue().trim();
                    }
                    map.put("applicant_code", applicantCode);

                    Cell applicantDateCell = row.getCell(2);
                    String applicantDate = " ";
                    if (applicantDateCell != null) {
                        applicantDate = applicantDateCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isNotBlank(applicantDate)) {
                        applicantDate = applicantDate.trim();
                        Date date = DateUtils.parseDate(applicantDate, "yyyy.MM.dd");
                        String timestampInSeconds = String.valueOf(date.getTime() / 1000);
                        map.put("applicant_date", timestampInSeconds);
                    } else {
                        map.put("applicant_date", "");
                    }

                    Cell titleCell = row.getCell(3);
                    String title = " ";
                    if (titleCell != null) {
                        title = titleCell.getStringCellValue().trim();
                    }
                    map.put("title", title);

                    Cell theAbstractCell = row.getCell(5);
                    String theAbstract = " ";
                    if (theAbstractCell != null) {
                        theAbstract = theAbstractCell.getStringCellValue().trim();
                    }
                    map.put("abstract", theAbstract);

                    Cell zQlyqCell = row.getCell(6);
                    String zQlyq = " ";
                    if (zQlyqCell != null) {
                        zQlyq = zQlyqCell.getStringCellValue().trim();
                    }
                    map.put("z_qlyq", zQlyq);

                    Cell zDirectoryCell = row.getCell(7);
                    String zDirectory = " ";
                    if (zDirectoryCell != null) {
                        zDirectory = zDirectoryCell.getStringCellValue().trim();
                    }
                    map.put("z_directory", zDirectory);

                    Cell publicIpcTypeCell = row.getCell(8);
                    String publicIpcType = " ";
                    if (publicIpcTypeCell != null) {
                        publicIpcType = publicIpcTypeCell.getStringCellValue().trim();
                    }
                    map.put("public_ipc_type", publicIpcType);

                    Cell cpcTypeCell = row.getCell(9);
                    String cpcType = " ";
                    if (cpcTypeCell != null) {
                        cpcType = cpcTypeCell.getStringCellValue().trim();
                    }
                    map.put("cpc_type", cpcType);

                    String agency = "";
                    agency = zDirectory.substring(zDirectory.indexOf("代理机构：") + "代理机构：".length(), zDirectory.indexOf("发明人：")).trim();
                    map.put("agency", agency);

                    String inventor = "";
                    inventor = zDirectory.substring(zDirectory.indexOf("发明人：") + "发明人：".length(), zDirectory.indexOf("申请（专利权）人：")).trim();
                    map.put("inventor", inventor);

                    String applicant = "";
                    applicant = zDirectory.substring(zDirectory.indexOf("申请（专利权）人：") + "申请（专利权）人：".length(), zDirectory.indexOf("申请人所在国家/地区/组织：")).trim();
                    map.put("applicant", applicant);

                    String inventorArea = "";
                    if (zDirectory.contains("公开日期")) {
                        inventorArea = zDirectory.substring(zDirectory.indexOf("申请人所在国家/地区/组织：") + "申请人所在国家/地区/组织：".length(), zDirectory.indexOf("公开日期")).trim();
                        map.put("inventor_area", inventorArea);

                        String sqPublicDate = "";
                        sqPublicDate = zDirectory.substring(zDirectory.indexOf("公开日期（授权）：") + "公开日期（授权）：".length()).trim();
                        map.put("sq_public_date", sqPublicDate);
                    } else {
                        inventorArea = zDirectory.substring(zDirectory.indexOf("申请人所在国家/地区/组织：") + "申请人所在国家/地区/组织：".length()).trim();
                        map.put("inventor_area", inventorArea);
                    }

                    String thumb = "";
                    for (String img : pngFiles) {
                        String imgName = getRelativePath(folderPath, img);
                        imgName = imgName.substring(0, imgName.indexOf("."));
                        if (imgName.equals(String.valueOf(index+1))) {
                            String extension = img.substring(img.lastIndexOf("."));
                            LocalDateTime localDateTime = LocalDateTime.now();
                            String hour = String.format("%02d", localDateTime.getHour());
                            String minute = String.format("%02d", localDateTime.getMinute());
                            String second = String.format("%02d", localDateTime.getSecond());
                            String temp = String.format("%03d", ++x);
                            String filename = hour + minute + second + temp;
                            thumb = "C:/Collect_Data/hospital-cqmu/20241597/" + filename + extension;
                            File sourceFile = new File(img);
                            File destinationFile = new File(thumb);
                            // 重命名文件并保存
                            if (Files.copy(sourceFile.toPath(), destinationFile.toPath()) != null) {
                                map.put("thumb", thumb);
                            }
                        }
                    }

                    String zlPdf = "";
                    for (String pdf : pdfFiles) {
                        String pdfName = getRelativePath(pdfFilePath, pdf);
                        pdfName = pdfName.substring(0, pdfName.indexOf("."));
                        if (pdfName.equals(title)) {
                            String extension = pdf.substring(pdf.lastIndexOf("."));
                            LocalDateTime localDateTime = LocalDateTime.now();
                            String hour = String.format("%02d", localDateTime.getHour());
                            String minute = String.format("%02d", localDateTime.getMinute());
                            String second = String.format("%02d", localDateTime.getSecond());
                            String temp = String.format("%03d", ++x);
                            String filename = hour + minute + second + temp;
                            zlPdf = "C:/Collect_Data/hospital-cqmu/20241597/" + filename + extension;
                            File sourceFile = new File(pdf);
                            File destinationFile = new File(zlPdf);
                            // 重命名文件并保存
                            if (Files.copy(sourceFile.toPath(), destinationFile.toPath()) != null) {
                                map.put("zl_pdf", zlPdf);
                            }
                        }
                    }

                    if (!thumb.isBlank()) {
                        String zyPic = "<p><img src=\"" + thumb + "\" alt=\"\" /></p>";
                        map.put("zy_pic", zyPic);
                    }

                    String sort = "00100001" + String.format("%4s", index).replace(" ", "0");
                    map.put("sort", sort);


                    RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
                }
            }
            // 持久化数据
            CrawlerManager.persistenceDatas(collectTask);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    public static List<String> classifyPngFiles(String folderPath) {
        List<String> pngFiles = new ArrayList<>();
        File folder = new File(folderPath);
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    classifyPngFiles(file.getAbsolutePath()).forEach(pngFiles::add); // 递归处理子文件夹
                }
                if (file.isFile() && file.getName().toLowerCase().endsWith(".png")) {// 获取相对路径并添加到列表
                    pngFiles.add(file.getAbsolutePath());
                }
            }
        }
        return pngFiles;
    }

    public static List<String> classifyPdfFiles(String folderPath) {
        List<String> pdfFiles = new ArrayList<>();
        File folder = new File(folderPath);
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    classifyPdfFiles(file.getAbsolutePath()).forEach(pdfFiles::add); // 递归处理子文件夹
                }
                if (file.isFile() && file.getName().toLowerCase().endsWith(".pdf")) {
                    pdfFiles.add(file.getAbsolutePath());
                }
            }
        }
        return pdfFiles;
    }

    private static String getRelativePath(String basePath, String filePath) {
        int baseLength = new File(basePath).getAbsolutePath().length();
        if (filePath.length() >= baseLength) {
            return filePath.substring(baseLength + 1);
        }
        return filePath;
    }
}
