package com.ruifox.collect.officeHandle;

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class DocxToNewsInfoOptimizedTest {

    // 使用线程安全的计数器
    private static final AtomicInteger imageCounter = new AtomicInteger(1);

    @Test
    public void test01() {
        int collectId = 473;
        // 获取 CollectTask 对象
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectId);
        // 获取与 CollectTask 相关的 TaskObject 对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());

        RedisGetManager.deleteRedisById(collectId);

        String folderPath = "C:/Users/<USER>/Desktop/testDoc"; // 文件夹路径
        String imageOutputDir = "C:/save-path/testData"; // 图片存放文件夹

        File folder = new File(folderPath);
        int sort = 1;
        if (folder.isDirectory()) {
            int index = 0;
            for (File file : Objects.requireNonNull(folder.listFiles())) {
                String fileName = file.getName().toLowerCase();
                
                // 创建用于存储解析结果的映射
                Map<String, String> map = new HashMap<>();
                // 获取数据表字段映射
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                // 初始化字段为空字符串
                dataTableFieldMap.forEach((key, value) -> {
                    if (!map.containsKey(key)) {
                        map.put(key, "");
                    }
                });

                // 填充基本信息到映射中
                map.put("collect_task_id", String.valueOf(collectTask.getId()));
                map.put("classification", collectTask.getImportCategoryName());
                map.put("target_url", "");
                map.put("page_id", UUID.randomUUID().toString());
                map.put("flag", "0");
                map.put("origin_code", "");

                String htmlContent = "";
                try {
                    if (fileName.endsWith(".docx") || fileName.endsWith(".doc")) {
                        htmlContent = convertDocFileToHtmlString(file, imageOutputDir);
                    }
                } catch (Exception e) {
                    log.error("Error processing file: {}", file.getName(), e);
                    continue; // 跳过有问题的文件，继续处理下一个
                }

                index++;
                if (index % 11 == 0) {
                    index = 1;
                    sort++;
                }
                map.put("sort", collectTask.getSort() + String.format("%4s", sort).replace(" ", "0") + String.format("%4s", index).replace(" ", "0"));

                // 标题
                map.put("title", fileName.substring(0, fileName.indexOf(".")));
                // 正文
                map.put("content", htmlContent);

                RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
            }
        }
        
        // 将数据推送到 Redis
        RedisUtil.getInstance().set(RedisConstant.TASK_OBJECT + collectTask.getId(), JsonUtil.obj2String(taskObject));
        // 持久化数据
        CrawlerManager.persistenceDatas(collectTask);
    }

    public static String convertDocFileToHtmlString(File file, String imageOutputDir) throws Exception {
        StringBuilder htmlBuilder = new StringBuilder();

        if (file.getName().toLowerCase().endsWith(".docx")) {
            try (FileInputStream fis = new FileInputStream(file); XWPFDocument docx = new XWPFDocument(fis)) {
                htmlBuilder.append(convertDocxToHtml(docx, imageOutputDir));
            }
        } else if (file.getName().toLowerCase().endsWith(".doc")) {
            try (FileInputStream fis = new FileInputStream(file); HWPFDocument doc = new HWPFDocument(fis)) {
                htmlBuilder.append(convertDocToHtmlOptimized(doc, imageOutputDir));
            }
        }

        return htmlBuilder.toString();
    }

    private static String convertDocxToHtml(XWPFDocument docx, String imageOutputDir) throws Exception {
        StringBuilder htmlBuilder = new StringBuilder();

        // 提取段落
        List<XWPFParagraph> paragraphs = docx.getParagraphs();
        if (paragraphs.isEmpty()) return "";

        // 定义基础样式，添加首行缩进两字符
        String baseParagraphStyle = "font-size: 16px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2; text-indent: 2em;";
        // 定义图片样式
        String imageStyle = "max-width:600px; height:auto; display: block; margin-left: auto; margin-right: auto;";

        // 处理所有段落
        for (XWPFParagraph para : paragraphs) {
            StringBuilder paraContent = new StringBuilder();
            boolean hasContent = false;

            // 获取段落对齐方式
            String alignmentStyle = "";
            switch (para.getAlignment()) {
                case CENTER:
                    alignmentStyle = "text-align: center;";
                    break;
                case RIGHT:
                    alignmentStyle = "text-align: right;";
                    break;
                case LEFT:
                    alignmentStyle = "text-align: left;";
                    break;
            }

            // 合并样式
            String combinedParagraphStyle = baseParagraphStyle + alignmentStyle;

            // 遍历段落中的所有Run
            for (XWPFRun run : para.getRuns()) {
                // 处理文本
                String text = run.getText(0);
                if (text != null && !text.trim().isEmpty()) {
                    paraContent.append(text);
                    hasContent = true;
                }

                // 处理图片（保留在原始位置）
                List<XWPFPicture> pictures = run.getEmbeddedPictures();
                for (XWPFPicture picture : pictures) {
                    XWPFPictureData pic = picture.getPictureData();
                    // 保存图片
                    String extension = "." + pic.suggestFileExtension();
                    String imageName = generateImageName(extension);
                    Path imagePath = Path.of(imageOutputDir, imageName);
                    Files.write(imagePath, pic.getData());

                    // 添加图片标签
                    String imgTag = String.format("<img src='%s' style='%s' alt='Image'/>",
                            imagePath.toString(), imageStyle);
                    paraContent.append(imgTag);
                    hasContent = true;
                }
            }

            // 如果有内容，则用div包裹
            if (hasContent) {
                htmlBuilder.append("<div style=\"").append(combinedParagraphStyle).append("\">")
                        .append(paraContent.toString())
                        .append("</div>\n");
            }
        }

        return htmlBuilder.toString();
    }

    /**
     * 优化的 .doc 文件转换方法
     * 主要改进：尝试更好地分布图片，而不是简单地放在每段后面
     */
    private static String convertDocToHtmlOptimized(HWPFDocument doc, String imageOutputDir) throws Exception {
        StringBuilder htmlBuilder = new StringBuilder();

        // 获取所有段落
        Range range = doc.getRange();
        List<String> paragraphs = new ArrayList<>();
        
        // 收集所有非空段落
        for (int i = 0; i < range.numParagraphs(); i++) {
            Paragraph para = range.getParagraph(i);
            String text = para.text().trim();
            if (!text.isEmpty() && !text.equals("\r")) { // 过滤空段落和仅包含回车的段落
                paragraphs.add(text);
            }
        }

        if (paragraphs.isEmpty()) return "";

        // 处理图片
        List<String> imagePaths = new ArrayList<>();
        try {
            for (Picture pic : doc.getPicturesTable().getAllPictures()) {
                String extension = "." + pic.suggestFileExtension();
                String imageName = generateImageName(extension);
                Path imagePath = Path.of(imageOutputDir, imageName);
                Files.write(imagePath, pic.getContent());
                imagePaths.add(imagePath.toString());
            }
        } catch (Exception e) {
            log.warn("Failed to extract images from .doc file: {}", e.getMessage());
        }

        // 定义样式
        String baseParagraphStyle = "font-size: 16px; color: #000000; font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif; line-height: 2; text-indent: 2em;";
        String imageStyle = "max-width:600px; height:auto; display: block; margin-left: auto; margin-right: auto; margin: 10px auto;";

        // 优化的图片分布策略
        if (imagePaths.isEmpty()) {
            // 没有图片，直接输出所有段落
            for (String paragraph : paragraphs) {
                htmlBuilder.append("<div style=\"").append(baseParagraphStyle).append("\">")
                        .append(paragraph)
                        .append("</div>\n");
            }
        } else {
            // 有图片时的分布策略
            distributeImagesInParagraphs(htmlBuilder, paragraphs, imagePaths, baseParagraphStyle, imageStyle);
        }

        return htmlBuilder.toString();
    }

    /**
     * 智能分布图片到段落中的方法
     * 策略：
     * 1. 如果图片数量 <= 段落数量：尽量均匀分布
     * 2. 如果图片数量 > 段落数量：部分段落可能包含多张图片
     * 3. 尝试避免连续的图片，让图文更自然地交替
     */
    private static void distributeImagesInParagraphs(StringBuilder htmlBuilder, List<String> paragraphs, 
            List<String> imagePaths, String baseParagraphStyle, String imageStyle) {
        
        int totalParagraphs = paragraphs.size();
        int totalImages = imagePaths.size();
        
        // 计算图片分布策略
        List<Integer> imageDistribution = calculateImageDistribution(totalParagraphs, totalImages);
        
        int imageIndex = 0;
        for (int i = 0; i < totalParagraphs; i++) {
            // 添加段落
            htmlBuilder.append("<div style=\"").append(baseParagraphStyle).append("\">")
                    .append(paragraphs.get(i))
                    .append("</div>\n");
            
            // 根据分布策略添加图片
            int imagesToAdd = imageDistribution.get(i);
            for (int j = 0; j < imagesToAdd && imageIndex < totalImages; j++) {
                String imgTag = String.format("<img src='%s' style='%s' alt='Image'/>",
                        imagePaths.get(imageIndex), imageStyle);
                htmlBuilder.append(imgTag).append("\n");
                imageIndex++;
            }
        }
        
        // 添加剩余的图片（如果有的话）
        while (imageIndex < totalImages) {
            String imgTag = String.format("<img src='%s' style='%s' alt='Image'/>",
                    imagePaths.get(imageIndex), imageStyle);
            htmlBuilder.append(imgTag).append("\n");
            imageIndex++;
        }
    }

    /**
     * 计算图片在段落中的分布
     * 返回一个列表，表示每个段落后应该放置多少张图片
     */
    private static List<Integer> calculateImageDistribution(int totalParagraphs, int totalImages) {
        List<Integer> distribution = new ArrayList<>(Collections.nCopies(totalParagraphs, 0));
        
        if (totalImages == 0) {
            return distribution;
        }
        
        if (totalImages <= totalParagraphs) {
            // 图片数量少于或等于段落数量：尽量均匀分布
            int interval = totalParagraphs / totalImages;
            int remainder = totalParagraphs % totalImages;
            
            for (int i = 0; i < totalImages; i++) {
                int position = i * interval + Math.min(i, remainder);
                if (position < totalParagraphs) {
                    distribution.set(position, 1);
                }
            }
        } else {
            // 图片数量多于段落数量：平均分配，某些段落可能有多张图片
            int imagesPerParagraph = totalImages / totalParagraphs;
            int extraImages = totalImages % totalParagraphs;
            
            for (int i = 0; i < totalParagraphs; i++) {
                distribution.set(i, imagesPerParagraph);
                if (i < extraImages) {
                    distribution.set(i, distribution.get(i) + 1);
                }
            }
        }
        
        return distribution;
    }

    /**
     * 生成唯一的图片文件名
     */
    private static String generateImageName(String extension) {
        LocalDateTime now = LocalDateTime.now();
        return String.format("%02d%02d%02d%03d%s",
                now.getHour(), now.getMinute(), now.getSecond(), 
                imageCounter.getAndIncrement(), extension);
    }
} 