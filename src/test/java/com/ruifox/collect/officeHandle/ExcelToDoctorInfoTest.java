package com.ruifox.collect.officeHandle;

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;

@SpringBootTest // 指定这是一个 Spring Boot 测试
@RunWith(SpringRunner.class) // 指定使用 Spring 运行器
public class ExcelToDoctorInfoTest {
    @Test // 指定这是一个测试方法
    public void test() { // 测试方法开始

        int collectTaskId = 475;
        // 获取 CollectTask 对象
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        // 获取与 CollectTask 相关的 TaskObject 对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());

        RedisGetManager.deleteRedisById(collectTaskId);

        // 指定文件夹路径，包含该科室的医生信息和照片
        String folderPath = "C:\\Users\\<USER>\\Desktop\\医生照片";
        // 获取该文件夹中的所有 .docx .doc 和 .jpg 文件
        List<String> imgFiles = classifyJpgFiles(folderPath);

        // Excel文件路径，需替换成实际的文件路径
        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\医生导入.xlsx";

        try (FileInputStream fis = new FileInputStream(new File(excelFilePath));
             Workbook workbook = WorkbookFactory.create(fis)) {
            // 获取第一个工作表（索引从0开始），你可以根据实际需要修改索引获取不同的工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 获取合并区域
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                int firstRow = region.getFirstRow();
                int lastRow = region.getLastRow();
                int firstCol = region.getFirstColumn();
                int lastCol = region.getLastColumn();

                // 设置合并区域的内容为起始单元格的内容
                for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
                    for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                        Row row = sheet.getRow(rowNum);
                        if (row == null) {
                            continue;
                        }
                        Cell cell = row.getCell(colNum);
                        if (cell == null) {
                            continue;
                        }
                        Cell startCell = sheet.getRow(firstRow).getCell(firstCol);
                        if (startCell != null) {
                            cell.setCellValue(startCell.getStringCellValue());
                        }
                    }
                }
            }

            int index = 0;
            // 遍历每一行，从第二行开始（索引为1，通常第一行是标题行，可根据实际情况调整）
            // 从第二行一直遍历，知道姓名获取为空
            for (int rowIndex = 1; rowIndex != 0 ; rowIndex++) {
                // 创建用于存储解析结果的映射
                Map<String, String> map = new HashMap<>();
                // 获取数据表字段映射
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                // 初始化字段为空字符串
                dataTableFieldMap.forEach((key, value) -> {
                    if (!map.containsKey(key)) {
                        map.put(key, "");
                    }
                });

                // 填充基本信息到映射中
                map.put("collect_task_id", String.valueOf(collectTask.getId())); // 采集任务 ID
                map.put("classification", collectTask.getImportCategoryName()); // 分类名称
                map.put("target_url", ""); // 目标 URL
                map.put("page_id", UUID.randomUUID().toString()); // 生成唯一页面 ID
                map.put("flag", "0"); // 标志位
                map.put("origin_code", ""); // 原始代码

                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    index++;
                    // 获取医生姓名
                    Cell nameCell = row.getCell(0);
                    String name = "";
                    if (nameCell != null) {
                        name = nameCell.getStringCellValue().trim();
                    }else{
                        //没有名字，到最后一条了
                        break;
                    }
                    map.put("title", name);

                    // 获取所属科室
                    Cell departCell = row.getCell(1);
                    String depart = "";
                    if (departCell != null) {
                        depart = departCell.getStringCellValue().trim();
                    }
                    map.put("depart", depart);

                    // 获取医师职称
                    Cell docPositionCell = row.getCell(2);
                    String docPosition = "";
                    if (docPositionCell != null) {
                        docPosition = docPositionCell.getStringCellValue().trim();
                    }
                    map.put("doc_position", docPosition);

                    // 获取教务职称
                    Cell eduPositionCell = row.getCell(3);
                    String eduPosition = "";
                    if (eduPositionCell != null) {
                        eduPosition = eduPositionCell.getStringCellValue().trim();
                    }
                    map.put("edu_position", eduPosition);

                    // 获取教学岗位
                    Cell eduPostCell = row.getCell(4);
                    String eduPost = "";
                    if (eduPostCell != null) {
                        eduPost = eduPostCell.getStringCellValue().trim();
                    }
                    map.put("edu_post", eduPost);

                    // 获取院内职务
                    Cell hosPositionCell = row.getCell(5);
                    String hosPosition = "";
                    if (hosPositionCell != null) {
                        hosPosition = hosPositionCell.getStringCellValue().trim();
                    }
                    map.put("hos_position", hosPosition);

                    // 获取专家级别
                    Cell levelCell = row.getCell(6);
                    String level = "";
                    if (levelCell != null) {
                        level = levelCell.getStringCellValue().trim();
                    }
                    map.put("level", level);

                    // 获取医生擅长
                    Cell goodatCell = row.getCell(7);
                    String goodat = "";
                    if (goodatCell != null) {
                        goodat = goodatCell.getStringCellValue().trim();
                        goodat = goodat.replace("擅长","");
                    }
                    map.put("goodat", goodat);

                    // 获取医生简介
                    Cell contentCell = row.getCell(8);
                    String content = "";
                    if (contentCell != null) {
                        content = contentCell.getStringCellValue().trim();
                    }
                    map.put("content", content);

                    // 获取医生头像
                    String thumb = name;
                    int repetion = 0;
                    for (String img : imgFiles) {
                        if (img.contains(thumb)) {
                            repetion++;
                            String extension = img.substring(img.lastIndexOf("."));
                            LocalDateTime localDateTime = LocalDateTime.now();
                            String hour = String.format("%02d", localDateTime.getHour());
                            String minute = String.format("%02d", localDateTime.getMinute());
                            String second = String.format("%02d", localDateTime.getSecond());
                            String temp = String.format("%03d", index);
                            String filename = hour + minute + second + temp;
                            thumb = ConfigUtil.getProperties("common.save-path") + collectTask.getHost() + "/" + collectTask.getFileFolderName() + "/" + filename + extension;
                            File sourceFile = new File(img);
                            File destinationFile = new File(thumb);
                            // 重命名文件并保存
                            if (Files.copy(sourceFile.toPath(), destinationFile.toPath()) != null) {
                                map.put("thumb", thumb);
                            }
                        }
                    }
                    if (repetion == 0) {
                        map.put("thumb", "未找到");
                    } else if (repetion > 1) {
                        map.put("thumb", "重复");
                    }

                    // 获取sort
                    String sort = "00100001" + String.format("%4s", index).replace(" ", "0");
                    map.put("sort", sort);

                    RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
                }
            }
            // 持久化数据
            CrawlerManager.persistenceDatas(collectTask);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static List<String> classifyJpgFiles(String folderPath) {
        List<String> imgFiles = new ArrayList<>();
        File folder = new File(folderPath);
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    classifyJpgFiles(file.getAbsolutePath()).forEach(imgFiles::add); // 递归处理子文件夹
                }
                if (file.isFile()) {
                    imgFiles.add(file.getAbsolutePath());
                }
            }
        }
        return imgFiles;
    }
}
