package com.ruifox.collect.officeHandle;

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import com.ruifox.collect.util.RedisUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@SpringBootTest // 指定这是一个 Spring Boot 测试
@RunWith(SpringRunner.class) // 指定使用 Spring 运行器
public class ExcelToNewsInfoTest {
    @Test
    public void test() {
        // Excel文件路径，需替换成实际的文件路径
        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\留言.xlsx";

        int collectTaskId = 483;
        // 获取 CollectTask 对象
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        // 获取与 CollectTask 相关的 TaskObject 对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());

        RedisGetManager.deleteRedisById(collectTaskId);

        try (FileInputStream fis = new FileInputStream(new File(excelFilePath));
             Workbook workbook = WorkbookFactory.create(fis)) {
            // 获取第一个工作表（索引从0开始），你可以根据实际需要修改索引获取不同的工作表
            Sheet sheet = workbook.getSheetAt(12);

            // 获取合并区域
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                int firstRow = region.getFirstRow();
                int lastRow = region.getLastRow();
                int firstCol = region.getFirstColumn();
                int lastCol = region.getLastColumn();

                // 设置合并区域的内容为起始单元格的内容
                for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
                    for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                        Row row = sheet.getRow(rowNum);
                        if (row == null) {
                            continue;
                        }
                        Cell cell = row.getCell(colNum);
                        if (cell == null) {
                            continue;
                        }
                        Cell startCell = sheet.getRow(firstRow).getCell(firstCol);
                        if (startCell != null) {
                            cell.setCellValue(startCell.getStringCellValue());
                        }
                    }
                }
            }

            int index = 0;

            int year = 2015;
            int month = 2;
            int day = 14;
            // 遍历每一行，从第二行开始（索引为1，通常第一行是标题行，可根据实际情况调整）
            for (int rowIndex = 0; rowIndex < 27; rowIndex++) {

                // 创建用于存储解析结果的映射
                Map<String, String> map = new HashMap<>();
                // 获取数据表字段映射
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                // 初始化字段为空字符串
                dataTableFieldMap.forEach((key, value) -> {
                    if (!map.containsKey(key)) {
                        map.put(key, "");
                    }
                });

                // 填充基本信息到映射中
                map.put("collect_task_id", String.valueOf(collectTask.getId())); // 采集任务 ID
                map.put("classification", collectTask.getImportCategoryName()); // 分类名称
                map.put("target_url", ""); // 目标 URL
                map.put("page_id", UUID.randomUUID().toString()); // 生成唯一页面 ID
                map.put("flag", "0"); // 标志位
                map.put("origin_code", ""); // 原始代码

                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    if(day<31){
                        day++;
                    }else{
                        day = 1;
                        month++;
                    }
                    String time = String.format("%4s", year) + "-" + String.format("%2s", month) + "-" + String.format("%2s", day);
                    Date date = DateUtils.parseDate(time, "yyyy-MM-dd");
                    time = String.valueOf(date.getTime() / 1000);
                    index++;
                    Cell contentCell = row.getCell(1);
                    String content = "";
                    if (contentCell != null) {
                        content = contentCell.getStringCellValue();
                    }
                    String sort = "00100001" + String.format("%4s", index).replace(" ", "0");
                    map.put("sort", sort);
                    map.put("title", sheet.getSheetName());
                    map.put("publish_time", time);
                    map.put("content", content);

                    RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
                }
            }
            // 持久化数据
            CrawlerManager.persistenceDatas(collectTask);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
