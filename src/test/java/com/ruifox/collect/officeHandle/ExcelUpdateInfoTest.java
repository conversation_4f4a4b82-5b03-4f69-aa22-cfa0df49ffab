package com.ruifox.collect.officeHandle;

import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCategoryMapper;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.module.entity.hms.HmsCategory;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest // 指定这是一个 Spring Boot 测试
@RunWith(SpringRunner.class) // 指定使用 Spring 运行器
public class ExcelUpdateInfoTest {
    @Test
    public void test() {
        // Excel文件路径，需替换成实际的文件路径
        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\全院医生擅长.xlsx";
//        //存放正常修改的医生
//        Map<Double, String> map = new HashMap<>();
//        //存放科室姓名重复的医生
//        Map<Double, String> reMap = new HashMap<>();
//        //存放未找到的医生
//        Map<Double, String> notFoundMap = new HashMap<>();
        Map<String, String> notFoundMap = new HashMap<>();

        DynamicDataSource.changeDynamicDataSource();

        HmsCMansDataMapper hmsCMansDataMapper = ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class);
        HmsCategoryMapper hmsCategoryMapper = ApplicationContextProvider.getBeanByType(HmsCategoryMapper.class);
        List<HmsCategory> hmsCategories = hmsCategoryMapper.selectList(null);
        List<HmsCMansData> hmsCMansDataList = hmsCMansDataMapper.selectList(null);

        try (FileInputStream fis = new FileInputStream(new File(excelFilePath));
             Workbook workbook = WorkbookFactory.create(fis)) {

            // 获取第一个工作表（索引从0开始），你可以根据实际需要修改索引获取不同的工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历每一行，从第二行开始（索引为1，通常第一行是标题行，可根据实际情况调整）
            for (int rowIndex = 1; rowIndex < 380; rowIndex++) {
                int n = 0;
                int x = 0;
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    // 遍历每一列，获取单元格数据
                    Cell depaertCell = row.getCell(0);
                    String departName = "";
                    if (depaertCell != null) {
                        departName = depaertCell.getStringCellValue();
                    }

                    Cell nameCell = row.getCell(1);
                    String name = "";
                    if (nameCell != null) {
                        name = nameCell.getStringCellValue();
                    }

                    Cell docCell = row.getCell(2);
                    String doc_position = "";
                    if (docCell != null) {
                        doc_position = docCell.getStringCellValue();
                    }
                    if (doc_position.equals("副主任医师")) {
                        doc_position = "2";
                    } else if (doc_position.equals("副主任中医师")) {
                        doc_position = "22";
                    } else if (doc_position.equals("工程师")) {
                        doc_position = "23";
                    } else if (doc_position.equals("护师")) {
                        doc_position = "9";
                    } else if (doc_position.equals("检验师")) {
                        doc_position = "19";
                    } else if (doc_position.equals("医师")) {
                        doc_position = "4";
                    } else if (doc_position.equals("中西医结合副主任医师")) {
                        doc_position = "24";
                    } else if (doc_position.equals("中西医结合医师")) {
                        doc_position = "25";
                    } else if (doc_position.equals("中西医结合主任医师")) {
                        doc_position = "26";
                    } else if (doc_position.equals("中西医结合主治医师")) {
                        doc_position = "27";
                    } else if (doc_position.equals("中医副主任护师")) {
                        doc_position = "28";
                    } else if (doc_position.equals("中医师")) {
                        doc_position = "29";
                    } else if (doc_position.equals("主管护师")) {
                        doc_position = "8";
                    } else if (doc_position.equals("主任医师")) {
                        doc_position = "1";
                    } else if (doc_position.equals("主任中医师")) {
                        doc_position = "30";
                    } else if (doc_position.equals("主治医师")) {
                        doc_position = "3";
                    } else if (doc_position.equals("主治中医师")) {
                        doc_position = "31";
                    } else if (doc_position.equals("住院医师")) {
                        doc_position = "4";
                    } else {
                        n = 100;
                    }

                    Cell goodCell = row.getCell(3);
                    String goodAt = "";
                    if (goodCell != null) {
                        goodAt = goodCell.getStringCellValue();
                    }


                    HmsCMansData data = new HmsCMansData();
                    if (n != 100) {
                        for (HmsCMansData hmsCMansData : hmsCMansDataList) {
                            String docName = hmsCMansData.getTitle();
                            String departIdStr = hmsCMansData.getDepart();
                            List<String> departs = new ArrayList<>();
                            if (departIdStr != null && !departIdStr.isEmpty()) {
                                List<Integer> departIds = new ArrayList<>();
                                if (departIdStr.contains(",")) {
                                    String[] split = departIdStr.split(",");
                                    for (int i = 0; i < split.length; i++) {
                                        departIds.add(Integer.parseInt(split[i]));
                                    }
                                } else {
                                    departIds.add(Integer.parseInt(departIdStr));
                                }
                                departs = hmsCategories.stream().filter(h -> departIds.contains(h.getId().intValue())).map(HmsCategory::getName).collect(Collectors.toList());
                            }
                            if (name.equals(docName) && departs.contains(departName)) {
                                n++;
                                //若是重名且科室也相同说明是同一个人，情况为撤稿了，但数据还存在，或者是他到其他栏目的方式不是投递而是复制
                                if (n > 2)
                                    x++;
                                hmsCMansData.setDocPosition(doc_position);
                                hmsCMansData.setGoodat(goodAt);
                                data = hmsCMansData;
                            }
                        }
                    }
                    if (n == 0 || n == 100) {
                        notFoundMap.put(name, departName);
                    } else {
                        //遇到两个相同的人(科室名字都相同，一般为同一个人)，此时将这两个数据都更改
                        if (x != 0) {
                            for (HmsCMansData hmsCMansData : hmsCMansDataList) {
                                if (hmsCMansData.getTitle().equals(data.getTitle())) {
                                    hmsCMansData.setDocPosition(data.getDocPosition());
                                    hmsCMansData.setGoodat(data.getGoodat());
                                    hmsCMansDataMapper.updateById(hmsCMansData);
                                }
                            }
                        } else {
                            hmsCMansDataMapper.updateById(data);
                        }
                    }
                }
            }

            try {
                // 创建工作簿
                Workbook workbook2 = new XSSFWorkbook();
                // 创建工作表
//                Sheet sheet2 = workbook2.createSheet("sheet1");
                Sheet sheet1 = workbook2.createSheet("111");

                int rowNum = 0;
//                for (Map.Entry<Double, String> entry : reMap.entrySet()) {
//                    Row row = sheet2.createRow(rowNum++);
//                    Cell keyCell = row.createCell(0);
//                    Cell valueCell = row.createCell(1);
//                    keyCell.setCellValue(entry.getKey());
//                    valueCell.setCellValue(entry.getValue());
//                }
                for (Map.Entry<String, String> entry : notFoundMap.entrySet()) {
                    Row row = sheet1.createRow(rowNum++);
                    Cell keyCell = row.createCell(0);
                    Cell valueCell = row.createCell(1);
                    keyCell.setCellValue(entry.getKey());
                    valueCell.setCellValue(entry.getValue());
                }

                // 将工作簿写入文件
                FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\未找到.xlsx");
                workbook2.write(outputStream);
                workbook2.close();
                outputStream.close();

                System.out.println("Excel 文件生成成功！");
//
//                RedisUtil.getInstance().set("成功的医生", JsonUtil.obj2String(map));
//                RedisUtil.getInstance().set("成功的数量", JsonUtil.obj2String(map.size()));
            } catch (Exception e) {
                e.printStackTrace();
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            DynamicDataSource.changeDefaultDataSource();
        }
    }

    @Test
    public void test1() {
        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\未找到.xlsx";
        Map<String, String> map = new HashMap<>();
        Map<String, String> map2 = new HashMap<>();

        DynamicDataSource.changeDynamicDataSource();

        HmsCMansDataMapper hmsCMansDataMapper = ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class);
        List<HmsCMansData> hmsCMansDataList = hmsCMansDataMapper.selectList(null);

        try (FileInputStream fis = new FileInputStream(new File(excelFilePath));
             Workbook workbook = WorkbookFactory.create(fis)) {

            // 获取第一个工作表（索引从0开始），你可以根据实际需要修改索引获取不同的工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历每一行，从第二行开始（索引为1，通常第一行是标题行，可根据实际情况调整）
            for (int rowIndex = 1; rowIndex < 133; rowIndex++) {
                int n = 0;
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    // 遍历每一列，获取单元格数据
                    Cell nameCell = row.getCell(0);
                    String name = "";
                    if (nameCell != null) {
                        name = nameCell.getStringCellValue();
                    }

                    Cell depaertCell = row.getCell(1);
                    String departName = "";
                    if (depaertCell != null) {
                        departName = depaertCell.getStringCellValue();
                    }

                    for (HmsCMansData hmsCMansData : hmsCMansDataList) {
                        if (hmsCMansData.getTitle().equals(name)) {
                            n++;
                        }
                    }
                    if (n == 0) {
                        map2.put(name, departName);
                    } else {
                        map.put(name, departName);
                    }
                }
            }

            try {
                // 创建工作簿
                Workbook workbook2 = new XSSFWorkbook();
                // 创建工作表
                Sheet sheet1 = workbook2.createSheet("科室不对");
                Sheet sheet2 = workbook2.createSheet("未找到");

                int rowNum = 0;
                for (Map.Entry<String , String> entry : map2.entrySet()) {
                    Row row = sheet2.createRow(rowNum++);
                    Cell keyCell = row.createCell(0);
                    Cell valueCell = row.createCell(1);
                    keyCell.setCellValue(entry.getKey());
                    valueCell.setCellValue(entry.getValue());
                }
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    Row row = sheet1.createRow(rowNum++);
                    Cell keyCell = row.createCell(0);
                    Cell valueCell = row.createCell(1);
                    keyCell.setCellValue(entry.getKey());
                    valueCell.setCellValue(entry.getValue());
                }

                // 将工作簿写入文件
                FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\未找到和科室不对.xlsx");
                workbook2.write(outputStream);
                workbook2.close();
                outputStream.close();

                System.out.println("Excel 文件生成成功！");
//
            } catch (Exception e) {
                e.printStackTrace();
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            DynamicDataSource.changeDefaultDataSource();
        }
    }
}
