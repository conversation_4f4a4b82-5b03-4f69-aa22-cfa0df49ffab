package com.ruifox.collect.officeHandle; // 包的声明

import com.ruifox.collect.common.constants.RedisConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper; // 导入 CollectTask 的 Mapper
import com.ruifox.collect.dao.mapper.TaskObjectMapper; // 导入 TaskObject 的 Mapper
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask; // 导入 CollectTask 实体类
import com.ruifox.collect.module.entity.TaskObject; // 导入 TaskObject 实体类
import com.ruifox.collect.system.ApplicationContextProvider; // 导入 ApplicationContextProvider
import com.ruifox.collect.util.JsonUtil; // 导入 Json 工具类
import com.ruifox.collect.util.RedisUtil;
import com.ruifox.collect.util.Sleeper;
import org.apache.commons.io.FileUtils; // 导入 FileUtils 用于文件操作
import org.apache.poi.hwpf.extractor.WordExtractor; // 导入 WordExtractor 用于读取 .doc 文件
import org.apache.poi.xwpf.extractor.XWPFWordExtractor; // 导入 XWPFWordExtractor 用于读取 .docx 文件
import org.apache.poi.xwpf.usermodel.XWPFDocument; // 导入 XWPFDocument 用于处理 .docx 文件
import org.junit.Test; // 导入 JUnit 测试注解
import org.junit.runner.RunWith; // 导入 JUnit 运行器
import org.springframework.boot.test.context.SpringBootTest; // 导入 Spring Boot 测试注解
import org.springframework.mock.web.MockMultipartFile; // 导入 MockMultipartFile 用于模拟文件上传
import org.springframework.test.context.junit4.SpringRunner; // 导入 Spring 运行器
import org.springframework.web.multipart.MultipartFile; // 导入 MultipartFile 接口

import java.io.File; // 导入 File 类用于文件操作
import java.io.IOException; // 导入 IOException 异常
import java.io.InputStream; // 导入 InputStream 类
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*; // 导入所有的集合类
import java.util.concurrent.TimeUnit;

@SpringBootTest // 指定这是一个 Spring Boot 测试
@RunWith(SpringRunner.class) // 指定使用 Spring 运行器
public class DocxToDoctorInfoTest { // 测试类声明

    @Test // 指定这是一个测试方法
    public void test() { // 测试方法开始
        // TODO: 从 docx 文件中解析医生的信息，并匹配医生的照片 (jpg 格式)，采集结果按医生照片前缀排序
        // NOTE: 这里每个文件夹下直接包含该科室的医生信息 (docx 文件)、医生照片 (jpg 格式)

        // 从数据库获取 CollectTask 对象，遍历 ID 从 354 到 354
        int collectTaskId = 472;
        // 获取 CollectTask 对象
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        // 获取与 CollectTask 相关的 TaskObject 对象
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());

        RedisGetManager.deleteRedisById(collectTaskId);

        // 指定文件夹路径，包含该科室的医生信息和照片
        String folderPath = "C:\\Users\\<USER>\\Desktop\\成都六医院照片";
        // 获取该文件夹中的所有 .docx .doc 和 .jpg 文件
        List<String> docxFiles = classifyDocxFiles(folderPath);
        List<String> jpgFiles = classifyJpgFiles(folderPath);
        // 定义临时列表，包含特定字段名称
        //List<String> tmpList = List.of("教务职称", "研究生导师", "专家级别", "院内职务", "疾病擅长", "简介");

        int index = 0;

        // 遍历每个 .docx 文件
        for (String filePath : docxFiles) {
            if (filePath.contains("东虹体检")) {
                System.out.println(1);
            }
            int x = 1;
            try {
                RedisGetManager.deleteRedisById(collectTaskId);

                // 将文件路径转换为 MockMultipartFile 以便后续处理
                MockMultipartFile multipartFile = convert(filePath);
                // 读取 Word 文档内容
                String s = readWord(multipartFile);
                // 创建用于存储解析结果的映射
                Map<String, String> map = new HashMap<>();

                // 获取数据表字段映射
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                // 初始化字段为空字符串
                dataTableFieldMap.forEach((key, value) -> {
                    if (!map.containsKey(key)) {
                        map.put(key, "");
                    }
                });

                index++;

                // 填充基本信息到映射中
                map.put("collect_task_id", String.valueOf(collectTask.getId())); // 采集任务 ID
                map.put("classification", collectTask.getImportCategoryName()); // 分类名称
                map.put("target_url", ""); // 目标 URL
                map.put("page_id", UUID.randomUUID().toString()); // 生成唯一页面 ID
                map.put("flag", "0"); // 标志位
                map.put("origin_code", ""); // 原始代码


                String[] infos = s.split("######");
                int sort = 0;
                String depart = "";
                for (String info : infos) {
                    if (sort == 0) {
                        depart = info.trim();
                        sort++;
                        continue;
                    }
                    String name = ""; // 存储医生姓名
                    if (info.contains("*")) {
                        name = info.substring(info.indexOf("*") + "*".length(), info.lastIndexOf("*"));

                        info = info.replace("*" + name + "*", "");

                        name = name.trim();
                    } else {
                        name = "*未找到";
                    }
                    map.put("title", name);
                    map.put("depart", depart);

                    String doc_position = "";
                    String hos_position = "";
                    if (info.contains("职称或职务")) {
                        doc_position = info.substring(info.indexOf("职称或职务") + "职称或职务".length() + 1);
                        if (!doc_position.contains("；")) {
                            doc_position = "符号；未找到";
                        } else {
                            doc_position = doc_position.substring(0, doc_position.indexOf("；"));

                            String delate = info.substring(info.indexOf("职称或职务"));
                            delate = delate.substring(0, delate.indexOf("；") + 1);
                            info = info.replace(delate, "");

                            String[] doc_position_spilts = doc_position.split(" ");
                            if (doc_position_spilts.length > 1) {
                                doc_position = doc_position_spilts[0];
                                String str = doc_position_spilts[1];
                                if (str.contains("科副主任")) {
                                    hos_position = "科副主任";
                                } else if (str.contains("科主任")) {
                                    hos_position = "科主任";
                                }
                                if (str.contains("副院长")) {
                                    hos_position = "副院长";
                                } else if (str.contains("院长")) {
                                    hos_position = "院长";
                                }
                            }
                        }
                    }
                    map.put("hos_position", hos_position);
                    map.put("doc_position", doc_position);

                    String goodat = "";
                    if (info.contains("专业擅长")) {
                        goodat = info.substring(info.indexOf("专业擅长") + "专业擅长".length() + 1);
                        if (goodat.contains("。")) {
                            goodat = goodat.substring(0, goodat.indexOf("。"));
                        } else {
                            goodat = "符号。没有";
                        }
                    }
                    map.put("goodat", goodat);

                    info = info.trim();
                    map.put("content", info);
                    map.put("sort", collectTask.getSort() + String.format("%4s", index).replace(" ", "0") + String.format("%4s", sort).replace(" ", "0"));

                    int repetion = 0;
                    // 头像与排序处理
                    for (String img : jpgFiles) {
                        if (img.contains(name)) {
                            repetion++;
                            String extension = img.substring(img.lastIndexOf("."));
                            LocalDateTime localDateTime = LocalDateTime.now();
                            String hour = String.format("%02d", localDateTime.getHour());
                            String minute = String.format("%02d", localDateTime.getMinute());
                            String second = String.format("%02d", localDateTime.getSecond());
                            String temp = String.format("%03d", x++);
                            String filename = hour + minute + second + temp;
                            String thumb = "C:/Collect_Data/www.cdlyy.com/20241503/" + filename + extension;
                            File sourceFile = new File(img);
                            File destinationFile = new File(thumb);
                            // 重命名文件并保存
                            if (Files.copy(sourceFile.toPath(), destinationFile.toPath()) != null) {
                                map.put("thumb", thumb);
                            }
                        }
                    }
                    if (repetion == 0) {
                        map.put("thumb", "未找到");
                    } else if (repetion > 1) {
                        map.put("thumb", "重复");
                    }
                    sort++;
                    RedisUtil.getInstance().lLeftPush(RedisConstant.DATA_ITEMS + collectTask.getId(), JsonUtil.obj2String(map));
                }
                if (infos.length - 1 <= 0) {
                    RedisUtil.getInstance().set(filePath, "为空");
                }
                Sleeper.sleep(1, TimeUnit.SECONDS);
                // 将数据推送到 Redis
                RedisUtil.getInstance().set(RedisConstant.TASK_OBJECT + collectTask.getId(), JsonUtil.obj2String(taskObject));
                // 持久化数据
                CrawlerManager.persistenceDatas(collectTask);

            } catch (IOException e) { // 捕获 IO 异常
                e.printStackTrace();
            }
        }
    }


    public static MockMultipartFile convert(String filePath) throws IOException {
        File file = new File(filePath);
        byte[] fileContent = FileUtils.readFileToByteArray(file);
        String fileName = file.getName();
        String contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        return new MockMultipartFile(fileName, fileName, contentType, fileContent);
    }

    /**
     * 读取word中的内容
     *
     * @param multipartFile
     * @return
     */
    public static String readWord(MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        String suff = fileName.substring(fileName.lastIndexOf(".") + 1);
        String message = "";
        XWPFWordExtractor extractor = null;
        WordExtractor ex = null;

        try (InputStream inputStream = multipartFile.getInputStream()) {
            // 后续处理逻辑，使用inputStream
            if (suff.equals("docx")) {
                XWPFDocument xdoc = new XWPFDocument(inputStream);
                extractor = new XWPFWordExtractor(xdoc);
                message = extractor.getText();
            } else if (suff.equals("doc")) {
                ex = new WordExtractor(inputStream);
                message = ex.getText();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 这里只需要关闭提取器相关资源即可，输入流由try-with-resources自动关闭
            if (extractor != null) {
                try {
                    extractor.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (ex != null) {
                try {
                    ex.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return message;
    }

    public static List<String> classifyDocxFiles(String folderPath) {
        List<String> docxFiles = new ArrayList<>();
        File folder = new File(folderPath);
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    classifyDocxFiles(file.getAbsolutePath()).forEach(docxFiles::add);
                }
                if (file.isFile() && file.getName().toLowerCase().endsWith(".docx") || file.isFile() && file.getName().toLowerCase().endsWith(".doc")) {
                    docxFiles.add(file.getAbsolutePath());
                }
            }
        }
        return docxFiles;
    }

    public static List<String> classifyJpgFiles(String folderPath) {
        List<String> jpgFiles = new ArrayList<>();
        File folder = new File(folderPath);
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    classifyJpgFiles(file.getAbsolutePath()).forEach(jpgFiles::add); // 递归处理子文件夹
                }
                if (file.isFile() && file.getName().toLowerCase().endsWith(".jpg")) {
                    jpgFiles.add(file.getAbsolutePath());
                }
            }
        }
        return jpgFiles;
    }
}


