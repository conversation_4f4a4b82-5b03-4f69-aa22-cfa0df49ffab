package com.ruifox.collect.ATest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansMapper;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.hms.HmsCMans;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class) // 指定使用SpringRunner来运行测试
@Slf4j
public class GetThumbFromDatabase {
    @Autowired
    HmsCMansMapper hmsCMansMapper;
    @Autowired
    HmsCMansDataMapper hmsCMansDataMapper;

    @Test
    public void getInfo() {
        DynamicDataSource.changeDynamicDataSource();
        LambdaQueryWrapper<HmsCMans> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(HmsCMans::getStatus, 99);
        List<HmsCMans> hmsCMans = hmsCMansMapper.selectList(wrapper1);
        List<Long> dataIds = new ArrayList<>();
        hmsCMans.stream().forEach(hmsCMan -> dataIds.add(hmsCMan.getDataid()));
        LambdaQueryWrapper<HmsCMansData> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.in(HmsCMansData::getDid, dataIds);
        List<HmsCMansData> hmsCMansDatas = hmsCMansDataMapper.selectList(wrapper2);
        DynamicDataSource.changeDefaultDataSource();

        String baseUrl = "https://www.gdjnh.com/mews/detail.aspx?mid=020201&id=1082";
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById("472");

        for (HmsCMansData hmsCMansData : hmsCMansDatas) {

            String url = hmsCMansData.getThumb();
            String name = hmsCMansData.getTitle();
            int i = 0;
            for (HmsCMansData hmsCMansData1 : hmsCMansDatas) {
                if (hmsCMansData1.getTitle().equals(name)) {
                    i++;
                }
            }
            if (i > 1) {
                String localPath = FileDataManager.downloadFromHttpUrl(url, baseUrl, "pageId", collectTask);
                String newPath = "C:\\Collect_Data\\www.qhuah.com\\111\\" + name + hmsCMansData.getDid() + ".jpg";
                moveFile(localPath, newPath);
            } else {
                String localPath = FileDataManager.downloadFromHttpUrl(url, baseUrl, "pageId", collectTask);
                String newPath = "C:\\Collect_Data\\www.qhuah.com\\20250690\\" + name+".jpg";
                renameFile(localPath, newPath);
            }
        }
    }

    public void moveFile(String sourcePath, String destinationPath) {
        File sourceFile = new File(sourcePath);

        // 目标文件路径（包括目标目录和新文件名）
        File destinationFile = new File(destinationPath);

        // 检查源文件是否存在
        if (sourceFile.exists()) {
            // 尝试移动文件
            boolean isMoved = sourceFile.renameTo(destinationFile);
            if (isMoved) {
                System.out.println("文件移动成功。");
            } else {
                System.out.println("文件移动失败。");
            }
        } else {
            System.out.println("源文件不存在。");
        }
    }

    public void renameFile(String oldPath, String newPath) {
        // 指定旧文件名和新文件名
        File oldFile = new File(oldPath);
        File newFile = new File(newPath);

        // 检查旧文件是否存在
        if (oldFile.exists()) {
            // 尝试重命名文件
            boolean isRenamed = oldFile.renameTo(newFile);
            if (isRenamed) {
                System.out.println("文件重命名成功。");
            } else {
                System.out.println("文件重命名失败。");
            }
        } else {
            System.out.println("旧文件不存在。");
        }
    }
}
