package com.ruifox.collect.ATest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansDataMapper;
import com.ruifox.collect.dao.mapper.hms.HmsCMansMapper;
import com.ruifox.collect.module.entity.hms.HmsCMans;
import com.ruifox.collect.module.entity.hms.HmsCMansData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.DynamicDataSource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ListDoctorInfoTest {
    @Test
    public void test() {
        try {
            DynamicDataSource.changeDynamicDataSource();
            HmsCMansDataMapper hmsCMansDataMapper = ApplicationContextProvider.getBeanByType(HmsCMansDataMapper.class);
            HmsCMansMapper hmsCMansMapper = ApplicationContextProvider.getBeanByType(HmsCMansMapper.class);
            //获取要排序的科室的id
            String theList = "352,602,598,465,457,453,449,445,441,437,432,133,131,129,127,125,123,594,424,420,416,412,408,404,400,396,384,380,376,372,368,364,360,356,292,275,352,348,344,340,336,319,332,328,324,320,312,308,304,300,296,288,284,279,258";
            String[] lists = theList.split(",");
            List<Integer> list = new ArrayList<>();
            for (String departId : lists) {
                list.add(Integer.valueOf(departId));
            }

            int index = 0;
            for (int id : list) {
                index++;
                LambdaQueryWrapper<HmsCMansData> lqw = new LambdaQueryWrapper<>();
                lqw.eq(HmsCMansData::getDepart, id);
                List<HmsCMansData> hmsCMansDataList = hmsCMansDataMapper.selectList(lqw);

                List<Integer> IdList = new ArrayList<>();

                for (HmsCMansData hmsCMansData : hmsCMansDataList) {
                    IdList.add(Math.toIntExact(hmsCMansData.getDid()));
                }

                if (!IdList.isEmpty()) {
                    LambdaQueryWrapper<HmsCMans> wrapper = new LambdaQueryWrapper<>();
                    wrapper.in(HmsCMans::getId, IdList)
                            .eq(HmsCMans::getCatid, 3)
                            .eq(HmsCMans::getStatus, 99);
                    List<HmsCMans> hmsCMansList = hmsCMansMapper.selectList(wrapper);
                    for (HmsCMans hmsCMans : hmsCMansList) {
                        Long listOrder = hmsCMans.getListorder();
                        //listOrder越大排在越前面
                        hmsCMans.setListorder(listOrder + index * 1000L);
                    }
                    hmsCMansMapper.updateById(hmsCMansList);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            DynamicDataSource.changeDefaultDataSource();
        }

    }
}
