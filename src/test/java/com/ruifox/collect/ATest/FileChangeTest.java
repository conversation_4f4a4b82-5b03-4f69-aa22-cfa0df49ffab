package com.ruifox.collect.ATest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruifox.collect.dao.mapper.NewsFileTableMapper;
import com.ruifox.collect.dao.mapper.NewsTableMapper;
import com.ruifox.collect.module.entity.news.NewsFileTable;
import com.ruifox.collect.module.entity.news.NewsTable;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SpringBootTest
@RunWith(SpringRunner.class) // 指定使用SpringRunner来运行测试
public class FileChangeTest {
    @Autowired
    NewsTableMapper newsTableMapper;
    @Autowired
    NewsFileTableMapper newsFileTableMapper;

    @Test
    public void testChangeFile() {
        List<NewsFileTable> newsFileTables = newsFileTableMapper.selectList(null);

        Map<String, Map<String, String>> map1 = new HashMap<>();
        Map<String, String> map2 = new HashMap<>();
        for (NewsFileTable newsFileTable : newsFileTables) {
            String originUrl = newsFileTable.getOriginUrl();
            if(originUrl.contains("https://www.xxzrmyy.com")) {
                originUrl = originUrl.replaceAll("https://www.xxzrmyy.com", "");
            }
            String localUrl = newsFileTable.getLocalUrl();
            map2.put(localUrl, originUrl);
            map1.put(newsFileTable.getPageId(), map2);
        }

        Set<String> strings = map1.keySet();
        for (String pageId : strings) {
            LambdaQueryWrapper<NewsTable> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NewsTable::getPageId, pageId);
            List<NewsTable> newsTables = newsTableMapper.selectList(wrapper);
            if (newsTables != null) {
                for(NewsTable newsTable:newsTables) {
                    String content = newsTable.getContent();
                    Map<String, String> stringStringMap = map1.get(pageId);
                    Set<String> localUrls = stringStringMap.keySet();
                    for (String localUrl : localUrls) {
                        String originUrl = stringStringMap.get(localUrl);
                        content = content.replace(originUrl, localUrl);
                    }
                    newsTable.setContent(content);
                    newsTableMapper.updateById(newsTable);
                }
            }
        }
    }

    @Test
    public void changeContent(){
        List<NewsTable> newsTables = newsTableMapper.selectList(null);
        for(NewsTable newsTable : newsTables){
            String content = newsTable.getContent();
            Document document = Jsoup.parse(content);
            if(document.select("tr").size() > 2){
                document.select("tr:lt(4)").remove();
            }
            content = document.toString();
            newsTable.setContent(content);
            newsTableMapper.updateById(newsTable);
        }
    }
}
