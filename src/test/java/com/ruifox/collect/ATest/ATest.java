package com.ruifox.collect.ATest;

import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.util.JsonUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ATest {

    @Test
    public void test1() {
        String a = "aaaa/3";
        System.out.println(a.substring(a.lastIndexOf("/")));

    }

    @Test
    public void test() {
        try {
            String folderPath = "C:\\Users\\<USER>\\Desktop\\新建文件夹";
            List<String> jpgFiles = classifyJpgFiles(folderPath);
            String localPath = "C:\\Users\\<USER>\\Desktop\\工作簿1.xlsx";
            writeListToExcel(jpgFiles, localPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void writeListToExcel(List<String> data, String filePath) {
        try (XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream outputStream = new FileOutputStream(filePath)) {

            XSSFSheet sheet = workbook.createSheet("DataSheet");

            int rowNum = 0;
            for (String item : data) {
                Row row = sheet.createRow(rowNum++);
                Cell cell = row.createCell(0);
                cell.setCellValue(item);
            }

            workbook.write(outputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static List<String> classifyJpgFiles(String folderPath) {
        List<String> jpgFiles = new ArrayList<>();
        File folder = new File(folderPath);
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile() && file.getName().toLowerCase().endsWith(".jpg")) {
                    jpgFiles.add(file.getAbsolutePath());
                }
            }
        }
        return jpgFiles;
    }


}


