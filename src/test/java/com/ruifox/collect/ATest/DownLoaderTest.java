package com.ruifox.collect.ATest;

import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.system.ApplicationContextProvider;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.junit4.SpringRunner;

import java.beans.Encoder;
import java.net.MalformedURLException;
import java.net.URL;

@SpringBootTest
@RunWith(SpringRunner.class)
public class DownLoaderTest {
    @Test
    public void test() {
        downloadFileFromUrl();
    }



    // TODO 测试从网络url下载文件
    private void downloadFileFromUrl() {


        String url = "http://cd3hospital.my120.org/oss/20201023/105023164.jpg";

        String baseUrl = "https://www.cd3hospital.com/";
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById("471");

        String localPath = FileDataManager.downloadFromHttpUrl(url, baseUrl, "pageId", collectTask);

        System.err.println(localPath);

    }

}
