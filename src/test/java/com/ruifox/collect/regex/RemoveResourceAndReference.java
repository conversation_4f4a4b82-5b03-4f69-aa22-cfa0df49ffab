package com.ruifox.collect.regex;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.dao.mapper.FolderResourceMapper;
import com.ruifox.collect.dao.mapper.station.StationMArticleMapper;
import com.ruifox.collect.dao.mapper.station.StationMDoctorMapper;
import com.ruifox.collect.dao.mapper.station.StationMImageMapper;
import com.ruifox.collect.dao.mapper.station.StationMTeacherMapper;
import com.ruifox.collect.dao.mapper.station2.*;
import com.ruifox.collect.dao.mapper.tbl.TblCManDataMxfyMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureDataMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCPictureMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCategoryMapper;
import com.ruifox.collect.dao.mapper.tbl2.*;
import com.ruifox.collect.module.entity.resource.FolderResource;
import com.ruifox.collect.module.entity.station2.StationMReference;
import com.ruifox.collect.system.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:删除导入成功的数据及其引用（适用于重新导入）PLUS
 * @author: WHL
 * @date: 2025/8/6 9:57
 * @param:
 * @return:
 **/

@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class RemoveResourceAndReference {
    @Autowired
    private StationMArticleDataMapper2 stationMArticleDataMapper;
    @Autowired
    private StationMReferenceMapper stationMReferenceMapper;
    @Autowired
    private FolderResourceMapper folderResourceMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;



    @Test
    public void removeNewsJava() {
        Integer catId = 356;
        //获取folder_resource的id列表
        List<Integer> folderIds = getFolderIds(catId);
        
        if (folderIds == null || folderIds.isEmpty()) {
            log.warn("catId: {} 没有找到相关数据，跳过删除", catId);
            return;
        }
        
        // 过滤掉null值
        folderIds = folderIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        
        //获得resource_data_article的id列表
        List<Integer> resourceIds = getResourceIds(folderIds);
        
        // 过滤掉null值
        if (resourceIds != null) {
            resourceIds = resourceIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        //开始删除
        //删除resource库中的两张表，开启事务
        //编程式事务
        List<Integer> finalResourceIds = resourceIds;
        List<Integer> finalFolderIds = folderIds;
        transactionTemplate.execute(status -> {
            DynamicDataSource.changeResourceDynamicDataSource();
            //删除resource_data_article
            if (finalResourceIds != null && !finalResourceIds.isEmpty()) {
                stationMArticleDataMapper.deleteByIds(finalResourceIds);
            }
            //删除folder_resource
            if (!finalFolderIds.isEmpty()) {
                folderResourceMapper.deleteByIds(finalFolderIds);
            }
            return true;
        });

        //删除station_m_reference
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaUpdateWrapper<StationMReference> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId);
        stationMReferenceMapper.delete(wrapper);

    }

    @Test
    public void testGetId(){
        Integer catId = 9999999;
        //获取folder_resource的id列表
        List<Integer> folderIds = getFolderIds(catId);
        //获得resource_data_article的id列表
        List<Integer> resourceIds = getResourceIds(folderIds);
        //获取reference的id列表
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId)
                .select(StationMReference::getId);
        List<Integer> references = stationMReferenceMapper.selectObjs(wrapper);

        System.out.println(folderIds.size());
        System.out.println(resourceIds.size());
        System.out.println(references.size());
    }

    /**
     * 批量删除多个catId的数据
     */
    @Test
    public void batchRemoveNewsJava() {
        // 要删除的catId列表，可以根据需要修改
        List<Integer> catIds = Arrays.asList(73,
                80,
                87,
                94,
                101,
                108,
                115,
                122,
                129,
                136,
                143,
                150,
                157,
                164,
                171,
                178,
                185,
                192,
                199,
                206,
                213,
                220,
                227,
                234,
                241,
                248,
                255,
                262,
                269,
                276,
                283,
                290,
                297,
                304,
                311,
                318,
                325,
                332,
                339,
                346,
                353,
                360,
                367,
                374,
                381,
                388,
                395);
        
        log.info("开始批量删除，catIds: {}", catIds);
        
        for (Integer catId : catIds) {
            try {
                log.info("正在删除catId: {}", catId);
                removeNewsJavaMethod(catId);
                log.info("成功删除catId: {}", catId);
            } catch (Exception e) {
                log.error("删除catId: {} 时发生错误", catId, e);
            }
        }
        
        log.info("批量删除完成");
    }

    /**
     * 删除单个catId的数据（从原方法提取出来的通用方法）
     * @param catId 要删除的分类ID
     */
    public void removeNewsJavaMethod(Integer catId) {
        //获取folder_resource的id列表
        List<Integer> folderIds = getFolderIds(catId);
        
        if (folderIds == null || folderIds.isEmpty()) {
            log.warn("catId: {} 没有找到相关数据，跳过删除", catId);
            return;
        }
        
        // 过滤掉null值
        folderIds = folderIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        
        //获得resource_data_article的id列表
        List<Integer> resourceIds = getResourceIds(folderIds);
        
        // 过滤掉null值
        if (resourceIds != null) {
            resourceIds = resourceIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        log.info("catId: {} 将删除 {} 个folder记录，{} 个resource记录", catId, folderIds.size(), resourceIds != null ? resourceIds.size() : 0);

        //开始删除
        //删除resource库中的两张表，开启事务
        //编程式事务
        List<Integer> finalResourceIds = resourceIds;
        List<Integer> finalFolderIds = folderIds;
        transactionTemplate.execute(status -> {
            DynamicDataSource.changeResourceDynamicDataSource();
            //删除resource_data_article
            if (finalResourceIds != null && !finalResourceIds.isEmpty()) {
                stationMArticleDataMapper.deleteByIds(finalResourceIds);
            }
            //删除folder_resource
            if (!finalFolderIds.isEmpty()) {
                folderResourceMapper.deleteByIds(finalFolderIds);
            }
            return true;
        });

        //删除station_m_reference
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaUpdateWrapper<StationMReference> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StationMReference::getCatId, catId);
        stationMReferenceMapper.delete(wrapper);
    }

    /**
     * 批量获取多个catId的统计信息
     */
    @Test
    public void batchTestGetId(){
        List<Integer> catIds = Arrays.asList(
                73,
                80,
                87,
                94,
                101,
                108,
                115,
                122,
                129,
                136,
                143,
                150,
                157,
                164,
                171,
                178,
                185,
                192,
                199,
                206,
                213,
                220,
                227,
                234,
                241,
                248,
                255,
                262,
                269,
                276,
                283,
                290,
                297,
                304,
                311,
                318,
                325,
                332,
                339,
                346,
                353,
                360,
                367,
                374,
                381,
                388,
                395);
        
        for (Integer catId : catIds) {
            log.info("=== catId: {} 的统计信息 ===", catId);
            //获取folder_resource的id列表
            List<Integer> folderIds = getFolderIds(catId);
            //获得resource_data_article的id列表
            List<Integer> resourceIds = new ArrayList<>();
            if (!folderIds.isEmpty()) {
                resourceIds = getResourceIds(folderIds);
            }
            //获取reference的id列表
            DynamicDataSource.changeBuildDynamicDataSource();
            LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StationMReference::getCatId,catId)
                    .select(StationMReference::getId);
            List<Integer> references = stationMReferenceMapper.selectObjs(wrapper);

            log.info("folderIds数量: {}", folderIds.size());
            log.info("resourceIds数量: {}", resourceIds.size());
            log.info("references数量: {}", references.size());
        }
    }


    public List<Integer>  getFolderIds(Integer catId){
        DynamicDataSource.changeBuildDynamicDataSource();
        LambdaQueryWrapper<StationMReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationMReference::getCatId,catId)
                .select(StationMReference::getDataId);
        return stationMReferenceMapper.selectObjs(wrapper);
    }

    public List<Integer>  getResourceIds(List<Integer> folderIds){
        DynamicDataSource.changeResourceDynamicDataSource();
        LambdaQueryWrapper<FolderResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FolderResource::getId,folderIds)
                .select(FolderResource::getResourceId);
        return folderResourceMapper.selectObjs(wrapper);
    }



}
