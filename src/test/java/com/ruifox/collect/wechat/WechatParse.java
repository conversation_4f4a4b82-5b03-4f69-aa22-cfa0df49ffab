package com.ruifox.collect.wechat;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruifox.collect.common.constants.NewsDataConstant;
import com.ruifox.collect.dao.mapper.CollectTaskMapper;
import com.ruifox.collect.dao.mapper.CommonMapper;
import com.ruifox.collect.dao.mapper.TaskObjectMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCNewsDataMapper;
import com.ruifox.collect.dao.mapper.tbl.TblCNewsMapper;
import com.ruifox.collect.manager.CrawlerManager;
import com.ruifox.collect.manager.FileDataManager;
import com.ruifox.collect.manager.RedisGetManager;
import com.ruifox.collect.module.entity.CollectTask;
import com.ruifox.collect.module.entity.TaskObject;
import com.ruifox.collect.module.entity.tbl.TblCNews;
import com.ruifox.collect.module.entity.tbl.TblCNewsData;
import com.ruifox.collect.system.ApplicationContextProvider;
import com.ruifox.collect.system.ConfigUtil;
import com.ruifox.collect.system.DynamicDataSource;
import com.ruifox.collect.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class WechatParse {

    @Test
    public void test() {
        String htmlFilePath = "C:/Users/<USER>/Desktop/横州市人民医院.html"; // 下载的html文件的路径
        int collectTaskId = 471; //所属采集任务的ID
        //限定的时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(2025, Calendar.MARCH, 1);
        Date targetDate = calendar.getTime();

        String pageSource = WechatUtil.getPageSourceFromFile(htmlFilePath);
        Document document = Jsoup.parse(pageSource);
        //根据class的名字来进行查找不用将空格处变为.
        Elements elements = document.getElementsByClass("weui_media_box appmsg js_appmsg js_wx_tap_highlight wx_tap_cell");
        int sum = elements.size();
        int r1 = 0;
        int r2 = 0;
        int num = 0;
        System.out.println("微信文章合计：" + sum);

        RemoteWebDriver driver;
        FirefoxOptions options = new FirefoxOptions();

        options.addPreference("permissions.default.image", 2);
        options.addArguments("--disable-gpu");
        options.addArguments("--disable-software-rasterizer");

        options.addArguments("--window-size=1920,1080");

        System.setProperty("webdriver.gecko.driver", ConfigUtil.getProperties("common.geckoDriver"));

        driver = new FirefoxDriver(options);

        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());
        CommonMapper commonMapper = ApplicationContextProvider.getBeanByType(CommonMapper.class);

        for (Element element : elements) {
            System.out.println("正在处理第 " + (++num) + " 个；已成功：" + r1 + "个，已失败：" + r2 + "个");
            String pageSourceTmp = "";
            try {
                String url = element.attr("hrefs");
                if (!url.startsWith("https")) {
                    url = url.replace("http", "https");
                }

                Map<String, String> dataItemMap = new HashMap<>();
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                dataTableFieldMap.forEach((key, value) -> {
                    if (!dataItemMap.containsKey(key)) {
                        dataItemMap.put(key, "");
                    }
                });
                dataItemMap.put(NewsDataConstant.COLLECT_TASK_ID, String.valueOf(collectTask.getId()));
                dataItemMap.put(NewsDataConstant.CLASSIFICATION, collectTask.getImportCategoryName());
                dataItemMap.put(NewsDataConstant.TARGET_URL, url);
                String page_id = UUID.randomUUID().toString();
                dataItemMap.put(NewsDataConstant.PAGE_ID, page_id);
                dataItemMap.put(NewsDataConstant.SORT, "00100001" + String.format("%4s", num).replace(" ", "0"));

                // TODO 标记是微信外链，即不解析正文，有时可能要解析正文，将其变为0-------------------------------------------------------------
                dataItemMap.put(NewsDataConstant.FLAG, "0");

                // FIXME 这里应该规范存放源码作为兜底操作，但微信文章的源码太多了，容易在批量落库时导致堆溢出异常，所以这里暂时为空
                dataItemMap.put(NewsDataConstant.ORIGIN_CODE, "");

                //获取封面内容
                String thumb = "";
                Elements selectThumb = element.select("span");
                String thumbStyle = selectThumb.attr("style");
                thumb = thumbStyle.substring(thumbStyle.indexOf("(") + 1, thumbStyle.indexOf(")"));
                thumb = FileDataManager.downloadFromHttpUrl(thumb, "https://mp.weixin.qq.com/", page_id, collectTask);
                dataItemMap.put(NewsDataConstant.THUMB, thumb);

                //获取时间信息
                String publish_time = "";
                Element publishElement = element.select("div > p.weui_media_extra_info").first();
                if (publishElement != null) {
                    publish_time = publishElement.text();
                    if (StringUtils.isNotBlank(publish_time)) {
                        Date date = DateUtils.parseDate(publish_time, "yyyy年MM月dd日");
                        if (date.before(targetDate))
                            break;
                        publish_time = String.valueOf(date.getTime() / 1000);
                    }
                }
                dataItemMap.put(NewsDataConstant.PUBLISH_TIME, publish_time);

                driver.get(url);
                Sleeper.sleep(3, TimeUnit.SECONDS);
                pageSourceTmp = driver.getPageSource();

                Document documentTmp = Jsoup.parse(pageSourceTmp);
                collectTask.setTargetUrl(url);
                Map<String, String> baseInfo = WechatUtil.getWechatBaseInfo(documentTmp, collectTask, page_id);
                dataItemMap.putAll(baseInfo);

                // FIXME 这里是单条数据插入，本来应该等所有的都解析完后再批量落库的
                commonMapper.insert(collectTask.getDataTableName(), dataItemMap);
                System.out.println("成功：" + (++r1) + "，失败：" + r2 + "，完成：" + num + "，总计：" + sum);
            } catch (Exception e) {
                System.out.println(e.getMessage());
                CrawlerManager.taskFailParseRecord(collectTaskId, "", e.getMessage(), pageSourceTmp);
                System.out.println("成功：" + r1 + "，失败：" + (++r2) + "，完成：" + num + "，总计：" + sum);
                driver.quit();
                break;
            }
        }
        // 数据落库，主要是下载失败信息
        CrawlerManager.persistenceDatas(collectTask);
        //删除Redis中的缓存
        RedisGetManager.deleteRedisById(collectTask.getId());
        driver.quit();
    }

    @Test
    public void testSq() {
        String hrefStr = "https://d.xiumi.us/board/v5/6oUnu/468913503,https://d.xiumi.us/board/v5/6oUnu/499075903,https://a.xiumi.us/board/v5/6oUnu/492426990,https://a.xiumi.us/board/v5/6oUnu/499066095,https://b.xiumi.us/board/v5/6oUnu/491265881,https://d.xiumi.us/board/v5/6oUnu/492457968,https://c.xiumi.us/board/v5/6oUnu/499526992,https://d.xiumi.us/board/v5/6oUnu/491285193,https://r.xiumi.us/board/v5/6oUnu/499513944,https://d.xiumi.us/board/v5/6oUnu/492446993,https://a.xiumi.us/board/v5/6oUnu/491573620,https://r.xiumi.us/board/v5/6oUnu/492438699,https://a.xiumi.us/board/v5/6oUnu/492413485,https://d.xiumi.us/board/v5/6oUnu/498719733,https://r.xiumi.us/board/v5/6oUnu/491290064,https://c.xiumi.us/board/v5/6oUnu/498731937,https://a.xiumi.us/board/v5/6oUnu/498743530,https://d.xiumi.us/board/v5/6oUnu/491227383,https://c.xiumi.us/board/v5/6oUnu/499041652,https://b.xiumi.us/board/v5/6omQt/472039316,https://r.xiumi.us/board/v5/6omQt/472061039,https://d.xiumi.us/board/v5/6omQt/486710233";
        String[] hrefs = hrefStr.split(",");

        int collectTaskId = 471;
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());
        CommonMapper commonMapper = ApplicationContextProvider.getBeanByType(CommonMapper.class);

        RemoteWebDriver driver;
        ChromeOptions options = new ChromeOptions();

        options.addArguments("--blink-settings=imagesEnabled=false");
        options.addArguments("--disable-gpu");
        options.addArguments("--disable-software-rasterizer");

        options.addArguments("--window-size=1920,1080");
        System.setProperty("webdriver.chrome.driver", ConfigUtil.getProperties("common.geckoDriver"));

        driver = new ChromeDriver(options);
        try {
            int num = 0;
            for (String href : hrefs) {
                Map<String, String> dataItemMap = new HashMap<>();
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                dataTableFieldMap.forEach((key, value) -> {
                    if (!dataItemMap.containsKey(key)) {
                        dataItemMap.put(key, "");
                    }
                });

                dataItemMap.put(NewsDataConstant.COLLECT_TASK_ID, String.valueOf(collectTask.getId()));
                dataItemMap.put(NewsDataConstant.CLASSIFICATION, collectTask.getImportCategoryName());
                dataItemMap.put(NewsDataConstant.TARGET_URL, href);
                String page_id = UUID.randomUUID().toString();
                dataItemMap.put(NewsDataConstant.PAGE_ID, UUID.randomUUID().toString());
                dataItemMap.put(NewsDataConstant.SORT, "00100001" + String.format("%4s", ++num).replace(" ", "0"));
                // TODO 标记是微信外链，即不解析正文，有时可能要解析正文，将其变为0
                dataItemMap.put(NewsDataConstant.FLAG, "0");
                // FIXME 这里应该规范存放源码作为兜底操作，但微信文章的源码太多了，容易在批量落库时导致堆溢出异常，所以这里暂时为空
                dataItemMap.put(NewsDataConstant.ORIGIN_CODE, "");

                driver.get(href);
                Sleeper.sleep(2, TimeUnit.SECONDS);
                Document document = Jsoup.parse(driver.getPageSource());

                collectTask.setTargetUrl(href);

                //获取标题内容
                String title = "";
                Element selectTitle = document.select("h3.ng-binding").first();
                String text = selectTitle.text();
                if (!text.isBlank()) {
                    title = text.trim();
                }
                dataItemMap.put(NewsDataConstant.TITLE, title);

                //获取时间信息
                String publish_time = "";
                Element publishElement = document.select("span.time.ng-binding").first();
                if (publishElement != null) {
                    publish_time = publishElement.text();
                    if (StringUtils.isNotBlank(publish_time)) {
                        Date date = DateUtils.parseDate(publish_time, "yyyy-MM-dd");
                        publish_time = String.valueOf(date.getTime() / 1000);
                    }
                }
                dataItemMap.put(NewsDataConstant.PUBLISH_TIME, publish_time);

                //获取正文
                Element contentElement = document.select("div.tn-board-body.ready").first();
                //下载并更换图片
                Elements imgElements = contentElement.select("img");
                for (Element imgElement : imgElements) {
                    String imgUrl = imgElement.attr("src");
                    imgUrl = "https:" + imgUrl;
                    String localPath = FileDataManager.downloadFromHttpUrl(imgUrl, collectTask.getTargetUrl(), page_id, collectTask);
                    if (localPath.isBlank()) {
                        localPath = "badPicture";
                    }
                    imgElement.attr("src", localPath);
                    //imgElement.attr("style", "vertical-align: middle; max-width: 100%; width: 50%;");
                }

                dataItemMap.put(NewsDataConstant.CONTENT, contentElement.toString());

                commonMapper.insert(collectTask.getDataTableName(), dataItemMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            driver.quit();
        }
    }

    @Test
    public void testWX() {
        String hrefStr = "https://mp.weixin.qq.com/s/5g9ZO_1yuAkJ8nK0oHzlkw,https://mp.weixin.qq.com/s/RD33ZjzZ9YtY6U8015L4KA?poc_token=HHzwOmejgIXpUPp0t09-wfsGVynM1t1geqpxo4l2,https://mp.weixin.qq.com/s/GIRUt0pa_Yenf-lAFWmT9w,https://mp.weixin.qq.com/s/NQBpngFl6zzy4gXBrYmKBw,https://mp.weixin.qq.com/s/DhsOJCqeKW0Mhi5juUgp2g,https://mp.weixin.qq.com/s/kgY58ZWDLeUqdcBjLnSq3w,https://mp.weixin.qq.com/s/74kw2iudp8EpA26U3L5Clw,https://mp.weixin.qq.com/s/uBB9AasnVXLUIGCmufsOHg,https://mp.weixin.qq.com/s/tb8SvufZhHKIsnNZJn1qhQ,https://mp.weixin.qq.com/s/-0hDjueA5lucdRfUsAMymQ,https://mp.weixin.qq.com/s/jSQumCR4aT5soc7MN5R-sA,https://mp.weixin.qq.com/s/HaV7uYuNhKK_9UyPM17MuA,https://mp.weixin.qq.com/s/6pOI9J8zpPUB3Z2SE8WS6w,https://mp.weixin.qq.com/s/z1JkcJGMOMULq6FqLzNzKg,https://mp.weixin.qq.com/s/kqenJP-mgl5LB5foUyLMww,https://mp.weixin.qq.com/s/0YnJL4n2i9J6f42YDr4reg,https://mp.weixin.qq.com/s/a6uLb52w5FNgTg-JjvkS8A,https://mp.weixin.qq.com/s/2E8-JeuMCQNRItZiBa-X0w,https://mp.weixin.qq.com/s/5WJUty8elWCiMS0RZPcvsA,https://mp.weixin.qq.com/s/glaGbUbrJOqR7LxVNfOiqQ,https://mp.weixin.qq.com/s/zZBz1kiH-8w38b9OM-93QQ,https://mp.weixin.qq.com/s/LI1oMpQ4yaSffPIoQdThCQ";
        String[] hrefs = hrefStr.split(",");

        int collectTaskId = 472;
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
        TaskObject taskObject = ApplicationContextProvider.getBeanByType(TaskObjectMapper.class).selectById(collectTask.getObjectId());
        CommonMapper commonMapper = ApplicationContextProvider.getBeanByType(CommonMapper.class);

        RemoteWebDriver driver;
        ChromeOptions options = new ChromeOptions();

        options.addArguments("--blink-settings=imagesEnabled=false");
        options.addArguments("--disable-gpu");
        options.addArguments("--disable-software-rasterizer");

        options.addArguments("--window-size=1920,1080");
        System.setProperty("webdriver.chrome.driver", ConfigUtil.getProperties("common.geckoDriver"));

        driver = new ChromeDriver(options);
        try {
            int num = 0;
            for (String href : hrefs) {
                Map<String, String> dataItemMap = new HashMap<>();
                Map<String, String> dataTableFieldMap = JsonUtil.Json2Obj(taskObject.getDataTableFields(), Map.class);
                dataTableFieldMap.forEach((key, value) -> {
                    if (!dataItemMap.containsKey(key)) {
                        dataItemMap.put(key, "");
                    }
                });

                dataItemMap.put(NewsDataConstant.COLLECT_TASK_ID, String.valueOf(collectTask.getId()));
                dataItemMap.put(NewsDataConstant.CLASSIFICATION, collectTask.getImportCategoryName());
                dataItemMap.put(NewsDataConstant.TARGET_URL, href);
                String page_id = UUID.randomUUID().toString();
                dataItemMap.put(NewsDataConstant.PAGE_ID, page_id);
                dataItemMap.put(NewsDataConstant.SORT, "00100001" + String.format("%4s", ++num).replace(" ", "0"));
                // TODO 标记是微信外链，即不解析正文，有时可能要解析正文，将其变为0
                dataItemMap.put(NewsDataConstant.FLAG, "0");
                // FIXME 这里应该规范存放源码作为兜底操作，但微信文章的源码太多了，容易在批量落库时导致堆溢出异常，所以这里暂时为空
                dataItemMap.put(NewsDataConstant.ORIGIN_CODE, "");

                driver.get(href);
                Sleeper.sleep(2, TimeUnit.SECONDS);
                Document document = Jsoup.parse(driver.getPageSource());

                collectTask.setTargetUrl(href);
                Map<String, String> baseInfo = WechatUtil.getWechatBaseInfo(document, collectTask, page_id);
                dataItemMap.putAll(baseInfo);

                commonMapper.insert(collectTask.getDataTableName(), dataItemMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            driver.quit();
        }
    }

    @Autowired
    private TblCNewsDataMapper tblCNewsDataMapper;
    @Autowired
    private TblCNewsMapper tblCNewsMapper;

//    @Test
//    public void GZSpecialWX() {
//        int collectTaskId = 471;
//        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);
//
//        //配置driver
//        RemoteWebDriver driver;
//        FirefoxOptions options = new FirefoxOptions();
//
//        options.addPreference("permissions.default.image", 2);
//        options.addArguments("--disable-gpu");
//        options.addArguments("--disable-software-rasterizer");
//
//        options.addArguments("--window-size=1920,1080");
//
//        System.setProperty("webdriver.gecko.driver", ConfigUtil.getProperties("common.geckoDriver"));
//
//        driver = new FirefoxDriver(options);
//
//        //切换到对应数据源
//        DynamicDataSource.changeDynamicDataSource();
//
//        List<Integer> errorLink = new ArrayList<>();
//
//        try {
//            LambdaQueryWrapper<TblCNews> tblCNewsLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            tblCNewsLambdaQueryWrapper.like(TblCNews::getIslink, "mp.weixin");
//            List<TblCNews> tblCNewsList = tblCNewsMapper.selectList(tblCNewsLambdaQueryWrapper);
//            Map<Integer, String> wxUrlsMap = new HashMap<>();
//            for (TblCNews tblCNews : tblCNewsList) {
//                String url = tblCNews.getIslink();
//                url = changeSpecialChat(url);
//                Map map = JSONObject.parseObject(url, Map.class);
//                wxUrlsMap.put(tblCNews.getDataid(), (String) map.get("url"));
//            }
//
//            Set<Integer> dataIds = wxUrlsMap.keySet();
//            for (Integer dataId : dataIds) {
//                String wxUrl = wxUrlsMap.get(dataId);
//                //将错误链接数据存储起来
//                if (!isLinkValid(wxUrl)) {
//                    errorLink.add(dataId);
//                    continue;
//                }
//                driver.get(wxUrl);
//                Sleeper.sleep(2, TimeUnit.SECONDS);
//                Document document = Jsoup.parse(driver.getPageSource());
//
//                String wechatBaseInfo = "";
//                try {
//                    wechatBaseInfo = WechatUtil.getWechatBaseInfo(document, wxUrl, UUID.randomUUID().toString(), collectTask);
//                } catch (Exception e) {
//                    log.error("", e);
//                    continue;
//                }
//                LambdaUpdateWrapper<TblCNewsData> tblCNewsDataLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
//                tblCNewsDataLambdaUpdateWrapper.eq(TblCNewsData::getId, dataId).set(TblCNewsData::getContent, wechatBaseInfo);
//                tblCNewsDataMapper.update(null, tblCNewsDataLambdaUpdateWrapper);
//
//                LambdaUpdateWrapper<TblCNews> wrapper2 = new LambdaUpdateWrapper<>();
//                wrapper2.eq(TblCNews::getDataid, dataId)
//                        .set(TblCNews::getIslink, "0")
//                        .set(TblCNews::getUrl, "");
//                tblCNewsMapper.update(null, wrapper2);
//            }
//
//
//            //将错误链接数据存入excel表中
//            if (!errorLink.isEmpty()) {
//                List<Map<String, String>> errorLinkList = new ArrayList<>();
//                for (Integer dataId : errorLink) {
//                    LambdaUpdateWrapper<TblCNewsData> tblCNewsDataLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
//                    tblCNewsDataLambdaUpdateWrapper.eq(TblCNewsData::getId, dataId);
//                    TblCNewsData tblCNewsData = tblCNewsDataMapper.selectOne(tblCNewsDataLambdaUpdateWrapper);
//                    Map<String, String> map = new HashMap<>();
//                    map.put("id", String.valueOf(tblCNewsData.getId()));
//                    map.put("title", tblCNewsData.getTitle());
//                    //获取其中的栏目Id
//                    map.put("catId", String.valueOf(
//                            tblCNewsList.stream()
//                                    .filter(m -> m.getDataid().equals(dataId))
//                                    .findFirst()
//                                    .map(TblCNews::getCatid)
//                                    .orElse(null)
//                    ));
//                    errorLinkList.add(map);
//                }
//                createExcel("C:\\Users\\<USER>\\Desktop\\失效的链接.xlsx", errorLinkList);
//            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            driver.quit();
//        }
//    }


    static String urls = "23557,22539,22540,22541,9742,16399,17935,22543,9748,17942,22551,17947,16924,16925,9761,23586,26658,17955,18467,17957,23589,13353,19501,17454,17457,17976,17981,9281,22089,9804,17998,10319,16463,22100,22102,17499,27228,18013,24165,24166,18023,17515,10351,10356,16500,16502,17015,11898,23162,23163,24700,17533,24701,16510,9859,18565,22151,22663,17544,20616,10892,10893,18061,22669,11924,16533,18581,19605,19606,9879,9882,16539,17054,22174,17055,16546,18596,18085,9900,23213,17070,23214,24750,23215,24751,17586,9907,18102,18110,22718,17088,19651,19652,17093,17094,17606,18630,19654,17099,22222,16592,12497,9938,19670,23256,24794,16603,16607,9441,17122,9955,16613,8936,9960,9965,17646,19694,16627,18166,23287,12536,8953,19706,9469,12546,22275,23811,16644,12549,12550,17159,18185,20745,22281,16654,10001,12562,18194,18197,16664,18712,18713,16667,9502,23327,24353,13090,24354,10020,23844,18213,17197,26927,18228,12086,16695,20792,12089,9531,18748,18237,9536,19779,19780,23877,18760,10057,12106,17741,10064,9557,10069,9558,11094,17750,18783,24928,17761,24929,10082,22887,22888,18794,9070,16750,19311,10096,17777,19315,26483,26484,24445,9094,9095,24967,17802,10129,23953,9108,24981,9110,16278,10140,10141,16286,24990,22943,22944,22946,11172,16806,18855,11176,9642,24492,18861,24493,24494,16303,24495,18864,10161,26546,18869,10168,11195,11196,16828,21948,16317,21951,23999,24000,10178,10179,16325,9670,10182,10184,18888,9163,19916,12241,26578,12243,23507,22485,12246,12247,10200,25050,21978,16347,20956,9694,16350,17374,18910,23518,23519,9699,23523,16868,9702,9703,16871,24039,24040,17897,17387,9709,11246,16367,19446,19447,9720,18938,18939,16893,16382";

    @Test
    public void GZSpecialWXPlus() {
        int collectTaskId = 471;
        CollectTask collectTask = ApplicationContextProvider.getBeanByType(CollectTaskMapper.class).selectById(collectTaskId);

        // 配置driver
        System.setProperty("webdriver.gecko.driver", ConfigUtil.getProperties("common.geckoDriver"));

        // 切换到对应数据源
        DynamicDataSource.changeDynamicDataSource();

        Map<Integer, String> errorMap = Collections.synchronizedMap(new HashMap<>());

        String[] split = urls.split(",");
        List<Integer> ids = Arrays.stream(split).map(m -> Integer.parseInt(m)).collect(Collectors.toList());

        try {
            LambdaQueryWrapper<TblCNews> tblCNewsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tblCNewsLambdaQueryWrapper.like(TblCNews::getIslink, "mp.weixin")
                    .in(TblCNews::getDataid, ids);
            List<TblCNews> tblCNewsList = tblCNewsMapper.selectList(tblCNewsLambdaQueryWrapper);
            Map<Integer, String> wxUrlsMap = new HashMap<>();

            // 处理数据并构建URL映射
            for (TblCNews tblCNews : tblCNewsList) {
                String url = tblCNews.getIslink();
                url = changeSpecialChat(url);
                Map map = JSONObject.parseObject(url, Map.class);
                wxUrlsMap.put(tblCNews.getDataid(), (String) map.get("url"));
                errorMap.put(tblCNews.getId(), tblCNews.getDataid() + "," + url + "：公众号内容出错");
            }

            /*Set<Integer> dataIds = wxUrlsMap.keySet();
            if (dataIds.isEmpty()) {
                log.info("没有需要处理的数据ID");
                return;
            }

            // 将数据ID分成5份
            List<List<Integer>> partitionedDataIds = partitionDataIds(new ArrayList<>(dataIds), 4);

            // 创建5个线程处理
            ExecutorService executorService = Executors.newFixedThreadPool(4);
            List<Future<?>> futures = new ArrayList<>();

            // 为每个数据块创建一个线程
            for (List<Integer> partition : partitionedDataIds) {
                futures.add(executorService.submit(() -> processPartition(partition, wxUrlsMap, collectTask, errorMap)));
            }

            // 等待所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (Exception e) {
                    log.error("线程执行异常", e);
                }
            }

            executorService.shutdown();*/

            // 将错误链接数据存入excel表中
            if (!errorMap.isEmpty()) {
                List<Map<String, String>> errorLinkList = new ArrayList<>();
                for (Integer id : errorMap.keySet()) {
                    String s = errorMap.get(id);
                    String[] split1 = s.split(",");
                    Integer dataId = Integer.parseInt(split1[0]);
                    LambdaUpdateWrapper<TblCNewsData> tblCNewsDataLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                    tblCNewsDataLambdaUpdateWrapper.eq(TblCNewsData::getId, dataId);
                    TblCNewsData tblCNewsData = tblCNewsDataMapper.selectOne(tblCNewsDataLambdaUpdateWrapper);
                    Map<String, String> map = new HashMap<>();
                    map.put("id", id.toString());
                    map.put("dataId", dataId.toString());
                    map.put("title", tblCNewsData.getTitle());
                    // 获取其中的栏目Id
                    map.put("catId", String.valueOf(
                            tblCNewsList.stream()
                                    .filter(m -> m.getDataid().equals(dataId))
                                    .findFirst()
                                    .map(TblCNews::getCatid)
                                    .orElse(null)
                    ));
                    map.put("reason", split1[1]);
                    errorLinkList.add(map);
                }
                createExcel("C:\\Users\\<USER>\\Desktop\\错误的微信链接.xlsx", errorLinkList);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 将数据ID分成指定数量的块
     */
    private <T> List<List<T>> partitionDataIds(List<T> data, int numPartitions) {
        List<List<T>> result = new ArrayList<>();
        int partitionSize = (data.size() + numPartitions - 1) / numPartitions;

        for (int i = 0; i < numPartitions; i++) {
            int start = i * partitionSize;
            int end = Math.min(start + partitionSize, data.size());
            result.add(data.subList(start, end));
        }

        return result;
    }

    /**
     * 处理一个数据块
     */
    private void processPartition(List<Integer> dataIds, Map<Integer, String> wxUrlsMap,
                                  CollectTask collectTask, Map<Integer, String> errorMap) {
        // 每个线程创建独立的driver
        FirefoxOptions options = new FirefoxOptions();
        options.addPreference("permissions.default.image", 2);
        options.addArguments("--disable-gpu");
        options.addArguments("--disable-software-rasterizer");
        options.addArguments("--window-size=1920,1080");

        // 切换到对应数据源
        DynamicDataSource.changeDynamicDataSource();
        RemoteWebDriver driver = new FirefoxDriver(options);
        try {
            for (Integer dataId : dataIds) {
                String wxUrl = wxUrlsMap.get(dataId);
                // 将错误链接数据存储起来
                if (!isLinkValid(wxUrl)) {
                    errorMap.put(dataId, wxUrl + "：链接失效");
                    continue;
                }
                driver.get(wxUrl);
                Sleeper.sleep(2, TimeUnit.SECONDS);
                String pageSource = driver.getPageSource();
                if (pageSource == null) {
                    errorMap.put(dataId, wxUrl + "：pageSource解析失败");
                    continue;
                }
                Document document = Jsoup.parse(pageSource);

                String wechatBaseInfo = "";
                try {
                    wechatBaseInfo = WechatUtil.getWechatBaseInfo(document, wxUrl, UUID.randomUUID().toString(), collectTask);
                    if (Objects.equals(wechatBaseInfo, "公众号内容出错")) {
                        errorMap.put(dataId, wxUrl + "：公众号内容出错");
                        continue;
                    }
                } catch (Exception e) {
                    log.error("该任务出错，跳过", e);
                    continue;
                }
                LambdaUpdateWrapper<TblCNewsData> tblCNewsDataLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                tblCNewsDataLambdaUpdateWrapper.eq(TblCNewsData::getId, dataId).set(TblCNewsData::getContent, wechatBaseInfo);
                try {
                    tblCNewsDataMapper.update(null, tblCNewsDataLambdaUpdateWrapper);
                } catch (Exception e) {
                    log.error("该任务出错，跳过", e);
                    continue;
                }

                LambdaUpdateWrapper<TblCNews> wrapper2 = new LambdaUpdateWrapper<>();
                wrapper2.eq(TblCNews::getDataid, dataId)
                        .set(TblCNews::getIslink, "0")
                        .set(TblCNews::getUrl, "");
                tblCNewsMapper.update(null, wrapper2);
            }
        } catch (Exception e) {
            log.error("处理数据块时发生异常", e);
        } finally {
            driver.quit();
        }
    }

    //判断链接是否有效
    public boolean isLinkValid(String link) {
        try {
            // 创建 URL 对象
            URL url = new URL(link);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为 HEAD，仅获取响应头信息，不获取响应体
            connection.setRequestMethod("HEAD");
            // 获取响应状态码
            int responseCode = connection.getResponseCode();
            // 判断状态码是否在 200 - 399 范围内
            return (responseCode >= 200 && responseCode < 400);
        } catch (IOException e) {
            // 发生异常，链接无效
            return false;
        }
    }

    //切换特殊字符
    public String changeSpecialChat(String text) {
        return text.replace("&#300", ";").replace("&#301", "'")
                .replace("&#302", "\"").replace("&#303", "(")
                .replace("&#304", "=").replace("&#305", "<")
                .replace("&#306", "");
    }


    //写入excel文件中
    public void createExcel(String saveSpace, List<Map<String, String>> listMap) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建工作表
            Sheet sheet = workbook.createSheet("错误的微信链接");

            int rowNum = 0;
            for (Map<String, String> map : listMap) {
                Row row = sheet.createRow(rowNum++);
                Cell IdCell = row.createCell(0);
                Cell DataIdCell = row.createCell(1);
                Cell titleCell = row.createCell(2);
                Cell CatIdCell = row.createCell(3);
                Cell ReasonCell = row.createCell(4);
                if (rowNum == 1) {
                    IdCell.setCellValue("id");
                    DataIdCell.setCellValue("dataId(资源id)");
                    titleCell.setCellValue("文章名称");
                    CatIdCell.setCellValue("栏目ID");
                    CatIdCell.setCellValue("地址和出错原因");
                } else {
                    IdCell.setCellValue(map.get("id"));
                    DataIdCell.setCellValue(map.get("dataId"));
                    titleCell.setCellValue(map.get("title"));
                    CatIdCell.setCellValue(map.get("catId"));
                    ReasonCell.setCellValue(map.get("reason"));
                }
            }

            // 将工作簿写入文件
            FileOutputStream outputStream = new FileOutputStream(saveSpace);
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();

            System.out.println("Excel 文件生成成功！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void screenLink() {
        LambdaQueryWrapper<TblCNews> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(TblCNews::getIslink, "0")
                .notLike(TblCNews::getIslink, "gzsrmyy")
                .like(TblCNews::getIslink, "enable&#302:&#3021&");
        //所有外链
        List<TblCNews> tblCNewsList = tblCNewsMapper.selectList(wrapper);
        Map<TblCNews, TblCNewsData> availableLinkMap = new HashMap<>();
        Map<TblCNews, TblCNewsData> disabledLinkMap = new HashMap<>();
        for (TblCNews tblCNews : tblCNewsList) {
            String url = tblCNews.getUrl();
            if(url.equals("1")){
                wrapper.clear();
                wrapper.eq(TblCNews::getDataid, tblCNews.getDataid());
                List<TblCNews> tblCNews1 = tblCNewsMapper.selectList(wrapper);
                List<String> collect = tblCNews1.stream().filter(m -> !m.getIslink().equals("1")).map(TblCNews::getUrl).collect(Collectors.toList());
                url = collect.get(0);
                tblCNews.setUrl(url);
            }
            LambdaQueryWrapper<TblCNewsData> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TblCNewsData::getId, tblCNews.getDataid());
            TblCNewsData tblCNewsData = tblCNewsDataMapper.selectOne(wrapper1);
            if(isLinkValid(url)) {
                availableLinkMap.put(tblCNews, tblCNewsData);
            }
            else{
                disabledLinkMap.put(tblCNews, tblCNewsData);
            }
        }

        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建工作表
            Sheet sheet = workbook.createSheet("能正常访问的链接");
            Sheet sheet2 = workbook.createSheet("不能正常访问的链接");

            int rowNum = 0;
            for (TblCNews tblCNews : availableLinkMap.keySet()) {
                Row row = sheet.createRow(rowNum++);
                Cell IdCell = row.createCell(0);
                Cell titleCell = row.createCell(1);
                Cell urlCell = row.createCell(2);
                if (rowNum == 1) {
                    IdCell.setCellValue("id");
                    titleCell.setCellValue("文章名称");
                    urlCell.setCellValue("url(地址)");
                } else {
                    IdCell.setCellValue(tblCNews.getId().toString());
                    titleCell.setCellValue(availableLinkMap.get(tblCNews).getTitle());
                    urlCell.setCellValue(tblCNews.getUrl());
                }
            }

            for (TblCNews tblCNews : disabledLinkMap.keySet()) {
                Row row = sheet2.createRow(rowNum++);
                Cell IdCell = row.createCell(0);
                Cell titleCell = row.createCell(1);
                Cell urlCell = row.createCell(2);
                if (rowNum == 1) {
                    IdCell.setCellValue("id");
                    titleCell.setCellValue("文章名称");
                    urlCell.setCellValue("url(地址)");
                } else {
                    IdCell.setCellValue(tblCNews.getId().toString());
                    titleCell.setCellValue(disabledLinkMap.get(tblCNews).getTitle());
                    urlCell.setCellValue(tblCNews.getUrl());
                }
            }

            // 将工作簿写入文件
            FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\所有的链接.xlsx");
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();

            System.out.println("Excel 文件生成成功！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
